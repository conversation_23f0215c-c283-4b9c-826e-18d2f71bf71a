import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { catchError, Observable, of } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { LoginService } from './auth/login.service';

@Injectable({
  providedIn: 'root'
})
export class StatusServiceService {

  baseURL:string;


  constructor(private http: HttpClient, private authService: LoginService) {
    this.baseURL= environment.STATUS_LIST;
  }

  getPendingList(): Observable<any> {
    const headers = this.getAuthHeaders();
    return this.http.get<any>(`${this.baseURL}/region/pending`, { headers });
  }

  getFirstLevelList(): Observable<any> {
    const headers = this.getAuthHeaders();
    return this.http.get<any>(`${this.baseURL}/region/approvedFromSuperandAM`, { headers });
  }

  getSecondLevelList(): Observable<any> {
    const headers = this.getAuthHeaders();
    return this.http.get<any>(`${this.baseURL}/region/approvedDrmAndMa`, { headers });
  }

  getApprovedList(): Observable<any> {
    const headers = this.getAuthHeaders();
    return this.http.get<any>(`${this.baseURL}/region/approvedFromSuperandAM`, { headers });
  }

  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken;
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // updateStatus(empId: any, status: any): Observable<any> {
  //   const headers = this.getAuthHeaders();
  //   return this.http.put<any>(`${this.baseURL}/status/${empId}`, { status }, { headers });
  // }

  updateStatus(empId: any, statusData: any): Observable<any> {
    const headers = this.getAuthHeaders();
    console.log('Update Status Payload:', statusData); // Log payload for debugging
    console.log('API URL:', `${this.baseURL}/status/${empId}`);

    return this.http.put<any>(`${this.baseURL}/status/${empId}`, statusData, {
      headers,
      observe: 'response' // This will give us the full HTTP response
    }).pipe(
      catchError((error) => {
        console.error('Error updating status:', error);
        console.error('Error status:', error.status);
        console.error('Error headers:', error.headers);
        console.error('Error body:', error.error);

        // If it's a 200 response but caught as error, it might be a parsing issue
        if (error.status === 200) {
          console.log('200 response caught as error - likely a parsing issue');
          // Return a successful response object
          return of({
            status: 200,
            body: { success: true, message: 'Status updated successfully' },
            statusText: 'OK'
          });
        }

        throw error;
      })
    );
  }
}
