<div class="form-container">
  <h2>Create User</h2>
  <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()" novalidate>
    
    <div class="row mb-3">
      <div class="col-md-6">
        <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="username"
               formControlName="username"
               [class.is-invalid]="isFieldInvalid('username')"
               placeholder="Enter username">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('username')">
          <small *ngIf="registrationForm.get('username')?.errors?.['required']">Username is required</small>
          <small *ngIf="registrationForm.get('username')?.errors?.['minlength']">Minimum 3 characters</small>
        </div>
      </div>
      <div class="col-md-6">
        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="name"
               formControlName="name"
               [class.is-invalid]="isFieldInvalid('name')"
               placeholder="Enter full name">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('name')">
          <small *ngIf="registrationForm.get('name')?.errors?.['required']">Name is required</small>
          <small *ngIf="registrationForm.get('name')?.errors?.['minlength']">Minimum 3 characters</small>
        </div>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-6">
        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
        <input type="email" class="form-control" id="email"
               formControlName="email"
               [class.is-invalid]="isFieldInvalid('email')"
               placeholder="Enter email">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('email')">
          <small *ngIf="registrationForm.get('email')?.errors?.['required']">Email is required</small>
          <small *ngIf="registrationForm.get('email')?.errors?.['email']">Invalid email address</small>
        </div>
      </div>

      <div class="col-md-6">
        <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
        <select class="form-select" id="role"
                formControlName="roleId"
                [class.is-invalid]="isFieldInvalid('roleId')">
          <option value="">Select Role</option>
          <option *ngFor="let role of roleList" [value]="role.id">{{ role.role }}</option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('roleId')">
          <small>Role is required</small>
        </div>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-6">
        <label for="mobile" class="form-label">Mobile <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="mobile"
               formControlName="mobile"
               [class.is-invalid]="isFieldInvalid('mobile')"
               placeholder="Enter 10-digit mobile">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('mobile')">
          <small *ngIf="registrationForm.get('mobile')?.errors?.['required']">Mobile is required</small>
          <small *ngIf="registrationForm.get('mobile')?.errors?.['pattern']">Enter a valid 10-digit number</small>
        </div>
      </div>
      <div class="col-md-6" >
        <label for="region" class="form-label">Region <span class="text-danger">*</span></label>
        <select class="form-select" id="region"
                formControlName="region"
                >
          <option value="">Select Region</option>
          <option *ngFor="let region of regionList" [value]="region">{{ region }}</option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('region')">
          <small>Region is required</small>
        </div>
      </div>
      
    </div>
    

    <div class="row mb-3">
      <div class="col-md-6">
        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
        <div class="input-group">
          <input [type]="showPassword ? 'text' : 'password'" class="form-control" id="password"
                 formControlName="password"
                 [class.is-invalid]="isFieldInvalid('password')"
                 placeholder="Enter password">
          <button type="button" class="btn btn-outline-secondary" (click)="togglePasswordVisibility()">
            <i class="bi bi-eye" [class.bi-eye]="!showPassword" [class.bi-eye-slash]="showPassword"></i>
          </button>
        </div>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('password')">
          <small>Password is required, 8+ chars, uppercase, lowercase, number, special</small>
        </div>
      </div>

      <div class="col-md-6">
        <label for="confirmPassword" class="form-label">Confirm Password <span class="text-danger">*</span></label>
        <div class="input-group">
          <input [type]="showConfirmPassword ? 'text' : 'password'" class="form-control" id="confirmPassword"
                 formControlName="confirmPassword"
                 [class.is-invalid]="isFieldInvalid('confirmPassword') || registrationForm.errors?.['passwordMismatch']"
                 placeholder="Confirm password">
          <button type="button" class="btn btn-outline-secondary" (click)="toggleConfirmPasswordVisibility()">
            <i class="bi bi-eye" [class.bi-eye]="!showConfirmPassword" [class.bi-eye-slash]="showConfirmPassword"></i>
          </button>
        </div>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('confirmPassword') || registrationForm.errors?.['passwordMismatch']">
          <small *ngIf="registrationForm.get('confirmPassword')?.errors?.['required']">Confirmation required</small>
          <small *ngIf="registrationForm.errors?.['passwordMismatch']">Passwords do not match</small>
        </div>
      </div>
    </div>

    <div class="d-flex justify-content-center mt-4">
      <button class="btn btn-primary px-4" type="submit"
              >
        <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2"></span>
        {{ isSubmitting ? 'Submitting...' : 'Submit' }}
      </button>
    </div>

    <div class="mt-3" *ngIf="submitMessage">
      <div class="alert" [class.alert-success]="submitSuccess" [class.alert-danger]="!submitSuccess">
        {{ submitMessage }}
      </div>
    </div>
  </form>
</div>
