<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Bootstrap Header</title>

  <!-- Bootstrap CSS -->
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

  <style>

  </style>
</head>
<body>

  <header class="header-component">
    <div class="hederlogo d-flex align-items-center">
      <!-- <button type="button" class="btn-menu mr-2" onclick="toggleSideNav()" style="background-color: #203664;">
        <i class="bi bi-list"></i>
      </button> -->
  <img src="logo_tncsc.png" alt="Logo" />
    </div>

    <div class="profile d-flex align-items-center">
      <!-- <div class="custom-toggle-group btn-group mr-3">
        <button type="button" class="btn btn-sm btn-outline-light active">ENG</button>
        <button type="button" class="btn btn-sm btn-outline-light">தமிழ்</button>
      </div>
 -->
      <span class="profileName d-none d-sm-inline" *ngIf="isEmployeeLogin">Employee: {{userName}}</span>
      <span class="profileName d-none d-sm-inline" *ngIf="!isEmployeeLogin">Username: {{userName}}</span>

      <div class="dropdown">
        <button class="btn" type="button" (click)="logout()" id="userDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          LOGOUT
        </button>
        <!-- <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
          <a class="dropdown-item" href="#">
            <span class="material-icons mr-1">account_circle</span> My Profile
          </a>
          <a class="dropdown-item" href="#" onclick="logout()">
            <span class="material-icons mr-1">logout</span> Logout
          </a>
        </div> -->
      </div>
    </div>
  </header>

  <!-- Bootstrap JS -->
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

  <script>
    function toggleSideNav() {
      alert('Toggle side nav!');
    }
    function signOut() {
      alert('Signed out!');
    }
  </script>
</body>
</html>

