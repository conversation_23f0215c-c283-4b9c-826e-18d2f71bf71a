<div class="form-container">
  <h2>Create Role</h2>
  <form [formGroup]="roleForm" (ngSubmit)="onSubmit()">
    <div class="form-group">
      <label for="role">Role</label>
      <input
        id="role"
        type="text"
        formControlName="role"
        placeholder="Enter role"
      />
      <div class="error" *ngIf="roleForm.get('role')?.touched && roleForm.get('role')?.invalid">
        Role is required.
      </div>
    </div>

    <div class="button-container">
      <button type="submit" [disabled]="roleForm.invalid">Submit</button>
    </div>
  </form>
</div>
