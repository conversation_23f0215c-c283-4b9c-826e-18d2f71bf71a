import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'translate',
  standalone: true
})
export class TranslatePipe implements PipeTransform {

  private translations: { [key: string]: string } = {
    'field.currentDesignation': 'Current Designation',
    'placeholder.enterDesignation': 'Enter designation',
    'dashboard.familyInfo': 'Family Information',
    'field.fatherName': 'Father Name',
    'field.motherName': 'Mother Name',
    'field.mobileNumber': 'Mobile Number',
    'field.email': 'Email',
    'dashboard.personalIdentification': 'Personal Identification',
    'field.personalIdMark1': 'Personal ID Mark 1',
    'field.personalIdMark2': 'Personal ID Mark 2',
    'field.registerNo': 'Register Number',
    'dashboard.religiousSocialInfo': 'Religious & Social Information',
    'field.religion': 'Religion',
    'option.selectReligion': 'Select Religion',
    'option.hinduism': 'Hinduism',
    'option.christianity': 'Christianity',
    'option.islam': 'Islam',
    'option.sikhism': 'Sikhism',
    'option.buddhism': 'Buddhism',
    'option.other': 'Other',
    'field.community': 'Community',
    'placeholder.selectCommunity': 'Select Community',
    'field.caste': 'Caste',
    'field.section': 'Section',
    'placeholder.enterSection': 'Enter section',
    'field.nativeDistrict': 'Native District',
    'option.selectDistrict': 'Select District',
    'dashboard.addressInfo': 'Address Information',
    'dashboard.documents': 'Documents',
    'field.profilePhoto': 'Profile Photo',
    'dashboard.locationServiceInfo': 'Location & Service Information',
    'field.dateOfEntry': 'Date of Entry',
    'option.selectSection': 'Select Section',
    'field.district': 'District',
    'field.nativeAndTaluk': 'Native Place and Taluk',
    'dashboard.documentUpload': 'Document Upload',
    'dashboard.uploadDocuments': 'Upload Documents',
    'field.documents': 'Documents',
    'dashboard.uploadInstructions': 'Upload Instructions',
    'instruction.supportedFormats': 'Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG',
    'instruction.maxFileSize': 'Maximum file size: 10MB per file',
    'instruction.multipleFiles': 'Multiple files can be uploaded',
    'instruction.requiredDocuments': 'Please upload all required documents',
    'dashboard.accountDetails': 'Account Details',
    'field.bankAccountNo': 'Bank Account Number',
    'field.ifscCode': 'IFSC Code',
    'field.bankName': 'Bank Name',
    'field.panNumber': 'PAN Number',
    'field.uanNumber': 'UAN Number',
    'field.aadharNumber': 'Aadhar Number',
    'dashboard.educationQualification': 'Education Qualification',
    'dashboard.serviceRecords': 'Service Records',
    'field.employeeType': 'Employee Type',
    'option.selectEmployeeType': 'Select Employee Type',
    'option.permanent': 'Permanent',
    'option.seasonal': 'Seasonal',
    'dashboard.serviceHistory': 'Service History',
    'dashboard.trainingHistory': 'Training History',
    'field.trainingType': 'Training Type',
    'placeholder.enterTrainingType': 'Enter training type',
    'field.trainingDate': 'Training Date',
    'dashboard.nominationDetails': 'Nomination Details',
    'field.nomineeName': 'Nominee Name',
    'field.address': 'Address',
    'field.relationship': 'Relationship',
    'option.selectRelationship': 'Select Relationship',
    'option.spouse': 'Spouse',
    'option.son': 'Son',
    'option.daughter': 'Daughter',
    'option.father': 'Father',
    'option.mother': 'Mother',
    'option.brother': 'Brother',
    'option.sister': 'Sister',
    'field.age': 'Age',
    'field.percentageShare': 'Percentage Share',
    'field.gender': 'Gender',
    'option.selectGender': 'Select Gender',
    'option.male': 'Male',
    'option.female': 'Female',
    'dashboard.leaveBalance': 'Leave Balance',
    'field.leaveType': 'Leave Type',
    'option.selectLeaveType': 'Select Leave Type',
    'option.earnedLeave': 'Earned Leave',
    'option.casualLeave': 'Casual Leave',
    'option.maternityLeave': 'Maternity Leave',
    'option.hospitalLeave': 'Hospital Leave',
    'option.specialDisabilityLeave': 'Special Disability Leave',
    'option.extraordinaryLeave': 'Extraordinary Leave',
    'option.specialCasualLeave': 'Special Casual Leave',
    'option.restrictedHolidays': 'Restricted Holidays',
    'field.openingBalance': 'Opening Balance',
    'field.closingBalance': 'Closing Balance',
    'field.entryDate': 'Entry Date',
    'button.update': 'Update',
    'button.save': 'Save',
    'button.cancel': 'Cancel'
  };

  transform(key: string): string {
    // If translation exists, use it
    if (this.translations[key]) {
      return this.translations[key];
    }

    // If key starts with 'field.', remove the prefix and return clean field name
    if (key.startsWith('field.')) {
      const fieldName = key.replace('field.', '');
      // Convert camelCase to Title Case
      return fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // If key starts with 'option.', remove the prefix and return clean option name
    if (key.startsWith('option.')) {
      const optionName = key.replace('option.', '');
      // Convert camelCase to Title Case
      return optionName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // If key starts with 'placeholder.', remove the prefix and return clean placeholder
    if (key.startsWith('placeholder.')) {
      const placeholderName = key.replace('placeholder.', '');
      // Convert camelCase to sentence case
      return 'Enter ' + placeholderName.replace(/([A-Z])/g, ' $1').toLowerCase();
    }

    // If key starts with 'dashboard.', remove the prefix and return clean section name
    if (key.startsWith('dashboard.')) {
      const sectionName = key.replace('dashboard.', '');
      // Convert camelCase to Title Case
      return sectionName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // If key starts with 'button.', remove the prefix and return clean button text
    if (key.startsWith('button.')) {
      const buttonName = key.replace('button.', '');
      // Convert camelCase to Title Case
      return buttonName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // If key starts with 'instruction.', remove the prefix and return clean instruction
    if (key.startsWith('instruction.')) {
      const instructionName = key.replace('instruction.', '');
      // Convert camelCase to sentence case
      return instructionName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // Default: return the key as is
    return key;
  }
}
