import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';

import { TranslatePipe } from '../../pipes/translate.pipe';

import { HttpClient } from '@angular/common/http';
import { LoginService } from '../../services/auth/login.service';
import Swal from 'sweetalert2';
import { MultiSelectDropdownComponent, MultiSelectOption } from '../../shared/multi-select-dropdown/multi-select-dropdown.component';
import { DashboardService } from '../../services/dashboard.service';

@Component({
  selector: 'app-dashboard',
  imports: [CommonModule, ReactiveFormsModule, TranslatePipe, MultiSelectDropdownComponent],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
  standalone: true,
})
export class DashboardComponent implements OnInit {
  employeeForm: FormGroup;
  isEditMode = false;
  editingEmployeeId: string | null = null;
  districts: string[] = ['Ariyalur', 'Chengalpattu',' Chennai(North)','Chennai(South)', 'Coimbatore', 'Cuddalore', 'Dharmapuri', 'Dindigul','Erode', 'Kallakurichi', 'Kancheepuram', 'Kanniyakumari', 'Karur','Krishnagiri','Madurai','Mayiladuthurai','Nagapattinam','Namakkal','Nilgiris','Perambalur','Pudukkottai','Ramanathapuram','Ranipet','Salem','Sivaganga','Tenkasi','Thanjavur','Theni','Thoothukudi','Tiruchirappalli','Tirunelveli','Tirupathur','Tiruppur','Tiruvallur','Tiruvannamalai','Tiruvarur','Vellore','Viluppuram','Virudhunagar'];
  educationQualifications: string[] = ['8th', '10th', '12th', 'Diploma', 'Highest Degree', 'Others'];
  communities: string[] = ['SC', 'ST', 'BC', 'MBC', 'General', 'Others'];
  sections: string[] = ['A', 'B', 'C', 'D', 'E'];
  isPermanent: boolean[] = [];
  isMainEmployeePermanent: boolean = false;

  // File upload properties
  profilePhotoError: string = '';
  profilePhotoPreview: string = '';
  signatureError: string = '';
  signaturePreview: string = '';
  selectedDocuments: File[] = [];
  documentError: string = '';
  role: string | null = null;

  // Track original documents vs new documents
  originalDocuments: File[] = [];
  newDocuments: File[] = [];

  // Photo and signature files
  selectedProfilePhoto: File | null = null;
  selectedSignature: File | null = null;

  // Nominee photo properties
  selectedNomineePhotos: (File | null)[] = [];
  nomineePhotoPreviews: (string | null)[] = [];
  nomineePhotoErrors: string[] = [];

  // Employee type visibility flags
  showServiceHistoryTrack: boolean = true;
  selectedEmployeeType: string = '';

  // Flags to track modifications for updates
  isProfilePhotoModified: boolean = false;
  isSignatureModified: boolean = false;
  isDocumentsModified: boolean = false;
  defaultLabel = "History";

  // Multi-select properties for involved persons
  employeeOptions: MultiSelectOption[] = [];
  selectedEmployees: MultiSelectOption[] = [];
  // File upload methods
  onProfilePhotoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        this.profilePhotoError = 'Only PNG, JPG, JPEG files are allowed';
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.profilePhotoError = 'File size should not exceed 5MB';
        return;
      }

      this.profilePhotoError = '';

      // Store the file and set modification flag
      this.selectedProfilePhoto = file;
      this.isProfilePhotoModified = true;
      console.log('Profile photo selected:', file.name);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.profilePhotoPreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  changeLabel() {
      if(this.defaultLabel == "Record") {
        this.defaultLabel = "History"
      }
  }
  onSignatureSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        this.signatureError = 'Only PNG, JPG, JPEG files are allowed';
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.signatureError = 'File size should not exceed 5MB';
        return;
      }

      this.signatureError = '';

      // Store the file and set modification flag
      this.selectedSignature = file;
      this.isSignatureModified = true;
      console.log('Signature selected:', file.name);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.signaturePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  onDocumentSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];
    if (files && files.length > 0) {
      this.documentError = '';

      for (const file of files) {
        // Check for duplicate files
        const isDuplicate = this.selectedDocuments.some(existingFile =>
          existingFile.name === file.name && existingFile.size === file.size
        );

        if (isDuplicate) {
          this.documentError = `File "${file.name}" is already selected`;
          return;
        }

        // Validate file type - Only PDF files allowed
        const allowedTypes = ['application/pdf'];

        if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.pdf')) {
          this.documentError = 'Only PDF files are allowed';
          return;
        }

        // Validate file size (10MB per file)
        if (file.size > 10 * 1024 * 1024) {
          this.documentError = `File "${file.name}" exceeds 10MB limit`;
          return;
        }
      }

      // Add files to selected documents and new documents
      this.selectedDocuments = [...this.selectedDocuments, ...files];
      this.newDocuments = [...this.newDocuments, ...files];
      this.isDocumentsModified = true;

      console.log('Documents selected:', this.selectedDocuments.map(f => ({
        fileName: f.name,
        fileType: f.type,
        size: f.size
      })));
      console.log('New documents added:', files.length);
      console.log('Documents modification flag set:', this.isDocumentsModified);
    }
  }

  removeDocument(index: number): void {
    const removedDocument = this.selectedDocuments[index];

    // Remove from selected documents
    this.selectedDocuments.splice(index, 1);

    // If it's a new document, also remove from newDocuments array
    if (!(removedDocument as any).isOriginal) {
      const newDocIndex = this.newDocuments.findIndex(doc => doc.name === removedDocument.name && doc.size === removedDocument.size);
      if (newDocIndex > -1) {
        this.newDocuments.splice(newDocIndex, 1);
      }

      // Update modification flag
      this.isDocumentsModified = this.newDocuments.length > 0;
      console.log('Removed new document. Documents modified:', this.isDocumentsModified);
    } else {
      console.log('Removed original document (will not affect upload)');
    }
  }

  openPDFInNewTab(document: File): void {
    console.log('Opening PDF in new tab:', document.name);

    try {
      // Check if this is an original document (from edit mode) with a URL
      if ((document as any).isOriginal && (document as any).fileUrl) {
        const fileUrl = (document as any).fileUrl;
        let fullUrl: string;

        // Construct full URL if needed
        if (fileUrl.startsWith('http')) {
          fullUrl = fileUrl;
        } else if (fileUrl.startsWith('/api/')) {
          fullUrl = `http://localhost:8082${fileUrl}`;
        } else {
          fullUrl = `http://localhost:8082${fileUrl}`;
        }

        console.log('Opening original document URL:', fullUrl);
        window.open(fullUrl, '_blank');
        return;
      }

      // For newly uploaded files, create blob URL
      if (document instanceof File) {
        const fileReader = new FileReader();
        fileReader.onload = (e) => {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
          const blobUrl = window.URL.createObjectURL(blob);

          console.log('Opening newly uploaded PDF with blob URL');
          window.open(blobUrl, '_blank');

          // Clean up blob URL after a delay to allow the tab to load
          setTimeout(() => {
            window.URL.revokeObjectURL(blobUrl);
          }, 1000);
        };
        fileReader.readAsArrayBuffer(document);
      }
    } catch (error) {
      console.error('Error opening PDF:', error);
      alert('Error opening PDF file. Please try again.');
    }
  }

  onNomineePhotoSelected(event: any, index: number): void {
    console.log(`Nominee photo selection triggered for index ${index}`);
    console.log('Current arrays state:', {
      selectedPhotos: this.selectedNomineePhotos.length,
      previews: this.nomineePhotoPreviews.length,
      errors: this.nomineePhotoErrors.length
    });

    const file = event.target.files[0];
    if (file) {
      console.log(`File selected for nominee ${index + 1}:`, {
        name: file.name,
        size: file.size,
        type: file.type
      });

      // Validate file type
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        this.nomineePhotoErrors[index] = 'Only PNG, JPG, JPEG files are allowed';
        console.error(`Invalid file type for nominee ${index + 1}:`, file.type);
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.nomineePhotoErrors[index] = 'File size should not exceed 5MB';
        console.error(`File too large for nominee ${index + 1}:`, file.size);
        return;
      }

      // Clear any previous errors
      this.nomineePhotoErrors[index] = '';

      // Store the file
      this.selectedNomineePhotos[index] = file;
      console.log(`Nominee ${index + 1} photo stored successfully:`, file.name);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.nomineePhotoPreviews[index] = e.target.result;
        console.log(`Nominee ${index + 1} photo preview created successfully`);
        console.log(`Preview URL length:`, e.target.result.length);

        // Trigger change detection
        setTimeout(() => {
          console.log(`After timeout - Preview ${index} exists:`, !!this.nomineePhotoPreviews[index]);
        }, 100);
      };

      reader.onerror = (error) => {
        console.error(`Error creating preview for nominee ${index + 1}:`, error);
        this.nomineePhotoErrors[index] = 'Error creating image preview';
      };

      reader.readAsDataURL(file);
    } else {
      console.log(`No file selected for nominee ${index + 1}`);
    }
  }

  // Method to get nominee photo URL for display
  getNomineePhotoUrl(index: number): string | null {
    if (this.nomineePhotoPreviews && this.nomineePhotoPreviews[index]) {
      return this.nomineePhotoPreviews[index];
    }
    return null;
  }

  // Handle nominee image error
  onNomineeImageError(event: any, index: number): void {
    console.log(`Error loading nominee ${index + 1} photo:`, event);
    console.log(`Current preview URL for nominee ${index + 1}:`, this.nomineePhotoPreviews[index]);
    // Hide the image on error
    event.target.style.display = 'none';
    // Clear the preview
    if (this.nomineePhotoPreviews && this.nomineePhotoPreviews[index]) {
      this.nomineePhotoPreviews[index] = null;
    }
  }

  // Helper method to check if nominee photo exists
  hasNomineePhoto(index: number): boolean {
    return !!(this.nomineePhotoPreviews && this.nomineePhotoPreviews[index]);
  }

  // Method to trigger file input for changing nominee photo
  changeNomineePhoto(index: number): void {
    // Trigger the hidden file input
    const fileInput = document.getElementById(`nomineePhotoInput${index}`) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  // Method to trigger file input for changing profile photo
  changeProfilePhoto(): void {
    // Trigger the hidden file input
    const fileInput = document.getElementById('profilePhotoInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  constructor(
    private fb: FormBuilder,
    private employeeService: EmployeeService,
    private route: ActivatedRoute,
    public router: Router,
    private http: HttpClient,
    private loginService: LoginService,
    private dashboardService: DashboardService
  ) {
    this.initializeForm();
    // Initialize nominee photo arrays for the default nominee entry
    this.selectedNomineePhotos = [null];
    this.nomineePhotoPreviews = [null];
    this.nomineePhotoErrors = [''];

    console.log('Dashboard component initialized with nominee arrays:', {
      selectedPhotos: this.selectedNomineePhotos.length,
      previews: this.nomineePhotoPreviews.length,
      errors: this.nomineePhotoErrors.length,
      nominationEntries: this.nominationEntries.length
    });
  }

  // Custom Validators
  static panValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const panPattern = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panPattern.test(control.value) ? null : { invalidPan: true };
  }

  static aadharValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const aadharPattern = /^[0-9]{12}$/;
    return aadharPattern.test(control.value) ? null : { invalidAadhar: true };
  }

  static uanValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const uanPattern = /^[0-9]{12}$/;
    return uanPattern.test(control.value) ? null : { invalidUan: true };
  }

  static ifscValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const ifscPattern = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscPattern.test(control.value) ? null : { invalidIfsc: true };
  }

  static bankAccountValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const accountPattern = /^[0-9]{9,18}$/;
    return accountPattern.test(control.value) ? null : { invalidBankAccount: true };
  }

  static employeeNameValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const namePattern = /^[A-Z][A-Za-z\s]*$/;
    return namePattern.test(control.value) ? null : { invalidEmployeeName: true };
  }

  static emailValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailPattern.test(control.value) ? null : { invalidEmail: true };
  }

  static ecpfNumberValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const ecpfPattern = /^[0-9]{12}$/;
    return ecpfPattern.test(control.value) ? null : { invalidEcpfNumber: true };
  }

  static registerNumberValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const registerPattern = /^[A-Z0-9]{5,20}$/;
    return registerPattern.test(control.value) ? null : { invalidRegisterNumber: true };
  }

  static phoneValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const phonePattern = /^[6-9][0-9]{9}$/;
    return phonePattern.test(control.value) ? null : { invalidPhone: true };
  }

  static ageValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const birthDate = new Date(control.value);
    const today = new Date();

    // Calculate age
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    // Adjust age if birthday hasn't occurred this year
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Check if age is less than 18
    if (age < 18) {
      return { ageRestriction: true };
    }

    return null;
  }

  ngOnInit(): void {
    this.role = sessionStorage.getItem('role');
    this.loadApprovedEmployeeNames();
    this.route.queryParams.subscribe(params => {
      if (params['edit'] && params['empId']) {
        this.isEditMode = true;
        this.editingEmployeeId = params['empId'];
        console.log('Edit mode activated for employee ID:', params['empId']);
        this.loadEmployeeForEdit(params['empId']);
        this.loadEmployeeDocuments(params['empId']);
      }
    });

    const existingEmployees = this.employeeService.loadEmployees();
    if (existingEmployees.length === 0) {
      // this.employeeService.generateSampleData();
    }
  }

  initializeForm() {
    this.employeeForm = this.fb.group({
      // Service record field
      mainEmployeeType: ['', Validators.required],
      seasonalCategory: ['', Validators.required],
      loadManCategory: ['', Validators.required],
      supernumericDateOfJoining: [''],
      supernumericRemarks: [''],
      othersRemarks: [''],

      // Main fields
      ecpfNumber: ['', [Validators.required, DashboardComponent.ecpfNumberValidator]],
      panNumber: ['', [Validators.required, DashboardComponent.panValidator]],
      employeeName: ['', [Validators.required, DashboardComponent.employeeNameValidator]],
      email: ['', [Validators.required, DashboardComponent.emailValidator]],
      gender: ['', Validators.required],
      registerNumber: ['', [Validators.required, DashboardComponent.registerNumberValidator]],
      section: ['', Validators.required],

      // Present Address fields
      presentDoorNo: ['', Validators.required],
      presentBuildingName: ['', Validators.required],
      presentStreetAddress: ['', Validators.required],
      presentCity: ['', Validators.required],
      presentPincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],

      // Permanent Address fields
      permanentDoorNo: ['', Validators.required],
      permanentBuildingName: ['', Validators.required],
      permanentStreetAddress: ['', Validators.required],
      permanentCity: ['', Validators.required],
      permanentPincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],

      // Address checkbox
      sameAsPresentAddress: [false],

      // Legacy address fields (for backward compatibility)
      presentAddress: ['', Validators.required],
      permanentAddress: ['', Validators.required],
      fatherName: ['', [Validators.required, DashboardComponent.employeeNameValidator]],
      motherName: ['', DashboardComponent.employeeNameValidator],
      maritalStatus: ['', Validators.required],
      spouseName: [''],
      mobileNumber: ['', [Validators.required, DashboardComponent.phoneValidator]],
      dateOfBirth: ['', [Validators.required, DashboardComponent.ageValidator]],
      religion: ['', Validators.required],
      community: ['', Validators.required],
      caste: ['', Validators.required],
      personalIdentificationmark1: ['', Validators.required],
      personalIdentificationmark2: [''],
      currentDesignation: ['', Validators.required],
      dateOfEntry: ['', Validators.required],
      district: ['', Validators.required],
      nativePlaceAndTaluk: ['', Validators.required],

      // Account Details
      bankAccountNumber: ['', [Validators.required, DashboardComponent.bankAccountValidator]],
      ifscCode: ['', [Validators.required, DashboardComponent.ifscValidator]],
      bankName: ['', Validators.required],
      uanNumber: ['', [Validators.required, DashboardComponent.uanValidator]],
      aadharNumber: ['', [Validators.required, DashboardComponent.aadharValidator]],

      // Salary Details
      lastSalaryRevisedDate: ['', Validators.required],

      // Additional Employee Details
      group: ['', Validators.required],
      payband: ['', Validators.required],
      gradepay: ['', Validators.required],

      // Form Arrays
      serviceEntries: this.fb.array([this.createServiceEntry()]),
      leaveEntries: this.fb.array([this.createLeaveEntry()]),
      trainingEntries: this.fb.array([this.createTrainingEntry()]),
      nominationEntries: this.fb.array([this.createNominationEntry()]),
      educationEntries: this.fb.array([this.createInitialEducationEntry()])
    });
  }

  createServiceEntry(): FormGroup {
    return this.fb.group({
      type: ['', Validators.required],
      appointmentType: [''],
      modeOfAppointment: [''],
      dateOfAppointment: [''],
      proceedingOrderNo: [''],
      proceedingOrderDate: [''],
      joiningDate: [''],
      fromDesignation: [''],
      toPromoted: [''],
      promotedDate: [''],
      fromDate: [''],
      toDate: [''],
      typeOfIncrement: [''],
      incrementType: [''],
      fromPlace: [''],
      toPlace: [''],
      // Seasonal increment fields
      basicPay: [''],
      da: [''],
      basicPlusDA: [''],
      // Punishment fields
      punishmentType: [''],
      punishmentDate: [''],
      // Withholding of increment specific fields
      withholdingFromDate: [''],
      withholdingToDate: [''],
      //Case Fields
      hasCaseDetails: ['no'], // Radio button for case details
      caseDetails: [''],
      caseDate: [''],
      caseNumber: [''],
      description: [''],
      personInvolved: [''],
      involvedPersons: [[] as number[]], // Array of employee IDs
      presentStatus: [''],
      // Probation fields
      probationDateOfJoining: [''],
      probationEndDate: [''],
      // Foreign service fields
      fromOrganization: [''],
      fromDesignationForeign: [''],
      toDesignationForeign: [''],
      fromDepartment: [''],
      toDepartment: [''],
      // Common fields for all service types
      designation: [''],
      originalDesignation: [''],
      parentDepartment: ['']
    });
  }

  createLeaveEntry(): FormGroup {
    return this.fb.group({
      leaveType: ['', Validators.required],
      openingBalance: ['', [Validators.required, Validators.min(0)]],
      closingBalance: ['', [Validators.required, Validators.min(0)]],
      entryDate: ['', Validators.required]
    });
  }

  createServiceRecord(): FormGroup {
    return this.fb.group({
      employeeType: ['', Validators.required]
    });
  }

  createTrainingEntry(): FormGroup {
    return this.fb.group({
      trainingtype: ['', Validators.required],
      date: ['', Validators.required]
    });
  }



  createNominationEntry(): FormGroup {
    return this.fb.group({
      nomineename: ['', [Validators.required, DashboardComponent.employeeNameValidator]],
      address: ['', Validators.required],
      relationship: ['', Validators.required],
      age: ['', [Validators.required, Validators.min(1), Validators.max(120)]],
      percentageofshare: ['', [Validators.required, Validators.min(1), Validators.max(100)]],
      gender: ['', Validators.required]
    });
  }

  createEducationEntry(): FormGroup {
    const formGroup = this.fb.group({
      qualification: ['', Validators.required],
      schoolname: ['', Validators.required], // School name is always mandatory
      coursename: [''], // Course name is optional
      collegename: [''],
      universityname: [''],
      specialization: [''] // New field for specialization
    });

    return formGroup;
  }

  createInitialEducationEntry(): FormGroup {
    return this.fb.group({
      qualification: ['', Validators.required], // Allow user to select qualification
      schoolname: ['', Validators.required], // School name is always mandatory
      coursename: [''], // Course name is optional
      collegename: [''],
      universityname: [''],
      specialization: [''] // New field for specialization
    });
  }

  get serviceEntries(): FormArray {
    return this.employeeForm.get('serviceEntries') as FormArray;
  }

  get leaveEntries(): FormArray {
    return this.employeeForm.get('leaveEntries') as FormArray;
  }

  get serviceRecords(): FormArray {
    return this.employeeForm.get('serviceRecords') as FormArray;
  }

  get trainingEntries(): FormArray {
    return this.employeeForm.get('trainingEntries') as FormArray;
  }



  get nominationEntries(): FormArray {
    return this.employeeForm.get('nominationEntries') as FormArray;
  }

  get educationEntries(): FormArray {
    return this.employeeForm.get('educationEntries') as FormArray;
  }

  addEducationEntry() {
    // Allow unlimited education entries
    const newEntry = this.createEducationEntry();
    this.educationEntries.push(newEntry);
  }

  removeEducationEntry(index: number) {
    if (this.educationEntries.length > 1) {
      this.educationEntries.removeAt(index);
    }
  }

  getQualificationByIndex(index: number): string {
    // This method is no longer needed since qualifications are selectable
    return '';
  }

  canAddNextEducation(): boolean {
    // Always allow adding more education entries
    return true;
  }

  getInstituteLabel(qualification: string): string {
    switch (qualification) {
      case '8th':
      case '10th':
      case '12th':
        return 'School Name';
      case 'Diploma':
        return 'College Name';
      case 'Highest Degree':
        return 'University Name';
      case 'Others':
        return 'Institute Name';
      default:
        return 'Institute Name';
    }
  }

  onMainEmployeeTypeChange(event: Event) {
    const selectedType = (event.target as HTMLSelectElement).value;
    this.selectedEmployeeType = selectedType;
    this.isMainEmployeePermanent = selectedType === 'Permanent';
    this.defaultLabel = selectedType === 'Permanent' ? "History" : "Record";

    // Show/hide service history track based on employee type
    if (selectedType === 'Permanent') {
      this.showServiceHistoryTrack = true;
      console.log('Permanent employee selected - showing all cards including service history track');
    } else if (selectedType === 'Seasonal') {
      this.showServiceHistoryTrack = false;
      console.log('Seasonal employee selected - hiding service history track card');
    }

    // Set validators based on employee type
    if (selectedType === 'Seasonal') {
      this.employeeForm.get('seasonalCategory')?.setValidators(Validators.required);
      this.employeeForm.get('loadManCategory')?.clearValidators();
      this.employeeForm.get('supernumericDateOfJoining')?.clearValidators();
      this.employeeForm.get('supernumericRemarks')?.clearValidators();
      this.employeeForm.get('othersRemarks')?.clearValidators();
    } else if (selectedType === 'Load Man') {
      this.employeeForm.get('loadManCategory')?.setValidators(Validators.required);
      this.employeeForm.get('seasonalCategory')?.clearValidators();
      this.employeeForm.get('supernumericDateOfJoining')?.clearValidators();
      this.employeeForm.get('supernumericRemarks')?.clearValidators();
      this.employeeForm.get('othersRemarks')?.clearValidators();
    } else if (selectedType === 'Supernumeric') {
      this.employeeForm.get('supernumericDateOfJoining')?.setValidators(Validators.required);
      this.employeeForm.get('supernumericRemarks')?.setValidators(Validators.required);
      this.employeeForm.get('seasonalCategory')?.clearValidators();
      this.employeeForm.get('loadManCategory')?.clearValidators();
      this.employeeForm.get('othersRemarks')?.clearValidators();
    } else if (selectedType === 'Others') {
      this.employeeForm.get('othersRemarks')?.setValidators(Validators.required);
      this.employeeForm.get('seasonalCategory')?.clearValidators();
      this.employeeForm.get('loadManCategory')?.clearValidators();
      this.employeeForm.get('supernumericDateOfJoining')?.clearValidators();
      this.employeeForm.get('supernumericRemarks')?.clearValidators();
    } else {
      this.employeeForm.get('seasonalCategory')?.clearValidators();
      this.employeeForm.get('loadManCategory')?.clearValidators();
      this.employeeForm.get('supernumericDateOfJoining')?.clearValidators();
      this.employeeForm.get('supernumericRemarks')?.clearValidators();
      this.employeeForm.get('othersRemarks')?.clearValidators();
    }

    this.employeeForm.get('seasonalCategory')?.updateValueAndValidity();
    this.employeeForm.get('loadManCategory')?.updateValueAndValidity();
    this.employeeForm.get('supernumericDateOfJoining')?.updateValueAndValidity();
    this.employeeForm.get('supernumericRemarks')?.updateValueAndValidity();
    this.employeeForm.get('othersRemarks')?.updateValueAndValidity();
  }

  onEmployeeTypeChange(event: Event, index: number) {
    const selectedType = (event.target as HTMLSelectElement).value;
    this.isPermanent[index] = selectedType === 'Permanent';
  }

  onAppointmentTypeChange(event: Event, index: number) {
    const selectedType = (event.target as HTMLSelectElement).value;
    const serviceEntry = this.serviceEntries.at(index);

    if (selectedType === 'Foreign Service') {
      // Set validators for foreign service fields
      serviceEntry.get('fromOrganization')?.setValidators(Validators.required);
      serviceEntry.get('fromDesignationForeign')?.setValidators(Validators.required);
      serviceEntry.get('toDesignationForeign')?.setValidators(Validators.required);
      serviceEntry.get('fromDepartment')?.setValidators(Validators.required);
      serviceEntry.get('toDepartment')?.setValidators(Validators.required);
    } else {
      // Clear validators for foreign service fields
      serviceEntry.get('fromOrganization')?.clearValidators();
      serviceEntry.get('fromDesignationForeign')?.clearValidators();
      serviceEntry.get('toDesignationForeign')?.clearValidators();
      serviceEntry.get('fromDepartment')?.clearValidators();
      serviceEntry.get('toDepartment')?.clearValidators();
    }

    // Update validity
    serviceEntry.get('fromOrganization')?.updateValueAndValidity();
    serviceEntry.get('fromDesignationForeign')?.updateValueAndValidity();
    serviceEntry.get('toDesignationForeign')?.updateValueAndValidity();
    serviceEntry.get('fromDepartment')?.updateValueAndValidity();
    serviceEntry.get('toDepartment')?.updateValueAndValidity();
  }

  onMaritalStatusChange(event: Event) {
    const selectedStatus = (event.target as HTMLInputElement).value;

    if (selectedStatus === 'Married') {
      this.employeeForm.get('spouseName')?.setValidators([Validators.required, DashboardComponent.employeeNameValidator]);
    } else {
      this.employeeForm.get('spouseName')?.clearValidators();
      this.employeeForm.get('spouseName')?.setValue('');
    }

    this.employeeForm.get('spouseName')?.updateValueAndValidity();
  }

  getServiceTypesForEmployee(): string[] {
    const mainEmployeeType = this.employeeForm.get('mainEmployeeType')?.value;

    if (mainEmployeeType === 'Seasonal') {
      return ['Appointment', 'Transfer', 'Retirement', 'Increment', 'Punishment', 'Probation'];
    }

    // Default service types for Permanent and Load Man employees
    return ['Appointment', 'Promotion', 'Increment', 'Transfer', 'Deputation', 'Punishment', 'Retirement', 'Probation'];
  }

  getPunishmentTypesForEmployee(): string[] {
    const mainEmployeeType = this.employeeForm.get('mainEmployeeType')?.value;

    if (mainEmployeeType === 'Seasonal') {
      return ['Black List', 'Relieved from Service'];
    }

    // Default punishment types for other employees
    return ['Black List', 'Censure', 'Compulsory Retirement', 'Demotion', 'Dismissal', 'Fine', 'Forfeiture of Service', 'Reduction in Rank', 'Removal', 'Reprimand', 'Suspension', 'Termination', 'Warning', 'Withholding of Increment', 'Relieved from Duty'];
  }

  loadEmployeeForEdit(empId: string): void {
    console.log('Loading employee details for ID:', empId);

    // Use the correct API endpoint: /employee/getEmp/{id}
    this.http.get<any>(`http://localhost:8082/employee/getEmp/${empId}`)
      .subscribe({
        next: (employee) => {
          console.log('Complete employee data loaded from API:', employee);
          this.populateFormWithEmployeeData(employee);
        },
        error: (error) => {
          console.error('Error loading employee from API:', error);
          console.error('API URL called:', `http://localhost:8082/employee/getEmp/${empId}`);
          // Fallback to localStorage
          const localEmployee = this.employeeService.getEmployeeById(empId);
          if (localEmployee) {
            console.log('Employee loaded from localStorage:', localEmployee);
            this.populateFormWithEmployeeData(localEmployee);
          } else {
            alert('Employee not found!');
            this.router.navigate(['/view']);
          }
        }
      });
  }

  loadEmployeeDocuments(empId: string): void {
    console.log('Loading documents for employee ID:', empId);

    // Call API to get employee documents
    this.http.get<any[]>(`http://localhost:8082/documents/employee/${empId}`)
      .subscribe({
        next: (documents) => {
          console.log('Employee documents loaded:', documents);

          // Convert API documents to File objects for display
          const convertedDocuments = documents.map(doc => {
            // Create a mock File object with the document information
            const file = new File([''], doc.fileName, { type: doc.fileType });
            // Add custom properties for display
            (file as any).id = doc.id;
            (file as any).employeeId = doc.employeeId;
            (file as any).fileUrl = doc.fileUrl;
            (file as any).uploadDate = doc.uploadDate;
            (file as any).isOriginal = true; // Mark as original document
            // Override size property
            Object.defineProperty(file, 'size', { value: doc.fileSize, writable: false });
            return file;
          });

          // Filter to show only PDF files in the selected documents section
          const pdfDocuments = convertedDocuments.filter(doc =>
            doc.type === 'application/pdf' || doc.name.toLowerCase().endsWith('.pdf')
          );

          // Store original documents (all types) and set only PDF documents as selected
          this.originalDocuments = [...convertedDocuments];
          this.selectedDocuments = [...pdfDocuments];
          this.newDocuments = []; // Clear new documents

          console.log('Original documents loaded (all types):', this.originalDocuments.length);
          console.log('PDF documents shown in selected documents:', this.selectedDocuments.length);
          console.log('Documents converted to File objects:', this.selectedDocuments);
        },
        error: (error) => {
          console.error('Error loading employee documents:', error);
          console.error('API URL called:', `http://localhost:8082/documents/employee/${empId}`);
        }
      });
  }

  populateFormWithEmployeeData(employee: any): void {
    console.log('Populating form with employee data:', employee);

    // Reset modification flags for edit mode
    this.isProfilePhotoModified = false;
    this.isSignatureModified = false;
    this.isDocumentsModified = false;

    // Populate basic fields
    this.employeeForm.patchValue({
      // Main employee type
      mainEmployeeType: employee.mainEmployeeType || '',
      seasonalCategory: employee.seasonalCategory || '',
      loadManCategory: employee.loadManCategory || '',
      supernumericDateOfJoining: employee.supernumericDateOfJoining || '',
      supernumericRemarks: employee.supernumericRemarks || '',
      othersRemarks: employee.othersRemarks || '',

      ecpfNumber: employee.ecpfNumber || '',
      panNumber: employee.panNumber || '',
      employeeName: employee.employeeName || '',
      email: employee.email || employee.profile?.email || '',
      gender: employee.gender || employee.profile?.gender || '',
      registerNumber: employee.registerNumber || '',
      section: employee.section || '',

      // Present Address structured fields
      presentDoorNo: employee.profile?.presentDoorNo || '',
      presentBuildingName: employee.profile?.presentBuildingName || '',
      presentStreetAddress: employee.profile?.presentStreetAddress || '',
      presentCity: employee.profile?.presentCity || '',
      presentPincode: employee.profile?.presentPincode || '',

      // Permanent Address structured fields
      permanentDoorNo: employee.profile?.permanentDoorNo || '',
      permanentBuildingName: employee.profile?.permanentBuildingName || '',
      permanentStreetAddress: employee.profile?.permanentStreetAddress || '',
      permanentCity: employee.profile?.permanentCity || '',
      permanentPincode: employee.profile?.permanentPincode || '',

      // Legacy address fields (fallback)
      presentAddress: employee.presentAddress || employee.profile?.presentaddress || '',
      permanentAddress: employee.permanentAddress || employee.profile?.permanentaddress || '',

      fatherName: employee.fatherName || employee.profile?.fatherName || '',
      motherName: employee.motherName || employee.profile?.motherName || '',
      maritalStatus: employee.maritalStatus || employee.profile?.maritalStatus || '',
      spouseName: employee.spouseName || employee.profile?.spouseName || '',
      mobileNumber: employee.mobileNumber || employee.profile?.mobileNumber || '',
      dateOfBirth: employee.dateOfBirth || employee.profile?.dateOfBirth || '',
      religion: employee.religion || '',
      community: employee.community || employee.profile?.community || '',
      caste: employee.caste || employee.profile?.caste || '',
      personalIdentificationmark1: employee.personalIdentificationmark1 || '',
      personalIdentificationmark2: employee.personalIdentificationmark2 || '',
      currentDesignation: employee.currentDesignation || '',
      dateOfEntry: employee.dateOfEntry || '',
      district: employee.district || employee.profile?.district || '',
      nativePlaceAndTaluk: employee.nativePlaceAndTaluk || employee.profile?.nativeplaceandtaluk || '',

      // Account details
      bankAccountNumber: employee.accountDetails?.bankaccountnumber || '',
      ifscCode: employee.accountDetails?.ifsccode || '',
      bankName: employee.accountDetails?.bankname || '',
      uanNumber: employee.accountDetails?.uannumber || '',
      aadharNumber: employee.accountDetails?.aadharnumber || '',

      // Salary details
      lastSalaryRevisedDate: employee.salaryDetails?.lastSalaryRevisedDate || '',
      group: employee.salaryDetails?.group || '',
      payband: employee.salaryDetails?.payband || '',
      gradepay: employee.salaryDetails?.gradepay || ''
    });

    // Merge punishment data into service history
    const serviceHistory = employee.serviceHistory || [];
    const punishmentDetails = employee.punishmentDetails || [];

    // Convert punishment details to service entries
    const punishmentServiceEntries = punishmentDetails.map((punishment: any) => ({
      type: 'Punishment',
      status: 'Active',
      punishmenttype: punishment.punishmenttype,
      date: punishment.date,
      punishmentdate: punishment.date,
      casedetails: punishment.casedetails,
      caseNumber: punishment.caseNumber,
      caseDate: punishment.caseDate,
      description: punishment.description,
      personInvolved: punishment.personInvolved,
      involvedPersons: punishment.involvedPersons || punishment.personsInvolved || [],
      presentStatus: punishment.presentStatus
    }));

    // Combine service history with punishment entries
    const combinedServiceHistory = [...serviceHistory, ...punishmentServiceEntries];

    // Populate arrays
    this.populateServiceEntries(combinedServiceHistory);
    this.populateLeaveEntries(employee.leaveBalances || []);
    this.populateEducationEntries(employee.educationQualifications || []);
    this.populateTrainingEntries(employee.trainingDetails || []);
    this.populateNominationEntries(employee.nominees || []);

    // Handle documents if included in the main response
    if (employee.documents && employee.documents.length > 0) {
      const convertedDocuments = employee.documents.map((doc: any) => {
        const file = new File([''], doc.fileName, { type: doc.fileType });
        (file as any).id = doc.id;
        (file as any).employeeId = doc.employeeId;
        (file as any).fileUrl = doc.fileUrl;
        (file as any).uploadDate = doc.uploadDate;
        (file as any).isOriginal = true; // Mark as original document
        Object.defineProperty(file, 'size', { value: doc.fileSize, writable: false });
        return file;
      });

      // Filter to show only PDF files in the selected documents section
      const pdfDocuments = convertedDocuments.filter(doc =>
        doc.type === 'application/pdf' || doc.name.toLowerCase().endsWith('.pdf')
      );

      // Store original documents (all types) and set only PDF documents as selected
      this.originalDocuments = [...convertedDocuments];
      this.selectedDocuments = [...pdfDocuments];
      this.newDocuments = []; // Clear new documents

      console.log('Documents loaded from main API response (all types):', this.originalDocuments.length);
      console.log('PDF documents shown in selected documents:', this.selectedDocuments.length);
      console.log('Original documents set:', this.originalDocuments.length);
    }

    // Load profile photo if available
    this.loadProfilePhotoForEdit(employee);
  }

  loadProfilePhotoForEdit(employee: any): void {
    console.log('Loading profile photo for edit mode...');
    console.log('Employee data for profile photo:', {
      profile: employee.profile,
      profilephoto: employee.profile?.profilephoto,
      profilePhoto: employee.profile?.profilePhoto,
      directProfilePhoto: employee.profilePhoto,
      profilePhotoUrl: employee.profilePhotoUrl
    });

    // Check multiple possible locations for profile photo
    const photoPath = employee.profile?.profilephoto ||
                     employee.profile?.profilePhoto ||
                     employee.profilePhoto ||
                     employee.profilePhotoUrl;

    if (photoPath && photoPath.trim() !== '' && photoPath !== 'pending_upload') {
      const baseUrl = 'http://localhost:8082';

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      console.log('Profile photo URL constructed for edit:', fullPhotoUrl);
      this.profilePhotoPreview = fullPhotoUrl;

      // Test if the image loads successfully
      const img = new Image();
      img.onload = () => {
        console.log('Profile photo loaded successfully for edit mode');
      };
      img.onerror = () => {
        console.log('Profile photo failed to load, trying document service...');
        this.tryDocumentServiceForProfilePhoto();
      };
      img.src = fullPhotoUrl;

      return;
    }

    console.log('No profile photo path found in employee data, trying document service...');
    this.tryDocumentServiceForProfilePhoto();
  }

  private tryDocumentServiceForProfilePhoto(): void {
    // Fallback: Try to load from document service
    if (this.editingEmployeeId) {
      console.log('Trying to load profile photo from document service...');
      this.http.get(`http://localhost:8082/document/photo/${this.editingEmployeeId}`, { responseType: 'blob' })
        .subscribe({
          next: (blob) => {
            console.log('Profile photo loaded from document service for edit');
            const photoUrl = window.URL.createObjectURL(blob);
            this.profilePhotoPreview = photoUrl;
          },
          error: (error) => {
            console.log('No profile photo found in document service for edit:', error);
            this.profilePhotoPreview = '';
          }
        });
    } else {
      console.log('No editing employee ID available for document service');
      this.profilePhotoPreview = '';
    }
  }

  populateServiceEntries(serviceHistory: any[]): void {
    // Clear existing entries
    while (this.serviceEntries.length !== 0) {
      this.serviceEntries.removeAt(0);
    }

    // Add entries from API data
    if (serviceHistory.length > 0) {
      serviceHistory.forEach(service => {
        const serviceEntry = this.createServiceEntry();
        serviceEntry.patchValue({
          type: service.type || '',
          appointmentType: service.appointmenttype || '',
          modeOfAppointment: service.modeofappointment || '',
          dateOfAppointment: service.dateofappointment || '',
          proceedingOrderNo: service.proceedingorderno || '',
          proceedingOrderDate: service.proceedingorderdate || '',
          joiningDate: service.joiningdate || '',
          fromDesignation: service.fromdesignation || '',
          toPromoted: service.topromoted || '',
          promotedDate: service.promoteddate || '', // Add this missing field
          fromDate: service.fromdate || '',
          toDate: service.todate || '',
          typeOfIncrement: service.typeofincrement || '',
          incrementType: service.incrementtype || service.typeofincrement || '',
          fromPlace: service.fromplace || '',
          toPlace: service.toplace || '',
          // Seasonal increment fields
          basicPay: service.basicPay || '',
          daField: service.daField || '',
          basicPlusDA: service.basicPlusDA || '',
          // Punishment fields
          punishmentType: service.punishmenttype || '',
          punishmentDate: service.date || service.punishmentdate || '',
          // Withholding of increment specific fields
          withholdingFromDate: service.withholdingFromDate || '',
          withholdingToDate: service.withholdingToDate || '',
          // Case details fields
          hasCaseDetails: service.hasCaseDetails || 'no',
          caseDetails: service.casedetails || '',
          caseNumber: service.caseNumber || '',
          caseDate: service.caseDate || '',
          description: service.description || '',
          personInvolved: service.personInvolved || '',
          involvedPersons: service.involvedPersons || service.personsInvolved || [],
          presentStatus: service.presentStatus || '',
          // Probation fields
          probationDateOfJoining: service.probationDateOfJoining || '',
          probationEndDate: service.probationEndDate || '',
          // Foreign service fields
          fromOrganization: service.fromOrganization || '',
          fromDesignationForeign: service.fromDesignationForeign || '',
          toDesignationForeign: service.toDesignationForeign || '',
          fromDepartment: service.fromDepartment || '',
          toDepartment: service.toDepartment || '',
          designation: service.designation || '',
          originalDesignation: service.originaldesignation || '',
          parentDepartment: service.parentdepartment || ''
        });
        this.serviceEntries.push(serviceEntry);
      });
    } else {
      // Add default entry if no data
      this.serviceEntries.push(this.createServiceEntry());
    }
  }

  populateLeaveEntries(leaveBalances: any[]): void {
    // Clear existing entries
    while (this.leaveEntries.length !== 0) {
      this.leaveEntries.removeAt(0);
    }

    // Add entries from API data
    if (leaveBalances.length > 0) {
      leaveBalances.forEach(leave => {
        const leaveEntry = this.createLeaveEntry();
        leaveEntry.patchValue({
          leaveType: leave.leaveType || '',
          openingBalance: leave.openingBalance || 0,
          closingBalance: leave.closingBalance || 0,
          entryDate: leave.entryDate || ''
        });
        this.leaveEntries.push(leaveEntry);
      });
    } else {
      // Add default entry if no data
      this.leaveEntries.push(this.createLeaveEntry());
    }
  }

  populateEducationEntries(educationQualifications: any[]): void {
    // Clear existing entries
    while (this.educationEntries.length !== 0) {
      this.educationEntries.removeAt(0);
    }

    // Add entries from API data
    if (educationQualifications.length > 0) {
      educationQualifications.forEach((education) => {
        const educationEntry = this.createEducationEntry();
        educationEntry.patchValue({
          qualification: education.qualification || '',
          schoolname: education.schoolname || education.collegename || education.universityname || '',
          coursename: education.coursename || '',
          universityname: education.universityname || '',
          specialization: education.specialization || ''
        });
        this.educationEntries.push(educationEntry);
      });
    } else {
      // Add default entry if no data
      this.educationEntries.push(this.createInitialEducationEntry());
    }
  }

  populateTrainingEntries(trainingDetails: any[]): void {
    console.log('Populating training entries with data:', trainingDetails);

    // Clear existing entries
    while (this.trainingEntries.length !== 0) {
      this.trainingEntries.removeAt(0);
    }

    // Add entries from API data
    if (trainingDetails.length > 0) {
      trainingDetails.forEach(training => {
        const trainingEntry = this.createTrainingEntry();
        trainingEntry.patchValue({
          trainingtype: training.trainingtype || '',  // Use correct form control name
          date: training.date || ''                   // Use correct form control name
        });
        this.trainingEntries.push(trainingEntry);
        console.log('Training entry added:', {
          trainingtype: training.trainingtype,
          date: training.date
        });
      });
    } else {
      // Add default entry if no data
      this.trainingEntries.push(this.createTrainingEntry());
    }

    console.log('Training entries populated. Total entries:', this.trainingEntries.length);
  }



  populateNominationEntries(nominees: any[]): void {
    // Clear existing entries and photo arrays
    while (this.nominationEntries.length !== 0) {
      this.nominationEntries.removeAt(0);
    }
    this.selectedNomineePhotos = [];
    this.nomineePhotoPreviews = [];
    this.nomineePhotoErrors = [];

    // Add entries from API data
    if (nominees.length > 0) {
      nominees.forEach((nominee, index) => {
        const nominationEntry = this.createNominationEntry();
        nominationEntry.patchValue({
          nomineename: nominee.nomineename || '',
          address: nominee.address || '',
          relationship: nominee.relationship || '',
          age: nominee.age || 0,
          percentageofshare: nominee.percentageofshare || 0,
          gender: nominee.gender || ''
        });
        this.nominationEntries.push(nominationEntry);

        // Initialize photo arrays for each nominee
        this.selectedNomineePhotos.push(null);
        this.nomineePhotoPreviews.push(null);
        this.nomineePhotoErrors.push('');

        // If nominee has a photo, construct the full URL and set the preview
        if (nominee.nomineePhoto && nominee.nomineePhoto !== 'pending_upload' && nominee.nomineePhoto.trim() !== '') {
          const baseUrl = 'http://localhost:8082';
          const photoPath = nominee.nomineePhoto;

          // Use the same logic as profile photo URL construction
          let fullPhotoUrl: string;
          if (photoPath.startsWith('http')) {
            fullPhotoUrl = photoPath;
          } else if (photoPath.startsWith('/api/')) {
            fullPhotoUrl = `${baseUrl}${photoPath}`;
          } else {
            fullPhotoUrl = `${baseUrl}${photoPath}`;
          }

          console.log(`Nominee ${index + 1} photo URL constructed for edit:`, fullPhotoUrl);
          this.nomineePhotoPreviews[index] = fullPhotoUrl;
        } else {
          this.nomineePhotoPreviews[index] = null;
          console.log(`Nominee ${index + 1} has no photo or invalid photo path:`, nominee.nomineePhoto);
        }
      });
    } else {
      // Add default entry if no data
      this.nominationEntries.push(this.createNominationEntry());
      this.selectedNomineePhotos.push(null);
      this.nomineePhotoPreviews.push(null);
      this.nomineePhotoErrors.push('');
    }
  }

  onSubmit() {
    console.log('Form submission started');
    console.log('Form valid:', this.employeeForm.valid);
    console.log('Selected documents count:', this.selectedDocuments.length);
    console.log('Selected documents:', this.selectedDocuments);

    // Mark all fields as touched to show validation errors
    this.markFormGroupTouched(this.employeeForm);

    // Check if form is valid - Enable validation check
    // if (!this.employeeForm.valid) {
    //   console.log('Form is invalid. Validation errors:', this.getFormValidationErrors());

    //   // Find the first invalid field and scroll to it
    //   const firstInvalidField = this.findFirstInvalidField();
    //   if (firstInvalidField) {
    //     firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
    //     firstInvalidField.focus();
    //   }

    //   alert('Please fill in all required fields correctly before submitting. Check the highlighted fields.');
    //   return;
    // }

    // Check if documents are uploaded (only for new employees)
    if (!this.isEditMode && this.selectedDocuments.length === 0) {
      alert('Please upload at least one document!');
      return;
    }

    // If validation passes, proceed with submission
    const formData = this.employeeForm.value;
    const payload = this.formatDataForBackend(formData);

    console.log('Final payload before sending:', payload);

    if (this.isEditMode && this.editingEmployeeId) {
      // Update existing employee
      this.updateEmployee(payload);
    } else {
      // Create new employee
      this.createEmployee(payload);
    }
  }

  // Helper method to mark all form controls as touched
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }

  // Helper method to get form validation errors for debugging
  private getFormValidationErrors() {
    const errors: any = {};
    Object.keys(this.employeeForm.controls).forEach(key => {
      const control = this.employeeForm.get(key);
      if (control && !control.valid) {
        errors[key] = control.errors;

        // Also check form arrays
        if (control instanceof FormArray) {
          control.controls.forEach((arrayControl, index) => {
            if (arrayControl && !arrayControl.valid) {
              errors[`${key}[${index}]`] = arrayControl.errors;

              // Check individual form group controls within arrays
              if (arrayControl instanceof FormGroup) {
                Object.keys(arrayControl.controls).forEach(subKey => {
                  const subControl = arrayControl.get(subKey);
                  if (subControl && !subControl.valid) {
                    errors[`${key}[${index}].${subKey}`] = subControl.errors;
                  }
                });
              }
            }
          });
        }
      }
    });
    console.log('Form validation errors:', errors);
    return errors;
  }

  // Helper method to find the first invalid field
  private findFirstInvalidField(): HTMLElement | null {
    const invalidFields = document.querySelectorAll('.form-control.ng-invalid');
    if (invalidFields.length > 0) {
      return invalidFields[0] as HTMLElement;
    }
    return null;
  }

  formatDataForBackend(formData: any): any {
    console.log('Formatting data for backend...');
    console.log('Selected documents in formatDataForBackend:', this.selectedDocuments);

    // Get the logged-in username
    const loggedInUsername = this.loginService.getUserName || sessionStorage.getItem('username') || '';
    console.log('Adding logged-in username to payload:', loggedInUsername);

    // Format address fields for legacy support
    const presentAddress = `${formData.presentDoorNo}, ${formData.presentBuildingName}, ${formData.presentStreetAddress}, ${formData.presentCity}, ${formData.presentPincode}`;
    const permanentAddress = `${formData.permanentDoorNo}, ${formData.permanentBuildingName}, ${formData.permanentStreetAddress}, ${formData.permanentCity}, ${formData.permanentPincode}`;

    const payload = {
      // Employee entity fields (matching your backend Employee entity)
      mainEmployeeType: formData.mainEmployeeType,
      seasonalCategory: formData.seasonalCategory,
      loadManCategory: formData.loadManCategory,
      supernumericDateOfJoining: formData.supernumericDateOfJoining,
      supernumericRemarks: formData.supernumericRemarks,
      othersRemarks: formData.othersRemarks,
      ecpfNumber: formData.ecpfNumber,
      empId: formData.ecpfNumber, // Using ecpfNumber as empId
      panNumber: formData.panNumber,
      employeeName: formData.employeeName,
      email: formData.email,
      gender: formData.gender,
      mobileNumber: formData.mobileNumber,
      registerNumber: formData.registerNumber,
      section: formData.section,
      fatherName: formData.fatherName,
      motherName: formData.motherName,
      maritalStatus: formData.maritalStatus,
      spouseName: formData.spouseName,
      dateOfBirth: formData.dateOfBirth,
      religion: formData.religion,
      community: formData.community,
      caste: formData.caste,
      personalIdentificationmark1: formData.personalIdentificationmark1,
      personalIdentificationmark2: formData.personalIdentificationmark2,
      currentDesignation: formData.currentDesignation,
      dateOfEntry: formData.dateOfEntry,
      district: formData.district,
      nativePlaceAndTaluk: formData.nativePlaceAndTaluk,

      // Additional employee fields
      group: formData.group,
      payband: formData.payband,
      gradepay: formData.gradepay,

      createdBy: loggedInUsername,

      // Profile entity fields (matching your backend Profile entity)
      profile: {
        fatherName: formData.fatherName,
        motherName: formData.motherName,
        maritalStatus: formData.maritalStatus,
        spouseName: formData.spouseName,
        dateOfBirth: formData.dateOfBirth,
        community: formData.community,
        caste: formData.caste,
        district: formData.district,
        nativeplaceandtaluk: formData.nativePlaceAndTaluk,

        // Present address fields
        presentDoorNo: formData.presentDoorNo,
        presentBuildingName: formData.presentBuildingName,
        presentStreetAddress: formData.presentStreetAddress,
        presentCity: formData.presentCity,
        presentPincode: formData.presentPincode,

        // Permanent address fields
        permanentDoorNo: formData.permanentDoorNo,
        permanentBuildingName: formData.permanentBuildingName,
        permanentStreetAddress: formData.permanentStreetAddress,
        permanentCity: formData.permanentCity,
        permanentPincode: formData.permanentPincode,

        email: formData.email,
        gender: formData.gender
      },

      accountDetails: {
        bankaccountnumber: formData.bankAccountNumber,
        ifsccode: formData.ifscCode,
        bankname: formData.bankName,
        uannumber: formData.uanNumber,
        aadharnumber: formData.aadharNumber
      },

      salaryDetails: {
        lastSalaryRevisedDate: formData.lastSalaryRevisedDate,
        group: formData.group,
        payband: formData.payband,
        gradepay: formData.gradepay
      },

      serviceHistory: formData.serviceEntries.map((entry: any) => ({
        date: entry.dateOfAppointment || entry.joiningDate || entry.fromDate || entry.promotedDate || entry.punishmentDate || '',
        type: entry.type,
        appointmenttype: entry.appointmentType,
        modeofappointment: entry.modeOfAppointment,
        dateofappointment: entry.dateOfAppointment,
        proceedingorderno: entry.proceedingOrderNo,
        proceedingorderdate: entry.proceedingOrderDate,
        joiningdate: entry.joiningDate,
        fromdesignation: entry.fromDesignation,
        topromoted: entry.toPromoted,
        promoteddate: entry.promotedDate, // Add this missing field
        fromdate: entry.fromDate,
        todate: entry.toDate,
        typeofincrement: entry.typeOfIncrement || entry.incrementType,
        incrementtype: entry.incrementType, // Add this field too
        fromplace: entry.fromPlace,
        toplace: entry.toPlace,
        // Seasonal increment fields
        basicPay: entry.basicPay || null,
        daField: entry.daField || null,
        basicPlusDA: entry.basicPlusDA || null,
        // Punishment fields
        punishmenttype: entry.punishmentType,
        punishmentdate: entry.punishmentDate,
        // Withholding of increment specific fields
        withholdingFromDate: entry.withholdingFromDate || null,
        withholdingToDate: entry.withholdingToDate || null,
        // Case details fields
        hasCaseDetails: entry.hasCaseDetails || 'no',
        casedetails: entry.caseDetails,
        caseNumber: entry.caseNumber,
        caseDate: entry.caseDate,
        description: entry.description,
        personInvolved: entry.personInvolved,
        involvedPersons: entry.involvedPersons || [],
        presentStatus: entry.presentStatus,
        // Probation fields
        probationDateOfJoining: entry.probationDateOfJoining,
        probationEndDate: entry.probationEndDate,
        // Foreign service fields
        fromOrganization: entry.fromOrganization,
        fromDesignationForeign: entry.fromDesignationForeign,
        toDesignationForeign: entry.toDesignationForeign,
        fromDepartment: entry.fromDepartment,
        toDepartment: entry.toDepartment,
        // Deputation fields
        designation: entry.designation,
        originaldesignation: entry.originalDesignation,
        parentdepartment: entry.parentDepartment
      })),

      leaveBalances: formData.leaveEntries.map((entry: any) => ({
        leaveType: entry.leaveType,
        openingBalance: entry.openingBalance,
        closingBalance: entry.closingBalance,
        entryDate: entry.entryDate
      })),

      educationQualifications: formData.educationEntries.map((entry: any) => ({
        qualification: entry.qualification,
        coursename: entry.coursename,
        schoolname: entry.schoolname,
        collegename: entry.collegename,
        universityname: entry.universityname,
        specialization: entry.specialization
      })),



      trainingDetails: formData.trainingEntries.map((entry: any) => ({
        trainingtype: entry.trainingtype,
        date: entry.date
      })),

      nominees: formData.nominationEntries.map((entry: any, index: number) => ({
        nomineename: entry.nomineename,
        address: entry.address,
        relationship: entry.relationship,
        age: entry.age,
        percentageofshare: entry.percentageofshare,
        gender: entry.gender,
        // Nominee photo will be handled by the backend when files are uploaded
        nomineePhoto: this.selectedNomineePhotos[index] ? 'pending_upload' : null
      }))
    };

    console.log('Payload prepared (documents will be uploaded separately):', payload);
    return payload;
  }

  createEmployee(payload: any) {
    console.log('Creating employee with createEmployeeWithFiles endpoint:', payload);

    // Create FormData for multipart/form-data request
    const formData = new FormData();

    // Add employee data as JSON string
    formData.append('employeeData', JSON.stringify(payload));

    // Add profile photo if selected
    if (this.selectedProfilePhoto) {
      formData.append('profilePhoto', this.selectedProfilePhoto);
      console.log('Profile photo added to form data:', this.selectedProfilePhoto.name);
    }

    // Add nominee photos if selected
    this.selectedNomineePhotos.forEach((photo, index) => {
      if (photo) {
        formData.append('nomineePhotos', photo);
        console.log(`Nominee photo ${index + 1} added to form data:`, photo.name);
      }
    });

    // Add documents if selected
    this.selectedDocuments.forEach((doc, index) => {
      if (doc) {
        formData.append('documents', doc);
        console.log(`Document ${index + 1} added to form data:`, doc.name);
      }
    });

    console.log('Sending request to createEmployeeWithFiles endpoint');

    this.http.post('http://localhost:8082/employee/createEmployeeWithFiles', formData)
      .subscribe({
        next: (response: any) => {
          console.log('Employee created successfully with createEmployeeWithFiles:', response);

          Swal.fire({
            title: 'Success',
            text: `Employee created successfully with all files uploaded!`,
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745'
          }).then(() => {
            if(this.role == 'OPERATOR') {
              this.router.navigate(['/view']);
            } else {
              this.router.navigate(['/dashboard']);
            }
          });
        },
        error: (error) => {
          console.error('Error creating employee with createEmployeeWithFiles:', error);
          Swal.fire({
            title: 'Error',
            text: `Something went wrong while creating employee`,
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#dc3545'
          });
        }
      });
  }

  updateEmployee(payload: any) {
    console.log('Updating employee with updateEmployeeWithFiles endpoint:', payload);
    console.log('Employee ID for update:', this.editingEmployeeId);

    // Create FormData for multipart/form-data request
    const formData = new FormData();

    // Add employee data as JSON string
    formData.append('employeeData', JSON.stringify(payload));

    // Add profile photo if selected and modified
    if (this.selectedProfilePhoto && this.isProfilePhotoModified) {
      formData.append('profilePhoto', this.selectedProfilePhoto);
      console.log('Profile photo added to form data:', this.selectedProfilePhoto.name);
    }

    // Add nominee photos if any are selected
    this.selectedNomineePhotos.forEach((photo, index) => {
      if (photo) {
        formData.append('nomineePhotos', photo);
        console.log(`Nominee photo ${index + 1} added to form data:`, photo.name);
      }
    });

    // Add new documents if any
    this.newDocuments.forEach((doc, index) => {
      if (doc && !(doc as any).isOriginal) {
        formData.append('documents', doc);
        console.log(`New document ${index + 1} added to form data:`, doc.name);
      }
    });

    console.log('Sending request to updateEmployeeWithFiles endpoint');

    this.http.put(`http://localhost:8082/employee/updateEmployeeWithFiles/${this.editingEmployeeId}`, formData)
      .subscribe({
        next: (response) => {
          console.log('Employee updated successfully with updateEmployeeWithFiles:', response);

          Swal.fire({
            title: 'Success',
            text: `Employee updated successfully with all files uploaded!`,
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745'
          }).then(() => {
            if(this.role == 'OPERATOR') {
              this.router.navigate(['/view']);
            } else {
              this.router.navigate(['/dashboard']);
            }
          });
        },
        error: (error) => {
          console.error('Error updating employee with updateEmployeeWithFiles:', error);
          console.error('API URL called:', `http://localhost:8082/employee/updateEmployeeWithFiles/${this.editingEmployeeId}`);
          Swal.fire({
            title: 'Error',
            text: `Something went wrong while updating employee`,
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#dc3545'
          });
        }
      });
  }

  uploadEmployeeFiles(employeeId: string, isUpdate: boolean = false): void {
    console.log('Starting file uploads for employee ID:', employeeId);

    const uploadPromises: Promise<any>[] = [];

    // Upload profile photo if selected (or modified in edit mode)
    if (this.selectedProfilePhoto && (!isUpdate || this.isProfilePhotoModified)) {
      console.log('Uploading profile photo...');
      uploadPromises.push(this.uploadProfilePhoto(employeeId));
    }

    // Upload signature if selected (or modified in edit mode)
    if (this.selectedSignature && (!isUpdate || this.isSignatureModified)) {
      console.log('Uploading signature...');
      uploadPromises.push(this.uploadSignature(employeeId));
    }

    // Upload nominee photos if any are selected
    this.selectedNomineePhotos.forEach((photo, index) => {
      if (photo) {
        console.log(`Uploading nominee ${index + 1} photo...`);
        uploadPromises.push(this.uploadNomineePhoto(employeeId, photo, index));
      }
    });

    // Upload documents if any new documents added (or not in update mode)
    if (!isUpdate || this.isDocumentsModified) {
      if (this.newDocuments.length > 0 || (!isUpdate && this.selectedDocuments.length > 0)) {
        console.log('Uploading documents...');
        console.log('New documents to upload:', this.newDocuments.length);
        console.log('Is update mode:', isUpdate);
        console.log('Documents modified:', this.isDocumentsModified);
        uploadPromises.push(this.uploadDocuments(employeeId, isUpdate));
      }
    } else {
      console.log('Skipping document upload - no new documents in update mode');
    }

    // Wait for all uploads to complete
    if (uploadPromises.length > 0) {
      Promise.all(uploadPromises)
        .then(() => {
          const photoCount = (this.selectedProfilePhoto && (!isUpdate || this.isProfilePhotoModified)) ? 1 : 0;
          const signatureCount = (this.selectedSignature && (!isUpdate || this.isSignatureModified)) ? 1 : 0;
          const nomineePhotoCount = this.selectedNomineePhotos.filter(photo => photo !== null).length;
          const docCount = isUpdate ? this.newDocuments.length : this.selectedDocuments.length;

          const totalUploaded = photoCount + signatureCount + nomineePhotoCount + docCount;

          Swal.fire({
            title: 'Success',
            text: `Employee ${isUpdate ? 'updated' : 'created'} successfully${totalUploaded > 0 ? ` with ${totalUploaded} files uploaded!` : '!'}`,
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745'
          }).then(() => {
            if(this.role == 'OPERATOR') {
              this.router.navigate(['/view']);
            } else {
              this.router.navigate(['/dashboard']);
            }
          });
        })
        .catch((error) => {
          console.error('Error uploading files:', error);
          Swal.fire({
            title: 'Warning',
            text: `Employee ${isUpdate ? 'updated' : 'created'} but some files failed to upload. Please try uploading files separately.`,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#ffc107'
          }).then(() => {
            if(this.role == 'OPERATOR') {
              this.router.navigate(['/view']);
            } else {
              this.router.navigate(['/dashboard']);
            }
          });
        });
    } else {
      Swal.fire({
        title: 'Success',
        text: `Employee ${isUpdate ? 'updated' : 'created'} successfully!`,
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#28a745'
      }).then(() => {
        if(this.role == 'OPERATOR') {
          this.router.navigate(['/view']);
        } else {
          this.router.navigate(['/dashboard']);
        }
      });
    }
  }

  uploadProfilePhoto(employeeId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.selectedProfilePhoto) {
        resolve('No profile photo to upload');
        return;
      }

      console.log('Uploading profile photo for employee ID:', employeeId);

      // Create FormData to send photo
      const formData = new FormData();
      formData.append('file', this.selectedProfilePhoto);

      // Call profile photo upload API
      this.http.post(`http://localhost:8082/document/upload/photo/${employeeId}`, formData)
        .subscribe({
          next: (response) => {
            console.log('Profile photo uploaded successfully:', response);
            resolve(response);
          },
          error: (error) => {
            console.error('Error uploading profile photo:', error);
            reject(error);
          }
        });
    });
  }

  uploadSignature(employeeId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.selectedSignature) {
        resolve('No signature to upload');
        return;
      }

      console.log('Uploading signature for employee ID:', employeeId);

      // Create FormData to send signature
      const formData = new FormData();
      formData.append('file', this.selectedSignature);

      // Call signature upload API
      this.http.post(`http://localhost:8082/document/upload/signature/${employeeId}`, formData)
        .subscribe({
          next: (response) => {
            console.log('Signature uploaded successfully:', response);
            resolve(response);
          },
          error: (error) => {
            console.error('Error uploading signature:', error);
            reject(error);
          }
        });
    });
  }

  uploadNomineePhoto(employeeId: string, photo: File, nomineeIndex: number): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!photo) {
        resolve('No nominee photo to upload');
        return;
      }

      console.log(`Uploading nominee ${nomineeIndex + 1} photo for employee ID:`, employeeId);

      // Create a new File object with a descriptive name for nominee photo
      const nomineePhotoFile = new File([photo], `${photo.name}`, {
        type: photo.type,
        lastModified: photo.lastModified
      });

      // Create FormData to send nominee photo using existing document upload API
      const formData = new FormData();
      formData.append('file', nomineePhotoFile);

      // Use the existing document upload API
      this.http.post(`http://localhost:8082/document/upload/${employeeId}`, formData)
        .subscribe({
          next: (response: any) => {
            console.log(`Nominee ${nomineeIndex + 1} photo uploaded successfully:`, response);
            resolve(response);
          },
          error: (error) => {
            console.error(`Error uploading nominee ${nomineeIndex + 1} photo:`, error);
            reject(error);
          }
        });
    });
  }

  uploadDocuments(employeeId: string, isUpdate: boolean = false): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log('Uploading documents for employee ID:', employeeId);
      console.log('Is update mode:', isUpdate);

      // Determine which documents to upload
      const documentsToUpload = isUpdate ? this.newDocuments : this.selectedDocuments;

      console.log('Documents to upload:', documentsToUpload.length);
      console.log('Files to upload:', documentsToUpload.map(f => f.name));

      if (documentsToUpload.length === 0) {
        console.log('No documents to upload');
        resolve('No documents to upload');
        return;
      }

      // Create FormData to send files
      const formData = new FormData();

      // Append each file to FormData with 'file' as the parameter name
      documentsToUpload.forEach((file, index) => {
        // Only upload files that are not marked as original
        if (!(file as any).isOriginal) {
          formData.append('file', file);
          console.log(`Added file ${index + 1}: ${file.name} (${file.type})`);
        }
      });

      // Check if there are any files to upload after filtering
      if (!formData.has('file')) {
        console.log('No new files to upload after filtering');
        resolve('No new files to upload');
        return;
      }

      // Call document upload API
      this.http.post(`http://localhost:8082/document/upload/${employeeId}`, formData)
        .subscribe({
          next: (response) => {
            console.log('Documents uploaded successfully:', response);
            resolve(response);
          },
          error: (error) => {
            console.error('Error uploading documents:', error);
            reject(error);
          }
        });
    });
  }

  // Array manipulation methods
  addServiceEntry() { this.serviceEntries.push(this.createServiceEntry()); }
  removeServiceEntry(index: number) { if (this.serviceEntries.length > 1) this.serviceEntries.removeAt(index); }
  addLeaveEntry() { this.leaveEntries.push(this.createLeaveEntry()); }
  removeLeaveEntry(index: number) { if (this.leaveEntries.length > 1) this.leaveEntries.removeAt(index); }
  addServiceRecord() { this.serviceRecords.push(this.createServiceRecord()); this.isPermanent.push(false); }
  removeServiceRecord(index: number) { if (this.serviceRecords.length > 1) { this.serviceRecords.removeAt(index); this.isPermanent.splice(index, 1); } }
  addTrainingEntry() { this.trainingEntries.push(this.createTrainingEntry()); }
  removeTrainingEntry(index: number) { if (this.trainingEntries.length > 1) this.trainingEntries.removeAt(index); }

  addNominationEntry() {
    this.nominationEntries.push(this.createNominationEntry());
    // Initialize photo arrays for new nominee
    this.selectedNomineePhotos.push(null);
    this.nomineePhotoPreviews.push(null);
    this.nomineePhotoErrors.push('');

    console.log('Added new nomination entry. Total entries:', this.nominationEntries.length);
    console.log('Photo arrays length:', {
      selectedPhotos: this.selectedNomineePhotos.length,
      previews: this.nomineePhotoPreviews.length,
      errors: this.nomineePhotoErrors.length
    });
  }

  removeNominationEntry(index: number) {
    if (this.nominationEntries.length > 1) {
      this.nominationEntries.removeAt(index);
      // Remove corresponding photo data
      this.selectedNomineePhotos.splice(index, 1);
      this.nomineePhotoPreviews.splice(index, 1);
      this.nomineePhotoErrors.splice(index, 1);
    }
  }

  // Education qualification change handler
  onEducationQualificationChange(index: number, event: Event) {
    const target = event.target as HTMLSelectElement;
    const qualification = target.value;
    const educationEntry = this.educationEntries.at(index);

    if (qualification === 'Highest Degree') {
      // Add required validators for university name and specialization
      educationEntry.get('universityname')?.setValidators([Validators.required]);
      educationEntry.get('specialization')?.setValidators([Validators.required]);
      // Clear school name and course name for highest degree
      educationEntry.get('schoolname')?.clearValidators();
      educationEntry.patchValue({
        schoolname: '',
        coursename: ''
      });
    } else if (qualification === 'Others') {
      // For Others, only specialization is required
      educationEntry.get('specialization')?.setValidators([Validators.required]);
      // Clear other fields
      educationEntry.get('universityname')?.clearValidators();
      educationEntry.get('schoolname')?.clearValidators();
      educationEntry.patchValue({
        universityname: '',
        schoolname: '',
        coursename: ''
      });
    } else {
      // For other qualifications (8th, 10th, 12th, Diploma), school name is required
      educationEntry.get('schoolname')?.setValidators([Validators.required]);
      // Remove validators and clear values for university, specialization
      educationEntry.get('universityname')?.clearValidators();
      educationEntry.get('specialization')?.clearValidators();
      educationEntry.patchValue({
        universityname: '',
        specialization: ''
      });
    }

    // Update validation status for all fields
    educationEntry.get('universityname')?.updateValueAndValidity();
    educationEntry.get('specialization')?.updateValueAndValidity();
    educationEntry.get('schoolname')?.updateValueAndValidity();
  }

  // Address checkbox handler
  onSameAsPresentAddressChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const isChecked = target.checked;

    if (isChecked) {
      // Copy present address fields to permanent address fields
      const presentDoorNo = this.employeeForm.get('presentDoorNo')?.value || '';
      const presentBuildingName = this.employeeForm.get('presentBuildingName')?.value || '';
      const presentStreetAddress = this.employeeForm.get('presentStreetAddress')?.value || '';
      const presentCity = this.employeeForm.get('presentCity')?.value || '';
      const presentPincode = this.employeeForm.get('presentPincode')?.value || '';

      this.employeeForm.patchValue({
        permanentDoorNo: presentDoorNo,
        permanentBuildingName: presentBuildingName,
        permanentStreetAddress: presentStreetAddress,
        permanentCity: presentCity,
        permanentPincode: presentPincode
      });
    }
  }

  // Helper method to check if qualification is Highest Degree
  isHighestDegree(index: number): boolean {
    const educationEntry = this.educationEntries.at(index);
    return educationEntry.get('qualification')?.value === 'Highest Degree';
  }

  // Helper method to check if qualification is Others
  isOthersQualification(index: number): boolean {
    const educationEntry = this.educationEntries.at(index);
    return educationEntry.get('qualification')?.value === 'Others';
  }

  onTypeChange(index: number) {
    const serviceEntry = this.serviceEntries.at(index);
    const serviceType = serviceEntry.get('type')?.value;

    console.log(`Service type changed to: ${serviceType} for entry ${index}`);

    // Handle Probation type validation
    if (serviceType === 'Probation') {
      serviceEntry.get('probationDateOfJoining')?.setValidators(Validators.required);
      serviceEntry.get('probationEndDate')?.setValidators(Validators.required);
      console.log('Probation service type selected - probation fields are now required');
    } else {
      serviceEntry.get('probationDateOfJoining')?.clearValidators();
      serviceEntry.get('probationEndDate')?.clearValidators();
    }

    // If service type is not "Punishment", reset case details fields
    if (serviceType !== 'Punishment') {
      serviceEntry.patchValue({
        hasCaseDetails: 'no',
        caseDetails: '',
        caseDate: '',
        caseNumber: '',
        description: '',
        personInvolved: '',
        presentStatus: ''
      });

      // Clear validators for case detail fields
      serviceEntry.get('caseDetails')?.clearValidators();
      serviceEntry.get('caseDate')?.clearValidators();
      serviceEntry.get('caseNumber')?.clearValidators();
      serviceEntry.get('description')?.clearValidators();
      serviceEntry.get('personInvolved')?.clearValidators();
      serviceEntry.get('presentStatus')?.clearValidators();

      console.log(`Case details cleared for non-punishment service type: ${serviceType}`);
    } else {
      console.log('Punishment service type selected - case details section will be available');
    }

    // Update validation status
    serviceEntry.get('probationDateOfJoining')?.updateValueAndValidity();
    serviceEntry.get('probationEndDate')?.updateValueAndValidity();
    serviceEntry.get('caseDetails')?.updateValueAndValidity();
    serviceEntry.get('caseDate')?.updateValueAndValidity();
    serviceEntry.get('caseNumber')?.updateValueAndValidity();
    serviceEntry.get('description')?.updateValueAndValidity();
    serviceEntry.get('personInvolved')?.updateValueAndValidity();
    serviceEntry.get('presentStatus')?.updateValueAndValidity();
  }

  translate(key: string): string {
    // Simple translation mapping - replace with actual translation service if needed
    const translations: { [key: string]: string } = {
      'field.currentDesignation': 'Current Designation',
      'placeholder.enterDesignation': 'Enter designation',
      'dashboard.familyInfo': 'Family Information',
      'field.fatherName': 'Father Name',
      'field.motherName': 'Mother Name',
      'field.email': 'Email',
      'dashboard.personalIdentification': 'Personal Identification',
      'field.personalIdMark1': 'Personal ID Mark 1',
      'field.personalIdMark2': 'Personal ID Mark 2',
      'field.registerNo': 'Register Number',
      'dashboard.religiousSocialInfo': 'Religious & Social Information',
      'field.religion': 'Religion',
      'option.selectReligion': 'Select Religion',
      'option.hinduism': 'Hinduism',
      'option.christianity': 'Christianity',
      'option.islam': 'Islam',
      'option.sikhism': 'Sikhism',
      'option.buddhism': 'Buddhism',
      'option.other': 'Other',
      'field.community': 'Community',
      'placeholder.selectCommunity': 'Select Community',
      'field.caste': 'Caste',
      'field.section': 'Section',
      'placeholder.enterSection': 'Enter section',
      'field.nativeDistrict': 'Native District',
      'option.selectDistrict': 'Select District',
      'dashboard.addressInfo': 'Address Information',
      'dashboard.documents': 'Documents',
      'field.profilePhoto': 'Profile Photo',
      'dashboard.locationServiceInfo': 'Location & Service Information',
      'field.dateOfEntry': 'Date of Entry',
      'option.selectSection': 'Select Section',
      'field.district': 'District',
      'field.nativeAndTaluk': 'Native Place and Taluk',
      'dashboard.documentUpload': 'Document Upload',
      'dashboard.uploadDocuments': 'Upload Documents',
      'field.documents': 'Documents',
      'dashboard.uploadInstructions': 'Upload Instructions',
      'instruction.supportedFormats': 'Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG',
      'instruction.maxFileSize': 'Maximum file size: 10MB per file',
      'instruction.multipleFiles': 'Multiple files can be uploaded',
      'instruction.requiredDocuments': 'Please upload all required documents',
      'dashboard.accountDetails': 'Account Details',
      'field.bankAccountNo': 'Bank Account Number',
      'field.ifscCode': 'IFSC Code',
      'field.bankName': 'Bank Name',
      'field.panNumber': 'PAN Number',
      'field.uanNumber': 'UAN Number',
      'field.aadharNumber': 'Aadhar Number',
      'dashboard.educationQualification': 'Education Qualification',
      'dashboard.serviceHistory': 'Service History',
      'dashboard.trainingHistory': 'Training History',
      'field.trainingType': 'Training Type',
      'placeholder.enterTrainingType': 'Enter training type',
      'field.trainingDate': 'Training Date',
      'dashboard.nominationDetails': 'Nomination Details',
      'field.nomineeName': 'Nominee Name',
      'field.address': 'Address',
      'field.relationship': 'Relationship',
      'option.selectRelationship': 'Select Relationship',
      'option.spouse': 'Spouse',
      'option.son': 'Son',
      'option.daughter': 'Daughter',
      'option.father': 'Father',
      'option.mother': 'Mother',
      'option.brother': 'Brother',
      'option.sister': 'Sister',
      'field.age': 'Age',
      'field.percentageShare': 'Percentage Share',
      'field.gender': 'Gender',
      'option.selectGender': 'Select Gender',
      'option.male': 'Male',
      'option.female': 'Female',
      'dashboard.leaveBalance': 'Leave Balance',
      'field.leaveType': 'Leave Type',
      'option.selectLeaveType': 'Select Leave Type',
      'option.earnedLeave': 'Earned Leave',
      'option.casualLeave': 'Casual Leave',
      'option.maternityLeave': 'Maternity Leave',
      'option.hospitalLeave': 'Hospital Leave',
      'option.specialDisabilityLeave': 'Special Disability Leave',
      'option.extraordinaryLeave': 'Extraordinary Leave',
      'option.specialCasualLeave': 'Special Casual Leave',
      'option.restrictedHolidays': 'Restricted Holidays',
      'field.openingBalance': 'Opening Balance',
      'field.closingBalance': 'Closing Balance',
      'field.entryDate': 'Entry Date',
      'button.update': 'Update',
      'button.save': 'Save',
      'button.cancel': 'Cancel'
    };

    return translations[key] || key;
  }

  loadApprovedEmployeeNames(): void {
    this.dashboardService.getApprovedEmployeeNames().subscribe({
      next: (data: any[]) => {
        if (data && Array.isArray(data)) {
          this.employeeOptions = data.map(employee => ({
            id: employee.id,
            name: employee.employeeName || employee.name, // Handle both employeeName and name
            selected: false
          }));
        } else {
          console.warn('Invalid data format received:', data);
          // Fallback with sample data for testing
          this.employeeOptions = [
            { id: 1, name: 'John Doe', selected: false },
            { id: 2, name: 'Jane Smith', selected: false },
            { id: 3, name: 'Mike Johnson', selected: false }
          ];
        }
      },
      error: (error) => {
        console.error('Error loading approved employee names:', error);
        // Fallback with sample data for testing
        this.employeeOptions = [
          { id: 1, name: 'John Doe', selected: false },
          { id: 2, name: 'Jane Smith', selected: false },
          { id: 3, name: 'Mike Johnson', selected: false }
        ];
      }
    });
  }

  onEmployeeSelectionChange(selectedEmployees: MultiSelectOption[], serviceEntryIndex: number): void {
    this.selectedEmployees = selectedEmployees;
    console.log('Selected employees for involved persons:', selectedEmployees);
    console.log('Service entry index:', serviceEntryIndex);

    // Extract employee IDs for the involved persons array
    const employeeIds = selectedEmployees.map(emp => emp.id);

    // Update the specific service entry's involvedPersons field
    const serviceEntries = this.serviceEntries;
    if (serviceEntries.length > serviceEntryIndex) {
      const targetServiceEntry = serviceEntries.at(serviceEntryIndex);
      if (targetServiceEntry) {
        targetServiceEntry.patchValue({
          involvedPersons: employeeIds
        });
        console.log('Updated service entry with involved persons:', employeeIds);
      }
    }
  }

  getSelectedEmployeeNames(): string {
    return this.selectedEmployees.map(emp => emp.name).join(', ');
  }

  getInvolvedPersonNames(involvedPersonIds: number[]): string {
    if (!involvedPersonIds || involvedPersonIds.length === 0) {
      return 'No persons selected';
    }

    const names = involvedPersonIds.map(id => {
      const employee = this.employeeOptions.find(emp => emp.id === id);
      return employee ? employee.name : `ID: ${id}`;
    });

    return names.join(', ');
  }
}
