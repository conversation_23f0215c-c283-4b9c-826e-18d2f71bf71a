.multi-select-container {
  position: relative;
  width: 100%;
  z-index: auto;
}

.multi-select-dropdown {
  position: relative;
  width: 100%;
  cursor: pointer;
}

.multi-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  background-color: #fff;
  min-height: 38px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.multi-select-trigger:hover {
  border-color: #86b7fe;
}

.multi-select-dropdown.open .multi-select-trigger {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.multi-select-dropdown.open {
  z-index: 99999;
  position: relative;
  transform: translateZ(0);
}

.multi-select-text {
  flex: 1;
  color: #495057;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multi-select-arrow {
  margin-left: 0.5rem;
  transition: transform 0.2s ease-in-out;
  color: #6c757d;
}

.multi-select-arrow.rotated {
  transform: rotate(180deg);
}

.multi-select-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  z-index: 999999 !important;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 100%;
  transform: translateZ(0);
  will-change: transform;
}

.multi-select-search-container {
  position: relative;
  padding: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.multi-select-search {
  padding-right: 2rem;
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
}

.multi-select-actions {
  padding: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
}

.multi-select-options {
  flex: 1;
  overflow-y: auto;
  max-height: 200px;
}

.multi-select-option {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.multi-select-option:hover {
  background-color: #f8f9fa;
}

.multi-select-option .form-check {
  margin-bottom: 0;
}

.multi-select-option .form-check-input {
  cursor: pointer;
}

.multi-select-option .form-check-label {
  cursor: pointer;
  width: 100%;
  padding-left: 0.25rem;
}

.multi-select-no-results {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.multi-select-no-results i {
  display: block;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.multi-select-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999998;
  background: transparent;
}

/* Custom scrollbar for options */
.multi-select-options::-webkit-scrollbar {
  width: 6px;
}

.multi-select-options::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.multi-select-options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.multi-select-options::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles */
.multi-select-trigger:focus-within {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}



/* Responsive adjustments */
@media (max-width: 576px) {
  .multi-select-actions {
    flex-direction: column;
  }

  .multi-select-actions .btn {
    margin-bottom: 0.25rem;
  }
}
