import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { Employee } from '../form-fill/employee.model';

@Component({
  selector: 'app-approved',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './approved.component.html',
  styleUrl: './approved.component.css'
})
export class ApprovedComponent implements OnInit {

  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  searchTerm: string = '';
  selectedEmployee: Employee | null = null;
  selectedEmployeePDFs: File[] = [];

  constructor(
    private employeeService: EmployeeService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadEmployees();
  }

  loadEmployees(): void {
    // Load approved employees from server
    this.employeeService.loadApprovedEmployees().subscribe({
      next: (employees) => {
        console.log('Loaded approved employees from server:', employees);
        this.employees = employees;
        this.filteredEmployees = [...this.employees];
      },
      error: (error) => {
        console.error('Failed to load approved employees from server:', error);
        // Fallback to localStorage data
        // const localEmployees = this.employeeService.loadEmployeesFromLocalStorage();
        // this.employees = localEmployees.filter(emp => emp.status === 'approved');
        // this.filteredEmployees = [...this.employees];
      }
    });
  }

  searchEmployees(): void {
    if (!this.searchTerm.trim()) {
      this.filteredEmployees = [...this.employees];
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase();
    this.filteredEmployees = this.employees.filter(employee =>
      employee.employeeName.toLowerCase().includes(searchTermLower) ||
      employee.designation.toLowerCase().includes(searchTermLower) ||
      employee.Authority.toLowerCase().includes(searchTermLower) ||
      employee.district.toLowerCase().includes(searchTermLower) ||
      employee.location.toLowerCase().includes(searchTermLower) ||
      employee.empId.toLowerCase().includes(searchTermLower)
    );
  }

  viewEmployee(employee: Employee): void {
    this.selectedEmployee = employee;
  }

  refreshData(): void {
    this.loadEmployees();
    this.searchTerm = ''; // Clear search term
  }

  // PDF related methods
  viewPDFs(employee: Employee): void {
    this.selectedEmployee = employee;
    // For now, we'll simulate PDF files since we don't have actual file storage
    // In a real application, you would fetch the PDFs from a server or localStorage
    this.selectedEmployeePDFs = this.generateSamplePDFs(employee.empId);
  }

  private generateSamplePDFs(empId: string): File[] {
    // Generate sample PDF files for demonstration
    const samplePDFs = [
      new File([''], `${empId}_Resume.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_Certificate.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_ID_Proof.pdf`, { type: 'application/pdf' })
    ];

    // Add some realistic file sizes
    Object.defineProperty(samplePDFs[0], 'size', { value: 245760 }); // 240 KB
    Object.defineProperty(samplePDFs[1], 'size', { value: 512000 }); // 500 KB
    Object.defineProperty(samplePDFs[2], 'size', { value: 1048576 }); // 1 MB

    return samplePDFs;
  }

  openPDF(file: File): void {
    // In a real application, you would have the actual file content
    // For demonstration, we'll show an alert
    alert(`Opening PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would open the PDF file.`);

    // If you had actual file content, you would do:
    // const fileURL = URL.createObjectURL(file);
    // window.open(fileURL, '_blank');
  }

  downloadPDF(file: File): void {
    // In a real application, you would download the actual file
    alert(`Downloading PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would download the PDF file.`);

    // If you had actual file content, you would do:
    // const fileURL = URL.createObjectURL(file);
    // const link = document.createElement('a');
    // link.href = fileURL;
    // link.download = file.name;
    // link.click();
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
