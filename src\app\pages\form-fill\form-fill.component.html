<!-- <form [formGroup]="employeeForm" (ngSubmit)="onSubmit()">
  <div class="card wide-card">
    <div class="card-body">
      <h2>{{ isEditMode ? 'Edit Employee Details' : 'Employee Details' }}</h2>
      <div class="row">
        <div class="col-md-3">
          <label class="required-label">Emp Id/Ecpf.No</label>
          <input type="text" class="form-control" formControlName="empId" required>
        </div>
        <div class="col-md-3">
          <label class="required-label">Employee Name</label>
          <input type="text" class="form-control" formControlName="employeeName" required>
        </div>
        <div class="col-md-3">
          <label class="required-label">Father's Name</label>
          <input type="text" class="form-control" formControlName="fatherName" required>
        </div>
        <div class="col-md-3">
          <label class="required-label">Date of Birth</label>
          <input type="date" class="form-control" formControlName="dateOfBirth" required>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-3">
          <label class="required-label">Religion</label>
          <input type="text" class="form-control" formControlName="religion" required>
        </div>
        <div class="col-md-3">
          <label class="required-label">Community</label>
          <input type="text" class="form-control" formControlName="community" required>
        </div>
        <div class="col-md-3">
          <label class="required-label">Category</label>
          <select class="form-control" formControlName="category" required>
            <option value="" disabled selected>Select Category</option>
            <option>SC</option>
            <option>ST</option>
            <option>BC</option>
            <option>OC</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="required-label">Height (cm)</label>
          <input type="number" class="form-control" formControlName="height" required>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <label class="required-label">Personal Identification Marks</label>
          <input type="text" class="form-control" formControlName="personalIdentificationMarks" placeholder="Mark 1 and Mark 2" required>
        </div>
        <div class="col-md-6">
          <label class="required-label">Department / Authority</label>
          <input type="text" class="form-control" formControlName="departmentAuthority" required>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <label class="required-label">Designation</label>
          <input type="text" class="form-control" formControlName="designation" required>
        </div>
        <div class="col-md-6">
          <label class="required-label">Date of Entry into Service</label>
          <input type="date" class="form-control" formControlName="dateOfEntryIntoService" required>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <label class="required-label">Educational Qualification</label>
          <input type="text" class="form-control" formControlName="educationalQualification" required>
        </div>
        <div class="col-md-6">
          <label class="required-label">Mobile</label>
          <input type="tel" class="form-control" formControlName="mobile" maxlength="15" required>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-12">
          <label class="required-label">Address</label>
          <textarea class="form-control" formControlName="address" rows="2" required></textarea>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-6">
          <label class="required-label">Location</label>
          <input type="text" class="form-control" formControlName="personalIdentificationMarks" placeholder="Enter the location" required>
        </div>
        </div>
    </div>
  </div>
 


  <div class="card wide-card mt-4">
    <div class="card-body">
      <h5 class="mb-3">Upload PDF Files</h5>
      <div class="upload-box text-center p-4 position-relative border border-dashed rounded"
           (dragover)="onDragOver($event)"
           (dragleave)="onDragLeave($event)"
           (drop)="onDrop($event)"
           [class.drag-over]="isDragOver">
        <div class="upload-icon mb-2">
          <i class="bi bi-file-earmark-pdf" style="font-size: 40px; color: #dc3545;"></i>
        </div>
        <p class="text-muted mb-2">Drag and drop PDF files here or click to browse</p>
        <input type="file"
               class="form-control upload-input d-none"
               #fileInput
               accept=".pdf"
               multiple
               (change)="onFileSelected($event)">
        <button type="button" class="btn btn-outline-primary" (click)="fileInput.click()">
          <i class="bi bi-upload"></i> Choose PDF Files
        </button>
      </div>

      Display uploaded files -->
      <!-- <div class="uploaded-files mt-3" *ngIf="uploadedFiles.length > 0">
        <h6>Uploaded Files:</h6>
        <div class="row">
          <div class="col-md-3 mb-2" *ngFor="let file of uploadedFiles; let i = index">
            <div class="pdf-file-card border rounded p-2 text-center position-relative">
              <button type="button" class="btn-close position-absolute top-0 end-0 m-1"
                      (click)="removeFile(i)"
                      title="Remove file"></button>
              <button type="button" class="btn btn-sm btn-outline-secondary mt-1" style="background-color: #007bff; color: white;" (click)="compressFile(i)">Compress</button>

              <div class="pdf-icon mb-2" (click)="openPDF(file)" style="cursor: pointer;">
                <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 30px;"></i>
              </div>
              <small class="text-muted d-block text-truncate" [title]="file.name">{{ file.name }}</small>
              <small class="text-muted">{{ formatFileSize(file.size) }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>Service History Track</h2>
      <div formArrayName="serviceEntries">
        <div *ngFor="let entry of serviceEntries.controls; let i = index" [formGroupName]="i" class="row service-entry mt-2">
          <div class="col-md-4">
            <label class="required-label">Date</label>
            <input type="date" class="form-control" formControlName="date" required>
          </div>
          <div class="col-md-4">
            <label class="required-label">Type</label>
            <select class="form-control" formControlName="type" required>
              <option>Promotion</option>
              <option>Transfer</option>
              <option>Punishment</option>
              <option>Increment</option>
              <option>Loan Recoveries</option>
              <option>Retirement</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="required-label">Status</label>
            <select class="form-control" formControlName="status" required>
              <option>In Progress</option>
              <option>Completed</option>
            </select>
          </div>
          <div class="col-md-1 d-flex align-items-end">
            <button type="button" class="btn btn-danger btn-sm" (click)="removeServiceEntry(i)">-</button>
            <button type="button" class="btn btn-success btn-sm mt-2" (click)="addServiceEntry()">+</button>
          </div>
        </div>
      </div>
    </div>
  </div>

   <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>Leave Balance Information</h2>
      <div formArrayName="leaveEntries">
        <div *ngFor="let entry of leaveEntries.controls; let i = index" [formGroupName]="i" class="row leave-entry mt-2">
          <div class="col-md-5">
            <label class="required-label">Leave Type</label>
            <select class="form-control" formControlName="leaveType" required>
              <option value="" disabled selected>Select Leave Type</option>
              <option>Earned Leave</option>
              <option>Casual Leave</option>
              <option>Maternity Leave</option>
              <option>Hospital Leave</option>
              <option>Special Disability Leave</option>
              <option>Extraordinary Leave</option>
              <option>Special Casual Leave</option>
              <option>Restricted Holidays</option>
            </select>
          </div>
          <div class="col-md-5">
            <label class="required-label">Leave Balance Count</label>
            <input type="number" class="form-control" formControlName="leaveBalanceCount" min="0" required placeholder="Enter count">
          </div>
          <div class="col-md-2 d-flex align-items-end">
            <button type="button" class="btn btn-danger btn-sm" (click)="removeLeaveEntry(i)">-</button>
            <button type="button" class="btn btn-success btn-sm mt-2" (click)="addLeaveEntry()">+</button>
          </div>
        </div>
      </div>
    </div>
  </div> 
  
  
  
  
  <div class="text-center mt-4 mb-5">
    <button type="submit" class="btn btn-primary btn-lg">
      {{ isEditMode ? 'Update Employee' : 'Submit' }}
    </button>
    <button type="button" class="btn btn-secondary btn-lg ms-2" (click)="router.navigate(['/view'])">
      Cancel
    </button>
  </div>
</form> --> 
