import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { UserserviceService } from '../../services/userservice.service';

@Component({
  selector: 'app-user-register',
  imports: [ ReactiveFormsModule, CommonModule],
  templateUrl: './user-register.component.html',
  styleUrl: './user-register.component.css'
})
export class UserRegisterComponent {
  registrationForm!: FormGroup;
  showPassword = false;
  showConfirmPassword = false;
  isSubmitting = false;
  submitMessage = '';
  submitSuccess = false;
  roleList: any;

regionList: string[] = [
  'Ariyalur',
  'Chengalpattu',
  'Chennai(North)',
  'Chennai(South)',
  'Coimbatore',
  'Cuddalore',
  'Dharmapuri',
  'Dindigul',
  'Erode',
  'Kalla<PERSON>richi',
  'Kancheepuram',
  'Karur',
  'Krishnagiri',
  'Madurai',
  'Mayiladuthurai',
  'Nagapattinam',
  'Namakkal',
  'Nilgiris',
  'Perambalur',
  'Pudukkottai',
  'Ramanathapuram',
  'Ranipet',
  'Salem',
  'Sivaganga',
  'Tenkasi',
  'Thanjavur',
  'Theni',
  'Thoothukudi',
  'Tiruchirappalli',
  'Tirunelveli',
  'Tirupathur',
  'Tiruppur',
  'Tiruvallur',
  'Tiruvannamalai',
  'Tiruvarur',
  'Vellore',
  'Viluppuram',
  'Virudhunagar'
];

  constructor(
    private fb: FormBuilder,
    private registrationService: UserserviceService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  
    // this.registrationForm.get('roleId')?.valueChanges.subscribe(roleId => {
    //   const regionControl = this.registrationForm.get('region');
    //   const selectedRole = this.roleList.find(r => r.id === roleId)?.role;
  
    //   if (selectedRole === 'OPERATOR' || selectedRole === 'USER') {
    //     regionControl?.clearValidators();
    //     regionControl?.setValue(null);
    //     regionControl?.disable(); // Optionally disable the control
    //   } else {
    //     regionControl?.setValidators(Validators.required);
    //     regionControl?.enable(); // Ensure the control is enabled
    //   }
    //   regionControl?.updateValueAndValidity();
    // });
  
    this.getRoleList();
  }
  

  private initializeForm(): void {
    this.registrationForm = this.fb.group({
      username: ['', [
        Validators.required,
        Validators.minLength(3),
        Validators.pattern(/^[a-zA-Z0-9_]+$/)
      ]],
      name: ['', [
        Validators.required,
        Validators.minLength(3),
        Validators.pattern(/^[a-zA-Z ]+$/)
      ]],
      roleId: ['', Validators.required],
      region: [''], // Initially set as required
      email: ['', [Validators.required,Validators.email
      ]],
      mobile: ['', [Validators.required, Validators.pattern(/^[6-9]\d{9}$/)]],
      password: [''],
      confirmPassword: ['']
    }, { 
      validators: this.passwordMatchValidator 
    });
  }

  // Custom validator for password matching
  private passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');
    
    if (!password || !confirmPassword) {
      return null;
    }
    
    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  // Check if field is invalid and touched
  isFieldInvalid(fieldName: string): boolean {
    const field = this.registrationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Toggle password visibility
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  // Toggle confirm password visibility
  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  // Handle form submission
  onSubmit(): void {
    if (this.registrationForm.valid) {
      this.isSubmitting = true;
      this.submitMessage = '';
      
      // const formData = {
      //   userName: this.registrationForm.value.userName,
      //   userRole: this.registrationForm.value.userRole,
      //   userEmail: this.registrationForm.value.userEmail,
      //   password: this.registrationForm.value.password
      // };

      console.log(this.registrationForm)

      this.registrationService.createUser(this.registrationForm.value).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.submitSuccess = true;
          this.submitMessage = 'Registration successful! Welcome aboard.';
          this.registrationForm.reset();
          console.log('Registration successful:', response);
        },
        error: (error) => {
          this.isSubmitting = false;
          this.submitSuccess = false;
          this.submitMessage = error.message || 'Registration failed. Please try again.';
          console.error('Registration error:', error);
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.registrationForm.controls).forEach(key => {
        this.registrationForm.get(key)?.markAsTouched();
      });
    }
  }

   getRoleList(){
    this.registrationService.roleList().subscribe(res => {
      this.roleList = res;
    });
  }

  // Get form control for easy access in template
  get f() {
    return this.registrationForm.controls;
  }

}
