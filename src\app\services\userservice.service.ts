import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UserserviceService {

 baseUrl: string;
roleUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = environment.USER_DATA;
    this.roleUrl = environment.ROLE;
  }

  createUser(data:any): Observable<any>{
    return this.http.post<any>(`${this.baseUrl}/userCreate`,data)
  }

  listByPage(pageNumber: number, pageSize: number = 10): Observable<any> {
    // pageNumber is 0-based (0, 1, 2, 3...) as expected by Spring Data PageRequest.of()
    return this.http.get(`${this.baseUrl}/allList?pagesize=${pageSize}&start=${pageNumber}`);
  }
   list(): Observable<any> {
    return this.http.get(`${this.baseUrl}/list`);
  }

  createrole(data:any):Observable<any>{
    return this.http.post<any>(`${this.roleUrl}/roleCreate`,data)
  }

  roleList(): Observable<any> {
    return this.http.get(`${this.roleUrl}/roleList`);
  }

  userList(): Observable<any> {
    return this.http.get(`${this.baseUrl}/listByRole`);
  }

  deleteUser(id: number): Observable<any> {
    return this.http.get(`${this.baseUrl}/delete/${id}`);
  }

  updateUser(id: number, data: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/update/${id}`, data);
  }

  view(id:any): Observable<any> {
    return this.http.get(`${this.baseUrl}/view/${id}`);
  }
}
