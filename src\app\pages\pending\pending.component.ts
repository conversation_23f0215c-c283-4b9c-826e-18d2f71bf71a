import { Component } from '@angular/core';
import { Employee } from '../form-fill/employee.model';
import { EmployeeService } from '../../services/employee.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { catchError, Observable, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-pending',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './pending.component.html',
  styleUrl: './pending.component.css'
})
export class PendingComponent {

    employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  searchTerm: string = '';
  selectedEmployee: Employee | null = null;
  selectedEmployeePDFs: File[] = [];
  selectedEmployeeDetails: any = null;
  isLoadingDetails: boolean = false;
  rejectionRemarks: string;
  isRejectionModalOpen: boolean;

  constructor(
    private employeeService: EmployeeService,
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.loadEmployees();
  }

  loadEmployees(): void {
    // Load pending employees from server
    this.employeeService.loadPendingEmployees().subscribe({
      next: (employees) => {
        console.log('Loaded pending employees from server:', employees);
        this.employees = employees;
        this.filteredEmployees = [...this.employees];
      },
      error: (error) => {
        console.error('Failed to load pending employees from server:', error);
        // Fallback to localStorage data
        // const localEmployees = this.employeeService.loadEmployeesFromLocalStorage();
        // this.employees = localEmployees.filter(emp => emp.status === 'pending' || !emp.status);
        // this.filteredEmployees = [...this.employees];
      }
    });
  }
  rejectEmployee(employee: Employee): void {
    if (confirm(`Are you sure you want to reject ${employee.employeeName}?`)) {
      const remarks = prompt(`Please provide remarks for rejecting ${employee.employeeName}:`, '');

      // Check if remarks were provided
      if (remarks === null) {
        // User cancelled the prompt
        return;
      }

      if (!remarks.trim()) {
        alert('Remarks are required for rejection. Please try again.');
        return;
      }

      this.employeeService.rejectEmployee(employee, remarks).subscribe({
        next: (response) => {
          console.log('Employee rejected successfully:', response);
          alert(`${employee.employeeName} has been rejected successfully!`);
          this.loadEmployees(); // Refresh the list
        },
        error: (error) => {
          console.error('Error rejecting employee:', error);
          alert(`Failed to reject ${employee.employeeName}. Please try again.`);
        }
      });
    }
  }
  searchEmployees(): void {
    if (!this.searchTerm.trim()) {
      this.filteredEmployees = [...this.employees];
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase();
    this.filteredEmployees = this.employees.filter(employee =>
      employee.employeeName.toLowerCase().includes(searchTermLower) ||
      employee.designation.toLowerCase().includes(searchTermLower) ||
      employee.Authority.toLowerCase().includes(searchTermLower) ||
      employee.district.toLowerCase().includes(searchTermLower) ||employee.location.toLowerCase().includes(searchTermLower) ||
      employee.empId.toLowerCase().includes(searchTermLower)
    );
  }

  viewEmployee(employee: Employee): void {
    this.selectedEmployee = employee;
    this.selectedEmployeeDetails = null;
    this.isLoadingDetails = true;

    // Call API to get detailed employee information
    this.employeeService.getEmployeeDetails(employee.id).subscribe({
      next: (details) => {
        console.log('Employee details loaded:', details);
        this.selectedEmployeeDetails = details;
        this.isLoadingDetails = false;
      },
      error: (error) => {
        console.error('Error loading employee details:', error);
        this.isLoadingDetails = false;
        // Keep the basic employee data if API fails
        alert('Failed to load detailed employee information. Showing basic details only.');
      }
    });
  }

  viewRemarks(employee: Employee): void {
    this.selectedEmployee = employee;
  }





  editEmployee(employee: Employee): void {
    this.router.navigate(['/dashboard'], {
      queryParams: { edit: true, empId: employee.empId }
    });
  }

  deleteEmployee(employee: Employee): void {
    Swal.fire({
      title: 'Delete Employee',
      text: `Are you sure you want to delete ${employee.employeeName}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Delete!',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        const employeeId = employee.id || employee.empId;

        // Show loading
        Swal.fire({
          title: 'Deleting...',
          text: 'Please wait while we delete the employee record',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        // Try to delete from API first
        this.http.delete(`http://localhost:8082/employee/deleteEmp/${employeeId}`)
          .subscribe({
            next: (response) => {
              console.log('Employee deleted successfully from API:', response);

              Swal.fire({
                title: 'Deleted!',
                text: `${employee.employeeName} has been deleted successfully!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              }).then(() => {
                this.loadEmployees(); // Refresh the list
              });
            },
            error: (error) => {
              console.error('Error deleting employee from API:', error);

              // Fallback to localStorage deletion
              this.employeeService.deleteEmployee(employee.empId);

              Swal.fire({
                title: 'Deleted!',
                text: `${employee.employeeName} has been deleted from local storage!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              }).then(() => {
                this.loadEmployees(); // Refresh the list
              });
            }
          });
      }
    });
  }

  sendToUser(employee: Employee): void {
    if (confirm(`Are you sure you want to send ${employee.employeeName}'s details to the user?`)) {
      this.employeeService.sendToUser(employee).subscribe({
        next: (response) => {
          console.log('Employee sent to user successfully:', response);
          alert(`${employee.employeeName}'s details have been sent to the user successfully!`);
          this.loadEmployees(); // Refresh the list to get updated data
        },
        error: (error) => {
          console.error('Error sending employee to user:', error);
          alert(`Failed to send ${employee.employeeName}'s details to the user. Please try again.`);
        }
      });
    }
  }

  approveEmployee(employee: Employee): void {
    if (confirm(`Are you sure you want to approve ${employee.employeeName}?`)) {
      this.employeeService.approveEmployee(employee).subscribe({
        next: (response) => {
          console.log('Employee approved successfully:', response);
          alert(`${employee.employeeName} has been approved successfully!`);
          this.loadEmployees(); // Refresh the list
        },
        error: (error) => {
          console.error('Error approving employee:', error);
          alert(`Failed to approve ${employee.employeeName}. Please try again.`);
        }
      });
    }
  }

  // Navigation methods
  addNewEmployee(): void {
    this.router.navigate(['/dashboard']);
  }

  refreshData(): void {
    this.loadEmployees();
    this.searchTerm = ''; // Clear search term
  }

  // PDF related methods
  viewPDFs(employee: Employee): void {
    this.selectedEmployee = employee;
    // For now, we'll simulate PDF files since we don't have actual file storage
    // In a real application, you would fetch the PDFs from a server or localStorage
    this.selectedEmployeePDFs = this.generateSamplePDFs(employee.empId);
  }

  private generateSamplePDFs(empId: string): File[] {
    // Generate sample PDF files for demonstration
    const samplePDFs = [
      new File([''], `${empId}_Resume.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_Certificate.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_ID_Proof.pdf`, { type: 'application/pdf' })
    ];

    // Add some realistic file sizes
    Object.defineProperty(samplePDFs[0], 'size', { value: 245760 }); // 240 KB
    Object.defineProperty(samplePDFs[1], 'size', { value: 512000 }); // 500 KB
    Object.defineProperty(samplePDFs[2], 'size', { value: 1048576 }); // 1 MB

    return samplePDFs;
  }

  openPDF(file: File): void {
    // In a real application, you would have the actual file content
    // For demonstration, we'll show an alert
    alert(`Opening PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would open the PDF file.`);

    // If you had actual file content, you would do:
    // const fileURL = URL.createObjectURL(file);
    // window.open(fileURL, '_blank');
  }

  downloadPDF(file: File): void {
    // In a real application, you would download the actual file
    alert(`Downloading PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would download the PDF file.`);

    // If you had actual file content, you would do:
    // const fileURL = URL.createObjectURL(file);
    // const link = document.createElement('a');
    // link.href = fileURL;
    // link.download = file.name;
    // link.click();
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

}
