<div class="mt-4">
  <h2>Employee Details</h2>

  <!-- Employee count display -->

<div class="row mb-3">
  <div class="col-md-6 d-flex align-items-center">
    <input type="text" class="form-control me-2" placeholder="Search employees..." [(ngModel)]="searchTerm" (input)="searchEmployees()">
    <button class="btn btn-outline-secondary flex-shrink-0" type="button" (click)="searchEmployees()" title="Search">
      <i class="bi bi-search"></i> Search
    </button>
  </div>
  <!-- <div class="col-md-6 text-end">
    <button class="btn btn-outline-info me-2" type="button" (click)="refreshData()" [disabled]="isLoading" title="Refresh">
      <i class="bi bi-arrow-clockwise" [class.spin]="isLoading"></i>
      <span *ngIf="!isLoading">Refresh</span>
      <span *ngIf="isLoading">Loading...</span>
    </button>
    <button class="btn btn-success" (click)="addNewEmployee()" title="Add New Employee">
      <i class="bi bi-plus-circle"></i> Add Employee
    </button>
  </div> -->
</div>
  <div class="table-responsive">
    <table class="table table-striped table-bordered">
      <thead>
        <tr >
          <th class="th-color">ECPF Number</th>
          <th class="th-color">Employee Name</th>
          <th class="th-color">Designation</th>
          <th class="th-color">District</th>
          <th class="th-color">Date of Operator Entry</th>
          <th class="th-color">Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Loading state -->
        <tr *ngIf="isLoading">
          <td colspan="5" class="text-center">
            <div class="d-flex justify-content-center align-items-center py-3">
              <div class="spinner-border text-primary me-2" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <span>Loading employees from API...</span>
            </div>
          </td>
        </tr>

        <!-- Employee data -->
        <tr *ngFor="let employee of paginatedEmployees; let i = index" [hidden]="isLoading">
          <td>{{ employee.ecpfNumber }}</td>
          <td>{{ employee.employeeName }}</td>
          <td>{{ employee.designation }}</td>
          <td>{{ employee.district }}</td>
          <td>{{ employee.createdAt | date:'dd/MM/yyyy' }}</td>

          <td>
            <button class="btn btn-sm btn-outline-primary me-1" data-toggle="modal" data-target="#employeeModal" (click)="viewEmployee(employee)" title="View">
              <i class="bi bi-eye"></i>
            </button>

            <!-- Show approve/reject buttons only when employeeResponseData exists and user can approve/reject -->

            <ng-container *ngIf="role == ''">
              <button class="btn btn-sm btn-success me-1" (click)="approveEmployee(employee)" title="Approve">
                <i class="bi bi-check-circle"></i>
              </button>
              <button class="btn btn-sm btn-danger me-1" (click)="rejectEmployee(employee)" title="Change Request">
                <i class="bi bi-x-circle"></i>
              </button>
            </ng-container>

            <!-- Show edit/PDF/delete buttons only when user has a role (not null) -->
            <ng-container *ngIf="userRole && userRole !== '' && userRole !== 'null'">
              <button class="btn btn-sm btn-outline-warning me-1" (click)="editEmployee(employee)" title="Edit">
                <i class="bi bi-pencil"></i>
              </button>
              <button class="btn btn-sm btn-outline-info me-1" data-toggle="modal" data-target="#pdfModal" (click)="viewPDFs(employee)" title="View PDF Documents">
                <i class="bi bi-file-earmark-pdf"></i>
              </button>
              <button class="btn btn-sm btn-outline-danger" (click)="deleteEmployee(employee)" title="Delete">
                <i class="bi bi-trash"></i>
              </button>
            </ng-container>
          </td>
        </tr>

        <!-- No data state -->
        <tr *ngIf="!isLoading && filteredEmployees.length === 0">
          <td colspan="5" class="text-center">
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              No employees found.
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
      <label class="me-2">Items per page:</label>
      <select class="form-control form-control-sm d-inline-block w-auto"
              [value]="itemsPerPage"
              (change)="onItemsPerPageChange($event)">
        <option value="10">10</option>
        <option value="20">20</option>
        <option value="50">50</option>
      </select>
      <span class="ms-3 text-muted">
        Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to
        {{ Math.min(currentPage * itemsPerPage, totalItems) }} of {{ totalItems }} entries
      </span>
    </div>
    <nav *ngIf="totalPages > 1">
      <ul class="pagination mb-0">
        <li class="page-item" [class.disabled]="currentPage === 1">
          <button class="page-link" (click)="goToPreviousPage()" [disabled]="currentPage === 1">
            Previous
          </button>
        </li>
        <li class="page-item"
            *ngFor="let page of getPageNumbers()"
            [class.active]="page === currentPage">
          <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
        </li>
        <li class="page-item" [class.disabled]="currentPage === totalPages">
          <button class="page-link" (click)="goToNextPage()" [disabled]="currentPage === totalPages">
            Next
          </button>
        </li>
      </ul>
    </nav>
  </div>
  <!-- Employee Details Modal -->
<div class="modal fade" id="employeeModal" tabindex="-1" role="dialog" aria-labelledby="employeeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <div class="d-flex align-items-center w-100">
          <img src="logo_tncsc.png" alt="TNCSC Logo" class="me-3" style="height: 70px; width: auto; max-width: 350px;">
          <div class="flex-grow-1">
            <h5 class="modal-title mb-0" id="employeeModalLabel">Complete Employee Details</h5>
            <small class="text-muted">{{ selectedEmployee?.employeeName }} ({{ selectedEmployee?.ecpfNumber }})</small>
          </div>
        </div>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" *ngIf="selectedEmployee" style="max-height: 70vh; overflow-y: auto;">
        <!-- Personal Information Section -->
        <div class="card mb-3">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Personal Details</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>ECPF Number:</strong> {{ selectedEmployee.ecpfNumber }}</p>
                    <p><strong>Employee Name:</strong> {{ selectedEmployee.employeeName }}</p>
                    <p><strong>Father Name:</strong> {{ selectedEmployee.fatherName }}</p>
                    <p><strong>Mother Name:</strong> {{ selectedEmployee.motherName }}</p>
                    <p><strong>Date of Birth:</strong> {{ selectedEmployee.dateOfBirth | date:'dd/MM/yyyy' }}</p>
                    <p><strong>Religion:</strong> {{ selectedEmployee.religion }}</p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>Community:</strong> {{ selectedEmployee.community }}</p>
                    <p><strong>Caste:</strong> {{ selectedEmployee.caste }}</p>
                    <p><strong>Designation:</strong> {{ selectedEmployee.designation }}</p>
                    <p><strong>District:</strong> {{ selectedEmployee.district }}</p>
                    <p><strong>Date of Entry into Service:</strong> {{ selectedEmployee.dateOfEntryIntoService | date:'dd/MM/yyyy' }}</p>
                    <p><strong>Date of Operator Entry:</strong> {{ selectedEmployee.createdAt | date:'dd/MM/yyyy HH:mm' }}</p>
                    <p><strong>Personal Identification Marks:</strong> {{ selectedEmployee.personalIdentificationMarks }}</p>
                  </div>
                </div>
              </div>
              <div class="col-md-4 text-center">
                <div class="profile-photo-container">
                  <h6 class="text-primary mb-3">Profile Photo</h6>
                  <div class="photo-frame" *ngIf="selectedEmployee.profilePhotoUrl; else noPhoto">
                    <img [src]="selectedEmployee.profilePhotoUrl"
                         alt="Employee Profile Photo"
                         class="img-fluid rounded border"
                         style="max-width: 150px; max-height: 200px; object-fit: cover;"
                         (error)="onImageError($event)"
                         (load)="onImageLoad($event)">
                  </div>
                  <ng-template #noPhoto>
                    <div class="no-photo-placeholder border rounded d-flex align-items-center justify-content-center"
                         style="width: 150px; height: 200px; background-color: #f8f9fa; margin: 0 auto;">
                      <div class="text-center text-muted">
                        <i class="bi bi-person-circle" style="font-size: 3rem;"></i>
                        <p class="small mb-0 mt-2">No Photo Available</p>
                      </div>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="card mb-3">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Contact Information</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Email:</strong> {{ selectedEmployee.email || 'Not provided' }}</p>
                <p><strong>Gender:</strong> {{ selectedEmployee.gender || selectedEmployee.profile?.gender || 'Not provided' }}</p>
                <p><strong>Mobile Number:</strong> {{ selectedEmployee.profile?.mobileNumber || selectedEmployee.mobileNumber || 'Not provided' }}</p>
                <p><strong>Present Address:</strong> {{ getFormattedPresentAddress() }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Permanent Address:</strong> {{ getFormattedPermanentAddress() }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Details Section -->
        <div class="card mb-3">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Account Details</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Bank Account Number:</strong> {{ selectedEmployee.bankAccountNo }}</p>
                <p><strong>IFSC Code:</strong> {{ selectedEmployee.ifscCode }}</p>
                <p><strong>Bank Name:</strong> {{ selectedEmployee.bankName }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>PAN Number:</strong> {{ selectedEmployee.panNumber }}</p>
                <p><strong>UAN Number:</strong> {{ selectedEmployee.uanNumber }}</p>
                <p><strong>Aadhar Number:</strong> {{ selectedEmployee.aadharNumber }}</p>
              </div>
            </div>
          </div>
        </div>



        <!-- Service History Section -->
        <div class="card mb-3" *ngIf="selectedEmployee.serviceEntries && selectedEmployee.serviceEntries.length > 0">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Service Track History</h6>
          </div>
          <div class="card-body">
            <div class="accordion" id="serviceHistoryAccordion">
              <div class="accordion-item mb-2" *ngFor="let service of selectedEmployee.serviceEntries; let i = index">
                <h2 class="accordion-header" [id]="'heading' + i">
                  <button class="accordion-button collapsed" type="button" data-toggle="collapse"
                          [attr.data-target]="'#collapse' + i" aria-expanded="false"
                          [attr.aria-controls]="'collapse' + i">
                    <div class="d-flex justify-content-between w-100 me-3">
                      <span><strong>{{ service.type }}</strong></span>
                      <span class="text-muted">Click to view details</span>
                    </div>
                  </button>
                </h2>
                <div [id]="'collapse' + i" class="collapse"
                     [attr.aria-labelledby]="'heading' + i" data-parent="#serviceHistoryAccordion">
                  <div class="accordion-body">

                    <!-- Appointment Details -->
                    <div *ngIf="service.type === 'Appointment'" class="row">
                      <div class="col-md-6">
                        <p><strong>Appointment Type:</strong> {{ service.appointmentType || 'N/A' }}</p>
                        <p><strong>Mode of Appointment:</strong> {{ service.modeOfAppointment || 'N/A' }}</p>
                        <p><strong>Date of Appointment:</strong> {{ (service.dateOfAppointment | date:'dd/MM/yyyy') || 'N/A' }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Proceeding Order No:</strong> {{ service.proceedingOrderNo || 'N/A' }}</p>
                        <p><strong>Proceeding Order Date:</strong> {{ (service.proceedingOrderDate | date:'dd/MM/yyyy') || 'N/A' }}</p>
                        <p><strong>Status:</strong> <span class="badge bg-success">{{ service.status }}</span></p>
                      </div>
                    </div>

                    <!-- Promotion Details -->
                    <div *ngIf="service.type === 'Promotion'" class="row">
                      <div class="col-md-6">
                        <p><strong>From Designation:</strong> {{ service.fromDesignation || 'N/A' }}</p>
                        <p><strong>Joining Date:</strong> {{ service.joiningDate | date:'dd/MM/yyyy' || 'N/A' }}</p>

                      </div>
                      <div class="col-md-6">
                        <p><strong>Promoted Designation:</strong> {{ service.toPromoted || 'N/A' }}</p>
                        <p><strong>Promoted Date:</strong> {{ service.promotedDate | date:'dd/MM/yyyy' || 'N/A' }}</p>

                        <!-- <p><strong>Status:</strong> <span class="badge bg-primary">{{ service.status }}</span></p> -->
                      </div>
                    </div>

                    <!-- Transfer Details -->
                    <div *ngIf="service.type === 'Transfer'" class="row">
                      <div class="col-md-6">
                        <p><strong>Transfer Order Date:</strong> {{ service.fromDate | date:'dd/MM/yyyy' || 'N/A' }}</p>
                        <p><strong>Current Place:</strong> {{ service.fromPlace || 'N/A' }}</p>

                      </div>
                      <div class="col-md-6">
                        <p><strong>Joining Date:</strong> {{ service.toDate | date:'dd/MM/yyyy' || 'N/A' }}</p>

                        <p><strong>Transfered Place:</strong> {{ service.toPlace || 'N/A' }}</p>
                        <!-- <p><strong>Status:</strong> <span class="badge bg-info">{{ service.status }}</span></p> -->
                      </div>
                    </div>

                    <!-- Increment Details -->
                    <div *ngIf="service.type === 'Increment'" class="row">
                      <div class="col-md-6">
                        <p><strong>Type of Increment:</strong> {{ service.typeOfIncrement || 'N/A' }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Date of Increment:</strong> {{ service.date | date:'dd/MM/yyyy' || 'N/A' }}</p>
                        <!-- <p><strong>Status:</strong> <span class="badge bg-warning">{{ service.status }}</span></p> -->
                      </div>
                    </div>

                    <!-- Deputation Details -->
                    <div *ngIf="service.type === 'Deputation'" class="row">
                      <div class="col-md-6">
                        <p><strong>Designation:</strong> {{ service.designation || 'N/A' }}</p>
                        <p><strong>Original Designation:</strong> {{ service.originalDesignation || 'N/A' }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Parent Department:</strong> {{ service.parentDepartment || 'N/A' }}</p>
                        <!-- <p><strong>Status:</strong> <span class="badge bg-secondary">{{ service.status }}</span></p> -->
                      </div>
                    </div>

                    <!-- Punishment Details -->
                    <div *ngIf="service.type === 'Punishment'" class="row">
                      <div class="col-md-6">
                        <p><strong>Punishment Type:</strong> {{ service.punishmentType || 'N/A' }}</p>
                        <p><strong>Punishment Date:</strong> {{ service.punishmentDate | date:'dd/MM/yyyy' || 'N/A' }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Case Details:</strong> {{ service.caseDetails || 'N/A' }}</p>
                        <!-- <p><strong>Status:</strong> <span class="badge bg-danger">{{ service.status }}</span></p> -->
                      </div>
                    </div>

                    <!-- Case Details -->


                    <!-- Retirement Details -->
                    <div *ngIf="service.type === 'Retirement'" class="row">
                      <div class="col-md-6">
                        <p><strong>Date of Joining:</strong> {{ (service.fromDate | date:'dd/MM/yyyy') || 'N/A' }}</p>
                        <p><strong>Date of Retirement:</strong> {{ (service.toDate | date:'dd/MM/yyyy') || (service.date | date:'dd/MM/yyyy') || 'N/A' }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Designation:</strong> {{ service.designation || service.originalDesignation || 'N/A' }}</p>
                        <p><strong>Status:</strong> <span class="badge bg-dark">{{ service.status }}</span></p>
                      </div>
                    </div>

                    <!-- Case Details - Only show for Punishment category when both case number AND case details exist -->
                    <div *ngIf="service.type === 'Punishment' &&
                                service.caseNumber && service.caseNumber.trim() !== '' &&
                                (service.casedetails || service.caseDetails) &&
                                (service.casedetails?.trim() !== '' || service.caseDetails?.trim() !== '')"
                         class="row mt-3">
                      <div class="col-12">
                        <div class="alert alert-info">
                          <h6 class="alert-heading mb-3">
                            <i class="bi bi-file-text me-2"></i>Case Details
                          </h6>
                          <div class="row">
                            <div class="col-md-4">
                              <p><strong>Case Type:</strong> {{ service.casedetails || service.caseDetails }}</p>
                              <p><strong>Case Number:</strong> {{ service.caseNumber }}</p>
                            </div>
                            <div class="col-md-4">
                              <p><strong>Case Date:</strong> {{ service.caseDate ? (service.caseDate | date:'dd/MM/yyyy') : 'N/A' }}</p>
                              <p><strong>Person Involved:</strong>
                                <span *ngIf="service.involvedPersonsWithNames && service.involvedPersonsWithNames.length > 0; else legacyPersonInvolved">
                                  <span *ngFor="let person of service.involvedPersonsWithNames; let last = last">
                                    {{ person.name }}<span *ngIf="!last">, </span>
                                  </span>
                                </span>
                                <ng-template #legacyPersonInvolved>
                                  {{ service.personInvolved || 'N/A' }}
                                </ng-template>
                              </p>
                            </div>
                            <div class="col-md-4">
                              <p><strong>Present Status:</strong> {{ service.presentStatus || 'N/A' }}</p>
                              <p><strong>Description:</strong> {{ service.description || 'N/A' }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Generic Details for other types -->
                    <div *ngIf="!['Appointment', 'Promotion', 'Transfer', 'Increment', 'Deputation', 'Punishment', 'Retirement'].includes(service.type)" class="row">
                      <div class="col-md-6">
                        <p><strong>Date:</strong> {{ service.date | date:'dd/MM/yyyy' || 'N/A' }}</p>
                        <p><strong>Type:</strong> {{ service.type || 'N/A' }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Designation:</strong> {{ service.designation || service.originalDesignation || 'N/A' }}</p>
                        <!-- <p><strong>Status:</strong> <span class="badge bg-light text-dark">{{ service.status }}</span></p> -->
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Leave Balances Section -->
        <div class="card mb-3" *ngIf="selectedEmployee.leaveEntries && selectedEmployee.leaveEntries.length > 0">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Leave Balance</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-bordered">
                <thead>
                  <tr>
                    <th>Leave Type</th>
                    <th>Opening Balance</th>
                    <th>Closing Balance</th>
                    <th>Entry Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let leave of selectedEmployee.leaveEntries">
                    <td>{{ leave.leaveType }}</td>
                    <td>{{ leave.openingBalance }}</td>
                    <td>{{ leave.closingBalance }}</td>
                    <td>{{ leave.entryDate | date:'dd/MM/yyyy' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Training Details Section -->
        <div class="card mb-3" *ngIf="selectedEmployee.trainingEntries && selectedEmployee.trainingEntries.length > 0">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Training History</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-bordered">
                <thead>
                  <tr>
                    <th>Training Type</th>
                    <th>Training Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let training of selectedEmployee.trainingEntries">
                    <td>{{ training.trainingType }}</td>
                    <td>{{ training.trainingDate | date:'dd/MM/yyyy' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Salary Details Section -->
        <div class="card mb-3" *ngIf="selectedEmployee.salaryDetails">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Salary Details</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <p><strong>Last Salary Revised Date:</strong> {{ (selectedEmployee.salaryDetails.lastSalaryRevisedDate | date:'dd/MM/yyyy') || 'N/A' }}</p>
              </div>
              <div class="col-md-3">
                <p><strong>Group:</strong> {{ selectedEmployee.salaryDetails?.group || 'N/A' }}</p>
              </div>
              <div class="col-md-3">
                <p><strong>Pay Band:</strong> {{ selectedEmployee.salaryDetails?.payband || 'N/A' }}</p>
              </div>
              <div class="col-md-3">
                <p><strong>Grade Pay:</strong> {{ selectedEmployee.salaryDetails?.gradepay || 'N/A' }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Punishment Details Section -->
        <div class="card mb-3" *ngIf="selectedEmployee.punishmentEntries && selectedEmployee.punishmentEntries.length > 0">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Punishment Details</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-bordered">
                <thead>
                  <tr>
                    <th>Punishment Type</th>
                    <th>Punishment Date</th>
                    <th>Case Details</th>
                    <th>Persons Involved</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let punishment of selectedEmployee.punishmentEntries">
                    <td>{{ punishment.punishmentType }}</td>
                    <td>{{ punishment.punishmentDate | date:'dd/MM/yyyy' }}</td>
                    <td>
                      <span *ngIf="punishment.caseNumber && punishment.caseNumber.trim() !== '' &&
                                   punishment.caseDetails && punishment.caseDetails.trim() !== ''; else noCaseDetails">
                        {{ punishment.caseDetails }}
                      </span>
                      <ng-template #noCaseDetails>
                        <span class="text-muted">No case details</span>
                      </ng-template>
                    </td>
                    <td>
                      <span *ngIf="punishment.involvedPersonsWithNames && punishment.involvedPersonsWithNames.length > 0; else noPersons">
                        <span *ngFor="let person of punishment.involvedPersonsWithNames; let last = last">
                          {{ person.name }}<span *ngIf="!last">, </span>
                        </span>
                      </span>
                      <ng-template #noPersons>
                        <span class="text-muted">No persons involved</span>
                      </ng-template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Educational Qualification Section -->
        <div class="card mb-3" *ngIf="selectedEmployee.educationEnties && selectedEmployee.educationEnties.length > 0">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Educational Qualifications</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-bordered">
                <thead>
                  <tr>
                    <th>Qualification</th>
                    <th>School/Institute Name</th>
                    <th>Course Name</th>
                    <th>University Name</th>
                    <th>Specialization</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let education of selectedEmployee.educationEnties">
                    <td>{{ education.qualification }}</td>
                    <td>{{ education.instituteName }}</td>
                    <td>{{ education.courseName || 'N/A' }}</td>
                    <td>{{ education.universityName || 'N/A' }}</td>
                    <td>{{ education.specialization || 'N/A' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Nomination Details Section -->
         <div class="card mb-3" *ngIf="selectedEmployee.nominationEntries && selectedEmployee.nominationEntries.length > 0">
  <div class="card-header">
    <h6 class="mb-0 text-primary">Nomination Details</h6>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-sm table-bordered">
        <thead>
          <tr>
            <th>Photo</th>
            <th>Nominee Name</th>
            <th>Relationship</th>
            <th>Age</th>
            <th>Share %</th>
            <th>Gender</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let nominee of selectedEmployee.nominationEntries; let i = index">
            <td class="text-center">
              <div class="nominee-photo-container">
                <!-- Show photo if available -->
                <div *ngIf="getNomineeDisplayPhotoUrl(i)" class="nominee-photo">
                  <img [src]="getNomineeDisplayPhotoUrl(i)"
                       alt="Nominee Photo"
                       style="width: 80px; height: 100px; border: 2px solid #007bff; border-radius: 8px; cursor: pointer; object-fit: cover; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                       (error)="onNomineeImageError($event, i)"
                       (click)="viewNomineePhoto(i)"
                       (load)="onNomineeImageLoad(i)"
                       title="Click to view larger image">
                </div>

                <!-- Show loading spinner while photo is being loaded -->
                <div *ngIf="!getNomineeDisplayPhotoUrl(i) && !nominee.photoLoadingComplete"
                     class="photo-loading d-flex align-items-center justify-content-center"
                     style="width: 80px; height: 100px;">
                  <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>

                <!-- Show placeholder if no photo available -->
                <div *ngIf="!getNomineeDisplayPhotoUrl(i) && nominee.photoLoadingComplete"
                     class="no-photo-placeholder border rounded d-flex align-items-center justify-content-center"
                     style="width: 80px; height: 100px; background-color: #f8f9fa; border: 2px solid #ddd;">
                  <div class="text-center">
                    <i class="bi bi-person-circle text-muted" style="font-size: 2.5rem;"></i>
                    <p class="small text-muted mb-0 mt-1">No Photo</p>
                  </div>
                </div>
              </div>
            </td>
            <td>{{ nominee.nomineeName }}</td>
            <td>{{ nominee.relationship }}</td>
            <td>{{ nominee.age }}</td>
            <td>{{ nominee.percentageOfShare }}%</td>
            <td>{{ nominee.gender }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

        <!-- <div class="card mb-3" *ngIf="selectedEmployee.nominationEntries && selectedEmployee.nominationEntries.length > 0">
          <div class="card-header">
            <h6 class="mb-0 text-primary">Nomination Details</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-bordered">
                <thead>
                  <tr>
                    <th>Nominee Name</th>
                    <th>Relationship</th>
                    <th>Age</th>
                    <th>Share %</th>
                    <th>Gender</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let nominee of selectedEmployee.nominationEntries; let i = index">
                    <td>{{ nominee.nomineeName }}</td>
                    <td>{{ nominee.relationship }}</td>
                    <td>{{ nominee.age }}</td>
                    <td>{{ nominee.percentageOfShare }}%</td>
                    <td>{{ nominee.gender }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div> -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-success me-2" (click)="exportToPDF()" title="Export to PDF">
          <i class="bi bi-file-earmark-pdf me-1"></i>Export PDF
        </button>
        <button type="button" class="btn btn-warning" (click)="rejectEmployee(selectedEmployee!)" *ngIf="role == '' " data-dismiss="modal">Change for Request</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- PDF Files Modal -->
<div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="pdfModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="pdfModalLabel">Employee PDF Documents ({{ selectedEmployeePDFs?.length || 0 }})</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div *ngIf="selectedEmployeePDFs && selectedEmployeePDFs.length > 0; else noPDFs">
          <div class="row">
            <div class="col-md-4 mb-3" *ngFor="let pdf of selectedEmployeePDFs; let i = index">
              <div class="pdf-card border rounded p-3 text-center h-100">
                <div class="pdf-icon mb-2" (click)="openPDF(pdf)" style="cursor: pointer;">
                  <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                </div>
                <h6 class="pdf-title text-truncate" [title]="pdf.name">{{ pdf.name }}</h6>
                <small class="text-muted">{{ formatFileSize(pdf.size) }}</small>
                <div class="mt-1" *ngIf="pdf.uploadDate">
                  <small class="text-info">Uploaded: {{ pdf.uploadDate | date:'dd/MM/yyyy HH:mm' }}</small>
                </div>
                <div class="mt-2">
                  <button class="btn btn-sm btn-primary" (click)="openPDF(pdf)" title="Open PDF">
                    <i class="bi bi-eye"></i> View
                  </button>
                  <button class="btn btn-sm btn-secondary ms-1"
                          (click)="downloadPDF(pdf)"
                          [disabled]="isDownloading(pdf.id)"
                          title="Download PDF">
                    <span *ngIf="isDownloading(pdf.id)">
                      <i class="bi bi-arrow-clockwise spin"></i> Downloading...
                    </span>
                    <span *ngIf="!isDownloading(pdf.id)">
                      <i class="bi bi-download"></i> Download
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <ng-template #noPDFs>
          <div class="text-center py-4">
            <i class="bi bi-file-earmark-pdf text-muted" style="font-size: 64px;"></i>
            <h5 class="mt-3 text-muted">No PDF Documents Found</h5>
            <p class="text-muted">This employee hasn't uploaded any PDF documents yet, or only photo/signature files are available.</p>
          </div>
        </ng-template>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

</div>
