import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LoginService } from '../../services/auth/login.service';
import { jwtDecode } from 'jwt-decode';
import { CommonModule } from '@angular/common';
import Swal from 'sweetalert2';

interface JwtPayload {
  user_id: string;
  sub: string;
  role: string;
}

@Component({
  selector: 'app-login',
  standalone:true,
  imports: [CommonModule, ReactiveFormsModule], // Import ReactiveFormsModule for formGroup
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'], // Optional, create if needed
})
export class LoginComponent implements OnInit{
 formGroup:FormGroup;
  errorMessage: string = '';
  isLoading: boolean = false;
  showPassword: boolean = false;
userType: string = 'admin'; // Default to admin
  employeeLoginFormGroup: FormGroup;
  loginMessage: string;
  isLoginSuccess: boolean;
  constructor(
    private formBuilder: FormBuilder, private authService:LoginService, private router:Router
  ) {

  }
  ngOnInit(): void {
    this.formGroup = this.formBuilder.group({
      username: ['', Validators.required],
      password: ['', Validators.required]
    })
    this.employeeLoginFormGroup = this.formBuilder.group({
      empId: ['', Validators.required],
      panNumber: ['', Validators.required]
    })
  }

setUserType(type: string) {
  this.userType = type;
}

  onLogin(): void {
    if (this.formGroup.valid) {
      this.isLoading = true;
      this.errorMessage = ''; // Clear previous errors

      const { username, password } = this.formGroup.value;

      this.authService.loginUser(username, password).subscribe(
        (response) => {
          console.log('Full API Response:', response);

          // Handle the actual API response structure
          if (response && response.responseData && response.responseType === 'SUCCESS') {
            console.log('Login successful, token:', response.responseData);
            const token = response.responseData;
            const role = response.role; // Get role from API response

            try {
              // Decode JWT to get user information
              var decoded = jwtDecode<JwtPayload>(token);
              console.log('Decoded JWT:', decoded);

              // Store user information
              sessionStorage.setItem('user_id', decoded.user_id || response.userData);
              sessionStorage.setItem('username', decoded.sub);
              sessionStorage.setItem('role', role);

              localStorage.setItem('username', decoded.sub);
              localStorage.setItem('token', token);
              sessionStorage.setItem('token', token);
              this.authService.setAuthToken(token);

              console.log('Stored role:', role);
              console.log('Navigating based on role...');

              // Navigate based on role
              if (role === 'ADMIN') {
                console.log('Navigating to dashboard-count');
                this.router.navigate(['/app/dashboard-count']).then(
                  (success) => {
                    console.log('Navigation success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              } else if(role === 'OPERATOR') {
                console.log('Navigating to dashboard');
                this.router.navigate(['/app/dashboard']).then(
                  (success) => {
                    console.log('Navigation success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              } else if(role === 'SUPERINTENDENT' || role === 'AM') {
                console.log('Navigating to dashboard');
                this.router.navigate(['/app/pending-list']).then(
                  (success) => {
                    console.log('Navigation success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              } else if(role === 'DRM' || role === 'MA'){
                  console.log('Navigating to dashboard');
                  this.router.navigate(['/app/first-level-list']).then(
                    (success) => {
                      console.log('Navigation success:', success);
                      if (!success) {
                        console.error('Navigation failed - route may not exist');
                      }
                    },
                    (error) => console.error('Navigation error:', error)
                  );
              }else if(role === 'RM'){
                console.log('Navigating to dashboard');
                this.router.navigate(['/app/second-level-list']).then(
                  (success) => {
                    console.log('Navigation success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              } else {
                console.error('No navigation path for this role:', role);
                this.errorMessage = 'Login failed: No navigation path for this role.';
              }
            } catch (jwtError) {
              console.error('JWT decode error:', jwtError);
              this.errorMessage = 'Invalid token received from server.';
            }
          } else {
            console.error('Invalid response structure or login failed:', response);
            this.errorMessage = response?.errorMessage || 'Username or password is incorrect.';
          }
        },
        (error) => {
          console.error('Login failed:', error);
          this.errorMessage = 'Invalid username or password.';
        }
      ).add(() => {
        this.isLoading = false; // Ensure the loading state is reset
      });
    } else {
      this.errorMessage = 'Please fill in all required fields correctly.';
    }
  }


  loginWithEmployee(): void {
    if (this.formGroup.valid) {
      this.isLoading = true;
      this.errorMessage = ''; // Clear previous errors

      const { username, password } = this.formGroup.value;

      this.authService.loginUser(username, password).subscribe(
        (response) => {
          console.log('Full API Response:', response);

          // Handle the actual API response structure
          if (response && response.responseData && response.responseType === 'SUCCESS') {
            console.log('Login successful, token:', response.responseData);
            const token = response.responseData;
            const role = response.role; // Get role from API response

            try {
              // Decode JWT to get user information
              var decoded = jwtDecode<JwtPayload>(token);
              console.log('Decoded JWT:', decoded);

              // Store user information
              sessionStorage.setItem('user_id', decoded.user_id || response.userData);
              sessionStorage.setItem('username', decoded.sub);
              sessionStorage.setItem('role', role);

              localStorage.setItem('username', decoded.sub);
              localStorage.setItem('token', token);
              sessionStorage.setItem('token', token);
              this.authService.setAuthToken(token);

              console.log('Stored role:', role);
              console.log('Navigating based on role...');

              // Navigate based on role
              if (role === 'ADMIN') {
                console.log('Navigating to dashboard-count');
                this.router.navigate(['/app/dashboard-count']).then(
                  (success) => {
                    console.log('Navigation success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              } else {
                console.log('Navigating to dashboard');
                this.router.navigate(['/app/dashboard']).then(
                  (success) => {
                    console.log('Navigation success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              }
            } catch (jwtError) {
              console.error('JWT decode error:', jwtError);
              this.errorMessage = 'Invalid token received from server.';
            }
          } else {
            console.error('Invalid response structure or login failed:', response);
            this.errorMessage = response?.errorMessage || 'Username or password is incorrect.';
          }
        },
        (error) => {
          console.error('Login failed:', error);

          // Check for ACCESS_DENIED response type
          if (error.error?.responseType === 'ACCESS_DENIED') {
            const errorMessage = error.error?.errorMessage || 'Access denied';

            // Show specific SweetAlert for ACCESS_DENIED
            Swal.fire({
              title: 'Access Denied!',
              text: errorMessage,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ffc107'
            }).then(() => {
              // Reload the page after user clicks OK
              window.location.reload();
            });

            this.errorMessage = errorMessage;
            return;
          }

          // Get the error message from API response for other errors
          const apiErrorMessage = error.error?.errorMessage || error.error?.responseData || error.error?.message;

          // Determine the message to display
          let displayMessage: string;
          if (apiErrorMessage && apiErrorMessage !== null) {
            displayMessage = apiErrorMessage;
          } else {
            displayMessage = 'Something went wrong. Please try again.';
          }

          // Show SweetAlert for other error messages
          Swal.fire({
            title: 'Login Failed!',
            text: displayMessage,
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#dc3545'
          });

          this.errorMessage = displayMessage;
        }
      ).add(() => {
        this.isLoading = false; // Ensure the loading state is reset
      });
    } else {
      this.errorMessage = 'Please fill in all required fields correctly.';
    }
  }

   togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  onForgotPassword(event: Event): void {
    event.preventDefault();
    alert('Redirecting to forgot password...');
    // Optionally, navigate to a forgot password route
    // this.router.navigate(['/forgot-password']);
  }

  loginEmployee(){
    if (this.employeeLoginFormGroup.invalid) {
      this.employeeLoginFormGroup.markAllAsTouched();
      return;
    }

    const formData = this.employeeLoginFormGroup.value;

    this.authService.loginEmployee(formData).subscribe({
      next: (res) => {
          console.log('Employee login response:', res);

          // Check for ACCESS_DENIED response type first
          if (res.responseType === 'ACCESS_DENIED') {
            const errorMessage = res.errorMessage || 'Access denied';

            // Show specific SweetAlert for ACCESS_DENIED
            Swal.fire({
              title: 'Access Denied!',
              text: errorMessage,
              icon: 'warning',
              confirmButtonText: 'OK',
              confirmButtonColor: '#ffc107'
            }).then(() => {
              // Reload the page after user clicks OK
              window.location.reload();
            });

            this.loginMessage = errorMessage;
            this.isLoginSuccess = false;
            this.isLoading = false;
            return;
          }

          // Check for LOGIN FAILED response type
          if (res.responseType === 'LOGIN FAILED') {
            const errorMessage = res.responseData || res.errorMessage || 'Invalid credentials';

            // Show specific SweetAlert for LOGIN FAILED
            Swal.fire({
              title: 'Login Failed!',
              text: errorMessage,
              icon: 'error',
              confirmButtonText: 'OK',
              confirmButtonColor: '#dc3545'
            });

            this.loginMessage = errorMessage;
            this.isLoginSuccess = false;
            this.isLoading = false;
            return;
          }

          this.loginMessage = '';
          this.isLoginSuccess = true;
          console.log('Employee Data:', res.responseData);
          const employeeData = res.responseData;
          const role = res.responseData?.role; // Get role from employee data
          const status = res.responseData?.status; // Get status from employee data
          const errMessaage = res.errorMessage;
 if (status === 'approved' || errMessaage === 'Employee is already approved') {
              Swal.fire({
                title: 'Already Approved!',
                text: `Employee record is already approved.`,
                icon: 'info',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745'
              });
              this.isLoading = false;
              return; // Don't proceed with navigation
            } else if (status === 'rejected') {
              Swal.fire({
                title: 'Sent for Change Request!',
                text: `Employee record for is sent for Change Request.`,
                icon: 'warning',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc3545'
              });
              this.isLoading = false;
              return; // Don't proceed with navigation
            }
                        localStorage.setItem('employeeResponseData', JSON.stringify(employeeData));
            localStorage.setItem('role', role || '');
            const ecpfNumber = "";
            localStorage.setItem('username', ecpfNumber);
            localStorage.setItem('employeeId', employeeData.id?.toString() || '');
            localStorage.setItem('employeeName', employeeData.employeeName || '');
            localStorage.setItem('token', 'employee_session'); // Simple session token for employee


            if (role === null || role === undefined || role === '') {
              console.log('Role is null, navigating to view component for approval/rejection');
              console.log('Employee data stored for header display:', employeeData.employeeName);

              // Check if we should skip the login message (after approval/rejection)
              const skipLoginMessage = localStorage.getItem('skipLoginMessage');
              if (skipLoginMessage) {
                localStorage.removeItem('skipLoginMessage');
                // Direct navigation without showing success message
                this.router.navigate(['/app/view']).then(
                  (success) => {
                    console.log('Navigation to view component success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
                return;
              }

              // Show success message for pending status
              if (status === 'pending' || status === 'user approval' || !status || res.responseType == 'LOGIN SUCCESS') {
                Swal.fire({
                  title: 'Login Successful!',
                  text: `Your record is pending approval.`,
                  icon: 'success',
                  confirmButtonText: 'Continue',
                  confirmButtonColor: '#007bff'
                }).then(() => {
                  // Navigate after user clicks OK
                  this.router.navigate(['/app/view']).then(
                    (success) => {
                      console.log('Navigation to view component success:', success);
                      if (!success) {
                        console.error('Navigation failed - route may not exist');
                      }
                    },
                    (error) => console.error('Navigation error:', error)
                  );
                });
              } else {
                // Direct navigation for other statuses
                this.router.navigate(['/app/view']).then(
                  (success) => {
                    console.log('Navigation to view component success:', success);
                    if (!success) {
                      console.error('Navigation failed - route may not exist');
                    }
                  },
                  (error) => console.error('Navigation error:', error)
                );
              }
            }


      },
      error: (err) => {
        console.error('Employee login error:', err);

        // Check for ACCESS_DENIED response type
        if (err.error?.responseType === 'ACCESS_DENIED') {
          const errorMessage = err.error?.errorMessage || 'Access denied';

          // Show specific SweetAlert for ACCESS_DENIED
          Swal.fire({
            title: 'Access Denied!',
            text: errorMessage,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#ffc107'
          }).then(() => {
            // Reload the page after user clicks OK
            window.location.reload();
          });

          this.loginMessage = errorMessage;
          this.isLoginSuccess = false;
          return;
        }

        // Get the error message from API response for other errors
        const apiErrorMessage = err.error?.errorMessage || err.error?.responseData || err.error?.message;

        // Determine the message to display
        let displayMessage: string;
        if (apiErrorMessage && apiErrorMessage !== null) {
          displayMessage = apiErrorMessage;
        } else {
          displayMessage = 'Something went wrong. Please try again.';
        }

        // Show SweetAlert for other error messages
        Swal.fire({
          title: 'Login Failed!',
          text: displayMessage,
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });

        this.loginMessage = displayMessage;
        this.isLoginSuccess = false;
      }
    });
  }

  isFieldInvalid(field: string): boolean {
    const control = this.employeeLoginFormGroup.get(field);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }
}
