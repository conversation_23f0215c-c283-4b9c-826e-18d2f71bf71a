import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ElementRef, ViewChild, Renderer2, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface MultiSelectOption {
  id: string | number;
  name: string;
  selected?: boolean;
}

@Component({
  selector: 'app-multi-select-dropdown',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './multi-select-dropdown.component.html',
  styleUrls: ['./multi-select-dropdown.component.css']
})
export class MultiSelectDropdownComponent implements OnInit, OnChanges, OnDestroy {
  @Input() options: MultiSelectOption[] = [];
  @Input() placeholder: string = 'Select involved persons...';
  @Input() label: string = 'Involved Persons';
  @Input() selectedValues: (string | number)[] = [];
  @Output() selectionChange = new EventEmitter<MultiSelectOption[]>();

  filteredOptions: MultiSelectOption[] = [];
  searchTerm: string = '';
  isDropdownOpen: boolean = false;
  selectedCount: number = 0;

  @ViewChild('triggerElement', { static: false }) triggerElement!: ElementRef;
  private dropdownElement: HTMLElement | null = null;
  private scrollListener: (() => void) | null = null;
  private resizeListener: (() => void) | null = null;
  private clickListener: ((event: Event) => void) | null = null;
  private keyListener: ((event: KeyboardEvent) => void) | null = null;

  constructor(private renderer: Renderer2, private elementRef: ElementRef) {}

  ngOnInit(): void {
    this.initializeOptions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['options'] || changes['selectedValues']) {
      this.initializeOptions();
    }
  }

  ngOnDestroy(): void {
    this.removeDropdownFromBody();
    this.removeEventListeners();
  }

  initializeOptions(): void {
    // Mark options as selected based on selectedValues input
    // selectedValues can be an array of IDs (numbers) or objects with id property
    this.options = this.options.map(option => ({
      ...option,
      selected: this.selectedValues.includes(option.id) ||
                this.selectedValues.some((val: any) => val === option.id || val.id === option.id)
    }));

    this.filteredOptions = [...this.options];
    this.updateSelectedCount();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    if (this.isDropdownOpen) {
      this.createDropdownInBody();
      // Focus on search input when dropdown opens
      setTimeout(() => {
        const searchInput = this.dropdownElement?.querySelector('input[type="text"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    } else {
      this.removeDropdownFromBody();
    }
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
    this.removeDropdownFromBody();
  }

  onSearchChange(): void {
    if (!this.searchTerm.trim()) {
      this.filteredOptions = [...this.options];
    } else {
      this.filteredOptions = this.options.filter(option =>
        option.name && option.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
  }

  onOptionToggle(option: MultiSelectOption): void {
    option.selected = !option.selected;

    // Also update the original option in the main options array
    const originalOption = this.options.find(o => o.id === option.id);
    if (originalOption) {
      originalOption.selected = option.selected;
    }

    this.updateSelectedCount();
    this.emitSelectionChange();
  }

  selectAll(): void {
    this.filteredOptions.forEach(option => {
      const originalOption = this.options.find(o => o.id === option.id);
      if (originalOption) {
        originalOption.selected = true;
        option.selected = true; // Also update the filtered option
      }
    });
    this.updateSelectedCount();
    this.emitSelectionChange();
  }

  deselectAll(): void {
    this.filteredOptions.forEach(option => {
      const originalOption = this.options.find(o => o.id === option.id);
      if (originalOption) {
        originalOption.selected = false;
        option.selected = false; // Also update the filtered option
      }
    });
    this.updateSelectedCount();
    this.emitSelectionChange();
  }

  private updateSelectedCount(): void {
    this.selectedCount = this.options.filter(option => option.selected).length;
  }

  private emitSelectionChange(): void {
    const selectedOptions = this.options.filter(option => option.selected);
    this.selectionChange.emit(selectedOptions);
  }

  getDisplayText(): string {
    if (this.selectedCount === 0) {
      return this.placeholder;
    } else if (this.selectedCount === 1) {
      const selectedOption = this.options.find(option => option.selected);
      return selectedOption ? selectedOption.name : this.placeholder;
    } else {
      return `${this.selectedCount} persons selected`;
    }
  }

  // Prevent dropdown from closing when clicking inside
  onDropdownClick(event: Event): void {
    event.stopPropagation();
  }

  // Track by function for ngFor performance
  trackByFn(index: number, item: MultiSelectOption): any {
    return item.id;
  }

  private createDropdownInBody(): void {
    if (!this.triggerElement) return;

    // Remove existing dropdown if any
    this.removeDropdownFromBody();

    // Create dropdown element
    this.dropdownElement = this.renderer.createElement('div');
    this.renderer.addClass(this.dropdownElement, 'multi-select-dropdown-portal');

    // Set initial styles
    this.renderer.setStyle(this.dropdownElement, 'position', 'fixed');
    this.renderer.setStyle(this.dropdownElement, 'z-index', '999999');
    this.renderer.setStyle(this.dropdownElement, 'background', 'white');
    this.renderer.setStyle(this.dropdownElement, 'border', '1px solid #ced4da');
    this.renderer.setStyle(this.dropdownElement, 'border-top', 'none');
    this.renderer.setStyle(this.dropdownElement, 'border-radius', '0 0 0.375rem 0.375rem');
    this.renderer.setStyle(this.dropdownElement, 'box-shadow', '0 0.5rem 1rem rgba(0, 0, 0, 0.15)');
    this.renderer.setStyle(this.dropdownElement, 'max-height', '300px');
    this.renderer.setStyle(this.dropdownElement, 'overflow', 'hidden');
    this.renderer.setStyle(this.dropdownElement, 'display', 'flex');
    this.renderer.setStyle(this.dropdownElement, 'flex-direction', 'column');

    // Position the dropdown
    this.updateDropdownPosition();

    // Create dropdown content HTML
    this.dropdownElement.innerHTML = this.getDropdownHTML();

    // Append to body
    this.renderer.appendChild(document.body, this.dropdownElement);

    // Add event listeners
    this.addDropdownEventListeners();
    this.addScrollAndResizeListeners();
  }

  private removeDropdownFromBody(): void {
    if (this.dropdownElement) {
      this.renderer.removeChild(document.body, this.dropdownElement);
      this.dropdownElement = null;
    }
    this.removeEventListeners();
  }

  private getDropdownHTML(): string {
    return `
      <div style="position: relative; padding: 0.5rem; border-bottom: 1px solid #e9ecef; display: flex; align-items: center; gap: 0.5rem;">
        <input type="text" placeholder="Search persons..."
               value="${this.searchTerm}" oninput="window.multiSelectSearch(this.value)" onkeydown="if(event.key==='Escape') window.multiSelectClose()"
               style="flex: 1; padding: 0.375rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; font-size: 1rem; line-height: 1.5;">
        <button type="button" onclick="window.multiSelectClose()" style="background: none; border: none; color: #6c757d; cursor: pointer; padding: 0.25rem; font-size: 1.2rem;" title="Close">
          <i class="bi bi-x"></i>
        </button>
      </div>
      <div class="multi-select-actions-container" style="padding: 0.5rem; border-bottom: 1px solid #e9ecef; display: flex; gap: 0.5rem;">
        ${this.getActionsHTML()}
      </div>
      <div class="multi-select-options-container" style="flex: 1; overflow-y: auto; max-height: 200px;">
        ${this.getOptionsHTML()}
      </div>
    `;
  }

  private getActionsHTML(): string {
    const selectAllBtn = this.filteredOptions.length > 0 ?
      `<button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="window.multiSelectSelectAll()">
        <i class="bi bi-check-all"></i> Select All
      </button>` : '';

    const deselectAllBtn = this.filteredOptions.length > 0 ?
      `<button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.multiSelectDeselectAll()">
        <i class="bi bi-x-square"></i> Deselect All
      </button>` : '';

    return selectAllBtn + deselectAllBtn;
  }

  private getOptionsHTML(): string {
    return this.filteredOptions.length > 0 ?
      this.filteredOptions.map((option, index) => {
        const displayName = option.name || 'Unknown Employee';
        return `
        <div onclick="window.multiSelectToggleOption(${index})" style="padding: 0.5rem 0.75rem; cursor: pointer; transition: background-color 0.15s ease-in-out;"
             onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='transparent'">
          <div style="margin-bottom: 0; display: flex; align-items: center;">
            <input type="checkbox" ${option.selected ? 'checked' : ''} id="portal-option-${option.id}"
                   style="cursor: pointer; margin-right: 0.5rem;" onclick="event.stopPropagation();">
            <label for="portal-option-${option.id}" style="cursor: pointer; margin: 0; flex: 1;">${displayName}</label>
          </div>
        </div>
        `;
      }).join('') :
      `<div style="padding: 1rem; text-align: center; color: #6c757d; font-style: italic;">
        <i class="bi bi-search" style="display: block; font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.5;"></i>
        <span>No persons found${this.searchTerm ? ` matching "${this.searchTerm}"` : ''}</span>
      </div>`;
  }

  private addDropdownEventListeners(): void {
    // Add global functions for dropdown interactions
    (window as any).multiSelectSearch = (value: string) => {
      this.searchTerm = value;
      this.onSearchChange();
      this.updateDropdownOptionsOnly(); // Only update options, not the entire content
    };

    (window as any).multiSelectToggleOption = (index: number) => {
      if (this.filteredOptions[index]) {
        this.onOptionToggle(this.filteredOptions[index]);
        this.updateDropdownOptionsOnly(); // Only update options, not the entire content
      }
    };

    (window as any).multiSelectSelectAll = () => {
      this.selectAll();
      this.updateDropdownOptionsOnly(); // Only update options, not the entire content
    };

    (window as any).multiSelectDeselectAll = () => {
      this.deselectAll();
      this.updateDropdownOptionsOnly(); // Only update options, not the entire content
    };

    (window as any).multiSelectClose = () => {
      this.closeDropdown();
    };

    // Add click listener to close dropdown when clicking outside
    this.clickListener = (event: Event) => {
      const target = event.target as Node;
      if (this.dropdownElement && !this.dropdownElement.contains(target) &&
          !this.triggerElement.nativeElement.contains(target)) {
        this.closeDropdown();
      }
    };

    // Add keyboard listener for escape key
    this.keyListener = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        this.closeDropdown();
      }
    };

    // Add the listeners with a small delay to avoid immediate closure
    setTimeout(() => {
      if (this.clickListener) {
        document.addEventListener('click', this.clickListener, true);
      }
      if (this.keyListener) {
        document.addEventListener('keydown', this.keyListener);
      }
    }, 100);
  }

  private updateDropdownContent(): void {
    if (this.dropdownElement) {
      this.dropdownElement.innerHTML = this.getDropdownHTML();
    }
  }

  private updateDropdownOptionsOnly(): void {
    if (!this.dropdownElement) return;

    // Find the options container and update only that part
    const optionsContainer = this.dropdownElement.querySelector('.multi-select-options-container');
    if (optionsContainer) {
      optionsContainer.innerHTML = this.getOptionsHTML();
    }

    // Update action buttons
    const actionsContainer = this.dropdownElement.querySelector('.multi-select-actions-container');
    if (actionsContainer) {
      actionsContainer.innerHTML = this.getActionsHTML();
    }
  }

  private updateDropdownPosition(): void {
    if (!this.dropdownElement || !this.triggerElement) return;

    const triggerRect = this.triggerElement.nativeElement.getBoundingClientRect();

    this.renderer.setStyle(this.dropdownElement, 'top', `${triggerRect.bottom}px`);
    this.renderer.setStyle(this.dropdownElement, 'left', `${triggerRect.left}px`);
    this.renderer.setStyle(this.dropdownElement, 'width', `${triggerRect.width}px`);
  }

  private addScrollAndResizeListeners(): void {
    this.scrollListener = () => {
      this.updateDropdownPosition();
    };

    this.resizeListener = () => {
      this.updateDropdownPosition();
    };

    // Add listeners to window and all scrollable parent elements
    window.addEventListener('scroll', this.scrollListener, true);
    window.addEventListener('resize', this.resizeListener);

    // Also listen for scroll on the document
    document.addEventListener('scroll', this.scrollListener, true);
  }

  private removeEventListeners(): void {
    if (this.scrollListener) {
      window.removeEventListener('scroll', this.scrollListener, true);
      document.removeEventListener('scroll', this.scrollListener, true);
      this.scrollListener = null;
    }

    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
      this.resizeListener = null;
    }

    if (this.clickListener) {
      document.removeEventListener('click', this.clickListener, true);
      this.clickListener = null;
    }

    if (this.keyListener) {
      document.removeEventListener('keydown', this.keyListener);
      this.keyListener = null;
    }
  }

}
