import { Component } from '@angular/core';
import { Employee } from '../form-fill/employee.model';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { StatusServiceService } from '../../services/status-service.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-rm-list',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './rm-list.component.html',
  styleUrl: './rm-list.component.css'
})
export class RmListComponent {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
statusFormGroup: FormGroup;
  rmLists:any[]=[];
  searchTerm: string = '';
  selectedEmployee: any;

  selectedEmployeePDFs: File[] = [];
  selectedEmployeeDetails: any = null;
  isLoadingDetails: boolean = false;
  rejectionRemarks: string;
  isRejectionModalOpen: boolean;
  isSubmitting: boolean;
  submitSuccess: boolean;
  submitMessage: string;
  registrationForm: any;

  constructor(
    private statusService: StatusServiceService, private employeeService: EmployeeService,
    private router: Router, private http:HttpClient, private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.statusFormGroup= this.formBuilder.group({
      status:[''],
      remarks:['']
    })
    this.rmList();
  }

  rmList(){
    this.statusService.getPendingList().subscribe({
      next: (employees) => {
        console.log('Employees loaded:', employees);
        this.employees = employees;
        this.rmLists = [...this.employees];
      },
      error: (error) => {
        console.error('Error loading employees:', error);
        alert('Failed to load employees. Please try again later.');
      }
    });
  }

  openApprovalModal(employee: any): void {
    this.selectedEmployee = employee;
    this.statusFormGroup.reset(); // clear previous data
  }

  statusSubmit(id:any){
    if(this.statusFormGroup.valid){
      // const {status, remarks} = this.statusFormGroup.value;
      this.statusService.updateStatus(id,this.statusFormGroup).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.submitSuccess = true;
          this.submitMessage = 'Status successful! Welcome aboard.';
          this.registrationForm.reset();
          console.log('Status successful:', response);
        },
        error: (error) => {
          this.isSubmitting = false;
          this.submitSuccess = false;
          this.submitMessage = error.message || 'Status failed. Please try again.';
          console.error('Status error:', error);
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.statusFormGroup.controls).forEach(key => {
        this.statusFormGroup.get(key)?.markAsTouched();
      });
    
    }
  }
}
