import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { Employee, ServiceEntry, LeaveEntry } from './employee.model';
 import { PDFDocument } from 'pdf-lib';


@Component({
  selector: 'app-form-fill',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './form-fill.component.html',
  styleUrl: './form-fill.component.css',
  standalone: true,

})
export class FormFillComponent implements OnInit{

  employeeForm!: FormGroup;
  isEditMode = false;
  editingEmployeeId: string | null = null;

  // File upload properties
  uploadedFiles: File[] = [];
  isDragOver = false;

  constructor(
    private fb: FormBuilder,
    private employeeService: EmployeeService,
    private route: ActivatedRoute,
    public router: Router
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.route.queryParams.subscribe(params => {
      if (params['edit'] && params['empId']) {
        this.isEditMode = true;
        this.editingEmployeeId = params['empId'];
        this.loadEmployeeForEdit(params['empId']);
      }
    });

    // Generate sample data if localStorage is empty
    const existingEmployees = this.employeeService.loadEmployees();
    // if (existingEmployees.length === 0) {
    //   this.employeeService.generateSampleData();
    // }
  }

  initializeForm() {
    this.employeeForm = this.fb.group({
      empId: ['', Validators.required],
      employeeName: ['', Validators.required],
      fatherName: ['', Validators.required],
      dateOfBirth: ['', Validators.required],
      religion: ['', Validators.required],
      community: ['', Validators.required],
      category: ['', Validators.required],
      height: ['', Validators.required],
      personalIdentificationMarks: ['', Validators.required],
      departmentAuthority: ['', Validators.required],
      designation: ['', Validators.required],
      dateOfEntryIntoService: ['', Validators.required],
      educationalQualification: ['', Validators.required],
      mobile: ['', [Validators.required, Validators.maxLength(15)]],
      address: ['', Validators.required],
      serviceEntries: this.fb.array([this.createServiceEntry()]),
      leaveEntries: this.fb.array([this.createLeaveEntry()]),
       location: ['', Validators.required],
    });
  }

  createServiceEntry(): FormGroup {
    return this.fb.group({
      date: ['', Validators.required],
      type: ['', Validators.required],
      status: ['', Validators.required]
    });
  }

  createLeaveEntry(): FormGroup {
    return this.fb.group({
      leaveType: ['', Validators.required],
      leaveBalanceCount: ['', [Validators.required, Validators.min(0)]]
    });
  }

  get serviceEntries(): FormArray {
    return this.employeeForm.get('serviceEntries') as FormArray;
  }

  get leaveEntries(): FormArray {
    return this.employeeForm.get('leaveEntries') as FormArray;
  }

  addServiceEntry() {
    this.serviceEntries.push(this.createServiceEntry());
  }

  removeServiceEntry(index: number) {
    if (this.serviceEntries.length > 1) {
      this.serviceEntries.removeAt(index);
    }
  }

  addLeaveEntry() {
    this.leaveEntries.push(this.createLeaveEntry());
  }

  removeLeaveEntry(index: number) {
    if (this.leaveEntries.length > 1) {
      this.leaveEntries.removeAt(index);
    }
  }

  loadEmployeeForEdit(empId: string): void {
    const employee = this.employeeService.getEmployeeById(empId);
    if (employee) {
      // Clear existing form arrays
      while (this.serviceEntries.length !== 0) {
        this.serviceEntries.removeAt(0);
      }
      while (this.leaveEntries.length !== 0) {
        this.leaveEntries.removeAt(0);
      }

      // Populate form with employee data
      this.employeeForm.patchValue({
        empId: employee.empId,
        employeeName: employee.employeeName,
        fatherName: employee.fatherName,
        dateOfBirth: employee.dateOfBirth,
        religion: employee.religion,
        community: employee.community,
    
        personalIdentificationMarks: employee.personalIdentificationMarks,
        departmentAuthority: employee.Authority,
        designation: employee.designation,
        dateOfEntryIntoService: employee.dateOfEntryIntoService,
        educationalQualification: employee.educationalQualification,
        district: employee.district,
     
        location: employee.location,
      });

      // Populate service entries
      employee.serviceEntries.forEach(entry => {
        const serviceGroup = this.createServiceEntry();
        serviceGroup.patchValue(entry);
        this.serviceEntries.push(serviceGroup);
      });

      // Populate leave entries
      employee.leaveEntries.forEach(entry => {
        const leaveGroup = this.createLeaveEntry();
        leaveGroup.patchValue(entry);
        this.leaveEntries.push(leaveGroup);
      });
    }
  }

  onSubmit() {
    if (this.employeeForm.valid) {
      const formData = this.employeeForm.value;

      if (this.isEditMode && this.editingEmployeeId) {
        // Update existing employee
        this.employeeService.updateEmployee(this.editingEmployeeId, formData);
        alert('Employee updated successfully!');
      } else {
        // Add new employee
        this.employeeService.addEmployee(formData);
        alert('Employee added successfully!');
      }

      // Navigate back to view page
      this.router.navigate(['/view']);
    } else {
      alert('Please fill all required fields!');
    }
  }

  // File upload methods
  onFileSelected(event: any): void {
    const files = event.target.files;
    this.handleFiles(files);
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files) {
      this.handleFiles(files);
    }
  }

  private handleFiles(files: FileList): void {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Check if file is PDF
      if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
        // Check if file already exists
        const existingFile = this.uploadedFiles.find(f => f.name === file.name && f.size === file.size);
        if (!existingFile) {
          this.uploadedFiles.push(file);
        }
      } else {
        alert(`File "${file.name}" is not a PDF file. Only PDF files are allowed.`);
      }
    }
  }

  removeFile(index: number): void {
    this.uploadedFiles.splice(index, 1);
  }

  openPDF(file: File): void {
    const fileURL = URL.createObjectURL(file);
    window.open(fileURL, '_blank');
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
 

async compressPDF(file: File): Promise<File> {
  const arrayBuffer = await file.arrayBuffer();
  const originalPdf = await PDFDocument.load(arrayBuffer);

  const compressedPdf = await PDFDocument.create();
  const pages = await compressedPdf.copyPages(originalPdf, originalPdf.getPageIndices());
  pages.forEach((page) => compressedPdf.addPage(page));

  const compressedBytes = await compressedPdf.save();

  return new File([compressedBytes], `compressed_${file.name}`, {
    type: 'application/pdf',
  });
}
async compressFile(index: number) {
  const originalFile = this.uploadedFiles[index];
  const compressed = await this.compressPDF(originalFile);
  this.uploadedFiles[index] = compressed;
}



}
