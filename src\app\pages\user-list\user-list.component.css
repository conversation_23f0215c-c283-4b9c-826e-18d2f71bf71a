/* Loading spinner styles */
.spinner-border {
  animation: spinner-border 0.75s linear infinite;
}

/* Pagination styles */
.pagination .page-link {
  color: #007bff;
  border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
}

.pagination .page-link:hover:not(.disabled) {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

/* Table responsive improvements */
.table-responsive {
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table {
  margin-bottom: 0;
}

.table thead th {
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
}

/* Loading container */
.loading-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.loading-text {
  margin-top: 1rem;
  color: #6c757d;
  font-size: 1.1rem;
}