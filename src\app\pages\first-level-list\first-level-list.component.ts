import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { StatusServiceService } from '../../services/status-service.service';
import { Employee } from '../form-fill/employee.model';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormGroup, FormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-first-level-list',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './first-level-list.component.html',
  styleUrl: './first-level-list.component.css'
})
export class FirstLevelListComponent implements OnInit {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  statusFormGroup: FormGroup;
  firstLevelList:any[]=[];
  searchTerm: string = '';
  selectedEmployee: Employee | null = null;
  selectedEmployeePDFs: File[] = [];
  selectedEmployeeDetails: any = null;
  isLoadingDetails: boolean = false;
  rejectionRemarks: string;
  isRejectionModalOpen: boolean;
  isSubmitting: boolean;
  submitSuccess: boolean;
  submitMessage: string;
  registrationForm: any;

  constructor(
    private statusService: StatusServiceService, private employeeService: EmployeeService,
    private router: Router, private formBuilder:FormBuilder, private http:HttpClient
  ) {}

  ngOnInit(): void {
    this.firstLevelListShow();
    this.statusFormGroup= this.formBuilder.group({
      status:[''],
      remarks:['']
    })
  }


  firstLevelListShow(){
    this.statusService.getFirstLevelList().subscribe({
      next: (employees) => {
        console.log('Employees loaded:', employees);
        this.employees = employees;
        this.firstLevelList = [...this.employees];
      },
      error: (error) => {
        console.error('Error loading employees:', error);
        alert('Failed to load employees. Please try again later.');
      }
    });
  }




  openApprovalModal(employee: any): void {
    this.selectedEmployee = employee;
    this.statusFormGroup.reset(); // clear previous data
  }




  statusSubmit(id: any) {
    if (this.statusFormGroup.valid) {
      this.isSubmitting = true;
      const { status, remarks } = this.statusFormGroup.value;
      const requestData = { status, remarks };

      console.log('Submitting status update:', { id, requestData });

      this.statusService.updateStatus(id, requestData).subscribe({
        next: (response) => {
          console.log('Full API Response:', response);
          console.log('Response status:', response?.status);
          console.log('Response type:', typeof response);

          this.isSubmitting = false;
          this.submitSuccess = true;
          this.submitMessage = 'Status updated successfully!';

          // Close the modal
          this.closeModal('approvalModal');

          // Reset the form
          this.statusFormGroup.reset();

          // Show success message using SweetAlert2
          Swal.fire({
            title: 'Success!',
            text: 'Status updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK'
          }).then(() => {
            // Reload the first level list data instead of the entire page
            this.firstLevelListShow();
          });
        },
        error: (error) => {
          console.error('Status update error details:', error);
          console.error('Error status code:', error.status);
          console.error('Error response:', error.error);

          this.isSubmitting = false;
          this.submitSuccess = false;
          this.submitMessage = 'Failed to update status. Please try again.';

          // Check if it's actually a successful response with error structure
          if (error.status === 200 || (error.error && error.error.success)) {
            // Handle case where backend returns 200 but with error structure
            Swal.fire({
              title: 'Success!',
              text: 'Status updated successfully!',
              icon: 'success',
              confirmButtonText: 'OK'
            }).then(() => {
              this.closeModal('approvalModal');
              this.statusFormGroup.reset();
              this.firstLevelListShow();
            });
          } else {
            const errorMessage = error.error?.message || error.message || 'Failed to update status. Please try again.';
            Swal.fire({
              title: 'Error!',
              text: errorMessage,
              icon: 'error',
              confirmButtonText: 'OK'
            });
          }
        },
        complete: () => {
          console.log('Status update request completed');
          this.isSubmitting = false;
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.statusFormGroup.controls).forEach(key => {
        this.statusFormGroup.get(key)?.markAsTouched();
      });
    }
  }

  // Method to close modal programmatically
  private closeModal(modalId: string) {
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
      // For Bootstrap 5
      const modal = (window as any).bootstrap?.Modal?.getInstance(modalElement);
      if (modal) {
        modal.hide();
      } else {
        // Fallback for Bootstrap 4 or if Bootstrap 5 instance not found
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
          backdrop.remove();
        }
      }
    }
  }




  // searchEmployees(): void {
  //   if (!this.searchTerm.trim()) {
  //     this.filteredEmployees = [...this.employees];
  //     return;
  //   }

  //   const searchTermLower = this.searchTerm.toLowerCase();
  //   this.filteredEmployees = this.employees.filter(employee =>
  //     employee.employeeName.toLowerCase().includes(searchTermLower) ||
  //     employee.designation.toLowerCase().includes(searchTermLower) ||
  //     employee.Authority.toLowerCase().includes(searchTermLower) ||
  //     employee.district.toLowerCase().includes(searchTermLower) ||employee.location.toLowerCase().includes(searchTermLower) ||
  //     employee.empId.toLowerCase().includes(searchTermLower)
  //   );
  // }

  editEmployee(employee: Employee): void {
    this.router.navigate(['/form-edit'], {
      queryParams: { edit: true, empId: employee.id || employee.empId }
    });
  }

  viewEmployee(employee: Employee): void {
    console.log('Viewing employee details for employee:', employee);
    console.log('Employee ID to be used for API call:', employee.id);

    // Use employee.id specifically for the API call
    if (!employee.id) {
      console.error('No employee.id found');
      this.selectedEmployee = employee;
      alert('Could not find employee ID. Showing basic information.');
      return;
    }

    this.http.get<any>(`http://localhost:8082/employee/getEmp/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Complete employee data received:', response);
          console.log('Profile photo in API response:', response.profilePhoto);
          console.log('Profile object in API response:', response.profile);
          console.log('Nominees in API response:', response.nominees);

          this.selectedEmployee = this.mapCompleteEmployeeData(response);

          console.log('Mapped employee data:', this.selectedEmployee);
          console.log('Profile photo URL after mapping:', this.selectedEmployee.profilePhotoUrl);
          console.log('Profile photo field after mapping:', (this.selectedEmployee as any).profilePhoto);

          console.log('Mapped employee nomination entries:', this.selectedEmployee.nominationEntries);
          console.log('Nominee photos after mapping:', this.selectedEmployee.nominationEntries?.map((nominee, index) => ({
            index: index + 1,
            name: nominee.nomineeName,
            rawPhoto: nominee.nomineePhoto,
            processedPhotoUrl: (nominee as any).nomineePhotoUrl
          })));

          // Load profile photo after setting employee data
          this.loadEmployeeProfilePhoto(employee.id.toString());
          // Initialize nominee photo loading
          this.initializeNomineePhotos();
        },
        error: (error) => {
          console.error('Error fetching employee details:', error);
          console.error('API URL called:', `http://localhost:8082/employee/getEmp/${employee.id}`);
          // Fallback to existing data if API fails
          this.selectedEmployee = employee;
          alert('Could not load complete employee details. Showing basic information.');
        }
      });
  }

  refreshData(): void {
    this.searchTerm = '';
  }

  firstLevelAprroval(employee: Employee): void {
    this.selectedEmployee = employee;
    this.selectedEmployeeDetails = null;
    this.isLoadingDetails = true;

    // Call API to get detailed employee information
    this.employeeService.getEmployeeDetails(employee.id).subscribe({
      next: (details) => {
        console.log('Employee details loaded:', details);
        this.selectedEmployeeDetails = details;
        this.isLoadingDetails = false;
      },
      error: (error) => {
        console.error('Error loading employee details:', error);
        this.isLoadingDetails = false;
        // Keep the basic employee data if API fails
        alert('Failed to load detailed employee information. Showing basic details only.');
      }
    });
  }

  private mapCompleteEmployeeData(apiData: any): Employee {
    const mappedEmployee = {
      // Basic Information
      id: apiData.id || apiData.employeeId || apiData.ecpfNumber || '',
      empId: apiData.ecpfNumber || apiData.empId || '',
      ecpfNumber: apiData.ecpfNumber || '',
      employeeName: apiData.employeeName || '',
      designation: apiData.currentDesignation || apiData.designation || '',
      Authority: apiData.section || '',
      district: apiData.district || '',
      location: apiData.nativePlaceAndTaluk || '',
      createdAt: apiData.createdAt || '',
      fatherName: apiData.fatherName || apiData.profile?.fatherName || '',
      motherName: apiData.motherName || apiData.profile?.motherName || '',
      dateOfBirth: apiData.dateOfBirth || apiData.profile?.dateOfBirth || '',
      religion: apiData.religion || '',
      community: apiData.community || apiData.profile?.community || '',
      caste: apiData.caste || apiData.profile?.caste || '',
      personalIdentificationMarks: (apiData.personalIdentificationmark1 || '') +
        (apiData.personalIdentificationmark2 ? ', ' + apiData.personalIdentificationmark2 : ''),
      dateOfEntryIntoService: apiData.dateOfEntry || '',
      educationalQualification: this.formatEducationQualifications(apiData.educationQualifications),

      // Profile object (including profile photo and address data)
      profile: {
        ...apiData.profile,
        // Ensure all address fields are mapped
        presentDoorNo: apiData.profile?.presentDoorNo || '',
        presentBuildingName: apiData.profile?.presentBuildingName || '',
        presentStreetAddress: apiData.profile?.presentStreetAddress || '',
        presentCity: apiData.profile?.presentCity || '',
        presentPincode: apiData.profile?.presentPincode || '',
        permanentDoorNo: apiData.profile?.permanentDoorNo || '',
        permanentBuildingName: apiData.profile?.permanentBuildingName || '',
        permanentStreetAddress: apiData.profile?.permanentStreetAddress || '',
        permanentCity: apiData.profile?.permanentCity || '',
        permanentPincode: apiData.profile?.permanentPincode || '',
        profilephoto: apiData.profile?.profilephoto || apiData.profile?.profilePhoto || apiData.profilePhoto || '',
        profilePhoto: apiData.profile?.profilePhoto || apiData.profile?.profilephoto || apiData.profilePhoto || '',
        mobileNumber: apiData.profile?.mobileNumber || apiData.mobileNumber || ''
      },

      // Missing required fields
      status: apiData.status || '',
      dateOfEntry: apiData.dateOfEntry || '',
      educationEnties: this.mapEducationEntries(apiData.educationQualifications || []),

      // Contact Information
      email: apiData.email || apiData.profile?.email || '',
      gender: apiData.gender || apiData.profile?.gender || '',
      presentAddress: apiData.presentAddress || apiData.profile?.presentaddress || '',
      permanentAddress: apiData.permanentAddress || apiData.profile?.permanentaddress || '',

      // Account Details
      bankAccountNo: apiData.accountDetails?.bankaccountnumber || '',
      ifscCode: apiData.accountDetails?.ifsccode || '',
      bankName: apiData.accountDetails?.bankname || '',
      panNumber: apiData.panNumber || '',
      uanNumber: apiData.accountDetails?.uannumber || '',
      aadharNumber: apiData.accountDetails?.aadharnumber || '',

      // Service Records
      serviceRecords: apiData.serviceHistory || [],
      leaveEntries: this.mapLeaveBalances(apiData.leaveBalances || []),

      // Additional Information
      registerNumber: apiData.registerNumber || '',
      personalIdMark1: apiData.personalIdentificationmark1 || '',
      personalIdMark2: apiData.personalIdentificationmark2 || '',
      nativeAndTaluk: apiData.nativePlaceAndTaluk || '',
      profilePhotoUrl: this.generateProfilePhotoUrl(apiData),

      // Store root level profilePhoto for access in loadEmployeeProfilePhoto
      profilePhoto: apiData.profilePhoto || '',

      // Arrays
      serviceEntries: this.mapServiceHistory(apiData.serviceHistory || []),
      trainingEntries: this.mapTrainingDetails(apiData.trainingDetails || []),
      punishmentEntries: this.mapPunishmentDetails(apiData.punishmentDetails || []),
      nominationEntries: this.mapNominees(apiData.nominees || []),
      educationEntries: this.mapEducationDetails(apiData.educationDetails || []),

      // Salary Details
      salaryDetails: {
        lastSalaryRevisedDate: apiData.salaryDetails?.lastSalaryRevisedDate || '',
        group: apiData.salaryDetails?.group || apiData.group || '',
        payband: apiData.salaryDetails?.payband || apiData.payband || '',
        gradepay: apiData.salaryDetails?.gradepay || apiData.gradepay || ''
      },

      remarks: apiData.remarks || '',
      rejectedBy: apiData.rejectedBy || ''
    };

    // Handle profile photo URL generation (same logic as services pending list)
    if (mappedEmployee.profile?.profilephoto || mappedEmployee.profile?.profilePhoto) {
      const photoPath = mappedEmployee.profile.profilephoto || mappedEmployee.profile.profilePhoto;
      const baseUrl = 'http://localhost:8082';

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      (mappedEmployee as any).profilePhotoUrl = fullPhotoUrl;
      console.log('Profile photo URL set for employee:', fullPhotoUrl);
    }

    return mappedEmployee;
  }

  private mapLeaveBalances(leaveBalances: any[]): any[] {
    return leaveBalances.map(leave => ({
      leaveType: leave.leaveType || '',
      leaveBalanceCount: leave.openingBalance || leave.closingBalance || 0,
      openingBalance: leave.openingBalance || 0,
      closingBalance: leave.closingBalance || 0,
      entryDate: leave.entryDate || ''
    }));
  }

  private mapServiceHistory(serviceHistory: any[]): any[] {
    return serviceHistory.map(service => ({
      date: service.date || service.dateofappointment || service.joiningdate || service.fromdate || service.promoteddate || service.punishmentdate || '',
      type: service.type || '',
      status: service.status || 'Active',

      // Appointment fields
      appointmentType: service.appointmenttype || '',
      modeOfAppointment: service.modeofappointment || '',
      dateOfAppointment: service.dateofappointment || '',
      proceedingOrderNo: service.proceedingorderno || '',
      proceedingOrderDate: service.proceedingorderdate || '',

      // Promotion fields
      joiningDate: service.joiningdate || '',
      fromDesignation: service.fromdesignation || '',
      toPromoted: service.topromoted || '',
      promotedDate: service.promoteddate || '',

      // Transfer fields
      fromDate: service.fromdate || '',
      toDate: service.todate || '',
      fromPlace: service.fromplace || '',
      toPlace: service.toplace || '',

      // Increment fields
      typeOfIncrement: service.typeofincrement || '',
      incrementType: service.incrementtype || '',

      // Deputation fields
      designation: service.designation || '',
      originalDesignation: service.originaldesignation || '',
      parentDepartment: service.parentdepartment || '',

      // Punishment fields
      punishmentType: service.punishmenttype || '',
      punishmentDate: service.punishmentdate || '',
      caseDetails: service.casedetails || '',
      caseNumber: service.caseNumber || '',
      caseDate: service.caseDate || '',
      personInvolved: service.personInvolved || '',
      involvedPersonsWithNames: service.involvedPersonsWithNames || [],
      presentStatus: service.presentStatus || '',
      description: service.description || ''
    }));
  }

  private mapTrainingDetails(trainingDetails: any[]): any[] {
    return trainingDetails.map(training => ({
      trainingType: training.trainingtype || '',
      trainingDate: training.date || ''
    }));
  }

  private mapPunishmentDetails(punishmentDetails: any[]): any[] {
    return punishmentDetails.map(punishment => ({
      punishmentType: punishment.punishmenttype || '',
      punishmentDate: punishment.date || '',
      caseDetails: punishment.casedetails || ''
    }));
  }

  private mapNominees(nominees: any[]): any[] {
    return nominees.map(nominee => {
      const mappedNominee = {
        nomineeName: nominee.nomineename || '',
        address: nominee.address || '',
        relationship: nominee.relationship || '',
        age: nominee.age || 0,
        percentageOfShare: nominee.percentageofshare || 0,
        gender: nominee.gender || '',
        nomineePhoto: nominee.nomineephoto || nominee.nomineePhoto || '',
        photoLoadingComplete: false
      };

      // If nominee has a photo, construct the full URL immediately
      if (mappedNominee.nomineePhoto && mappedNominee.nomineePhoto !== 'pending_upload' && mappedNominee.nomineePhoto.trim() !== '') {
        const baseUrl = 'http://localhost:8082';
        const photoPath = mappedNominee.nomineePhoto;

        let fullPhotoUrl: string;
        if (photoPath.startsWith('http')) {
          fullPhotoUrl = photoPath;
        } else if (photoPath.startsWith('/api/')) {
          fullPhotoUrl = `${baseUrl}${photoPath}`;
        } else {
          fullPhotoUrl = `${baseUrl}${photoPath}`;
        }

        // Store the processed URL for immediate use
        (mappedNominee as any).nomineePhotoUrl = fullPhotoUrl;
        console.log('Nominee photo URL set during mapping:', fullPhotoUrl);
      }

      return mappedNominee;
    });
  }

  private mapEducationEntries(educationQualifications: any[]): any[] {
    return educationQualifications.map(education => ({
      qualification: education.qualification || '',
      courseName: education.coursename || '',
      instituteName: education.schoolname || education.collegename || education.universityname || '',
      universityName: education.universityname || '',
      specialization: education.specialization || ''
    }));
  }

  private mapEducationDetails(educationDetails: any[]): any[] {
    return educationDetails.map(education => ({
      qualification: education.qualification || '',
      instituteName: education.institutename || education.schoolname || education.collegename || '',
      courseName: education.coursename || '',
      yearOfPassing: education.yearofpassing || '',
      percentage: education.percentage || '',
      grade: education.grade || ''
    }));
  }

  private formatEducationQualifications(educationQualifications: any[]): string {
    if (!educationQualifications || educationQualifications.length === 0) {
      return 'Not specified';
    }
    return educationQualifications.map(edu => edu.qualification).join(', ');
  }

  // Image handling methods
  onImageError(event: any): void {
    console.log('Profile image failed to load');
    event.target.style.display = 'none';
  }

  onImageLoad(event: any): void {
    console.log('Profile image loaded successfully');
  }

  // Address formatting methods
  getFormattedPresentAddress(): string {
    if (!this.selectedEmployee) return 'Not provided';

    const profile = this.selectedEmployee.profile;
    if (profile && (profile.presentDoorNo || profile.presentBuildingName || profile.presentStreetAddress || profile.presentCity)) {
      const parts = [
        profile.presentDoorNo,
        profile.presentBuildingName,
        profile.presentStreetAddress,
        profile.presentCity,
        profile.presentPincode
      ].filter(part => part && part.trim() !== '');

      return parts.length > 0 ? parts.join(', ') : (this.selectedEmployee.presentAddress || 'Not provided');
    }

    return this.selectedEmployee.presentAddress || 'Not provided';
  }

  getFormattedPermanentAddress(): string {
    if (!this.selectedEmployee) return 'Not provided';

    const profile = this.selectedEmployee.profile;
    if (profile && (profile.permanentDoorNo || profile.permanentBuildingName || profile.permanentStreetAddress || profile.permanentCity)) {
      const parts = [
        profile.permanentDoorNo,
        profile.permanentBuildingName,
        profile.permanentStreetAddress,
        profile.permanentCity,
        profile.permanentPincode
      ].filter(part => part && part.trim() !== '');

      return parts.length > 0 ? parts.join(', ') : (this.selectedEmployee.permanentAddress || 'Not provided');
    }

    return this.selectedEmployee.permanentAddress || 'Not provided';
  }

  // Helper method to generate profile photo URL
  private generateProfilePhotoUrl(apiData: any): string {
    // Check multiple possible locations for profile photo - prioritize root level profilePhoto
    const profilePhoto = apiData.profilePhoto || apiData.profile?.profilephoto || apiData.profile?.profilePhoto;

    console.log('API Data structure:', {
      rootProfilePhoto: apiData.profilePhoto,
      profileObjectPhoto: apiData.profile?.profilephoto,
      profileObjectPhoto2: apiData.profile?.profilePhoto,
      selectedPhoto: profilePhoto
    });

    if (profilePhoto && profilePhoto.trim() !== '') {
      const baseUrl = 'http://localhost:8082';

      if (profilePhoto.startsWith('http')) {
        console.log('Profile photo is already a full URL:', profilePhoto);
        return profilePhoto;
      } else if (profilePhoto.startsWith('/api/')) {
        const fullUrl = `${baseUrl}${profilePhoto}`;
        console.log('Profile photo URL constructed:', fullUrl);
        return fullUrl;
      } else if (profilePhoto.startsWith('/')) {
        const fullUrl = `${baseUrl}${profilePhoto}`;
        console.log('Profile photo URL constructed:', fullUrl);
        return fullUrl;
      } else {
        // Ensure path starts with /
        const fullUrl = `${baseUrl}/${profilePhoto}`;
        console.log('Profile photo URL constructed:', fullUrl);
        return fullUrl;
      }
    }

    console.log('No profile photo found in API data');
    return '';
  }

  // Load employee profile photo (similar to view component)
  loadEmployeeProfilePhoto(employeeId: string): void {
    console.log('Loading profile photo for employee ID:', employeeId);

    // Check if we already have a valid profile photo URL from mapping
    if (this.selectedEmployee?.profilePhotoUrl && this.selectedEmployee.profilePhotoUrl.trim() !== '') {
      console.log('Profile photo URL already set from mapping:', this.selectedEmployee.profilePhotoUrl);
      return;
    }

    // Check multiple possible locations for profile photo - prioritize root level profilePhoto
    const profilePhotoPath = (this.selectedEmployee as any)?.profilePhoto ||
                            this.selectedEmployee?.profile?.profilephoto ||
                            this.selectedEmployee?.profile?.profilePhoto;

    console.log('Profile photo path search:', {
      rootLevelPhoto: (this.selectedEmployee as any)?.profilePhoto,
      profileObjectPhoto: this.selectedEmployee?.profile?.profilephoto,
      profileObjectPhoto2: this.selectedEmployee?.profile?.profilePhoto,
      selectedPath: profilePhotoPath
    });

    if (profilePhotoPath) {
      console.log('Profile photo found in employee data:', profilePhotoPath);

      // Construct the full URL for the profile photo
      const baseUrl = 'http://localhost:8082';

      // Handle both absolute and relative paths
      let fullPhotoUrl: string;
      if (profilePhotoPath.startsWith('http')) {
        fullPhotoUrl = profilePhotoPath;
      } else if (profilePhotoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${profilePhotoPath}`;
      } else if (profilePhotoPath.startsWith('/')) {
        fullPhotoUrl = `${baseUrl}${profilePhotoPath}`;
      } else {
        // Add /api prefix if path doesn't start with /
        fullPhotoUrl = `${baseUrl}/api/${profilePhotoPath}`;
      }

      console.log('Full profile photo URL constructed:', fullPhotoUrl);
      (this.selectedEmployee as any).profilePhotoUrl = fullPhotoUrl;
      return;
    }

    // Fallback: Call API to get employee profile photo from document service
    console.log('No profile photo in employee data, trying document service...');
    this.http.get(`http://localhost:8082/document/photo/${employeeId}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log('Profile photo loaded successfully from document service');
          // Create blob URL for the image
          const photoUrl = window.URL.createObjectURL(blob);
          if (this.selectedEmployee) {
            (this.selectedEmployee as any).profilePhotoUrl = photoUrl;
          }
        },
        error: (error) => {
          console.log('No profile photo found in document service or error loading photo:', error);
          // Set profilePhotoUrl to null if no photo is found
          if (this.selectedEmployee) {
            (this.selectedEmployee as any).profilePhotoUrl = null;
          }
        }
      });
  }

  // Nominee photo handling methods (matching view component)
  getNomineePhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      console.log(`No nominee found at index ${nomineeIndex}`);
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // Check if nominee already has a processed photo URL
    if ((nominee as any).nomineePhotoUrl) {
      console.log(`Nominee ${nomineeIndex + 1} already has processed URL:`, (nominee as any).nomineePhotoUrl);
      return (nominee as any).nomineePhotoUrl;
    }

    // Check if nominee has a photo field
    if ((nominee as any).nomineePhoto &&
        (nominee as any).nomineePhoto !== 'pending_upload' &&
        (nominee as any).nomineePhoto.trim() !== '') {

      const baseUrl = 'http://localhost:8082';
      const photoPath = (nominee as any).nomineePhoto;

      console.log(`Nominee ${nomineeIndex + 1} raw photo path:`, photoPath);

      // Use the same logic as profile photo (exactly matching view component logic)
      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      console.log(`Nominee ${nomineeIndex + 1} constructed full photo URL:`, fullPhotoUrl);

      // Store the processed URL for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      (nominee as any).photoLoadingComplete = true;
      return fullPhotoUrl;
    }

    console.log(`Nominee ${nomineeIndex + 1} has no valid photo path, trying document service...`);
    // If no photo path in nominee data, try to load from document service
    this.loadNomineePhotoFromDocuments(nomineeIndex);
    return null;
  }

  // Simple method to get nominee photo URL for template use (matching view component)
  getNomineeDisplayPhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // If already processed, return it
    if ((nominee as any).nomineePhotoUrl) {
      return (nominee as any).nomineePhotoUrl;
    }

    // If has raw photo path, construct URL immediately
    if (nominee.nomineePhoto && nominee.nomineePhoto !== 'pending_upload' && nominee.nomineePhoto.trim() !== '') {
      const baseUrl = 'http://localhost:8082';
      const photoPath = nominee.nomineePhoto;

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      // Store for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      return fullPhotoUrl;
    }

    return null;
  }

  onNomineeImageError(event: any, nomineeIndex: number): void {
    console.log(`Error loading nominee ${nomineeIndex + 1} photo:`, event);

    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // Try to reload from document service if the current URL failed
    if (!(nominee as any).documentServiceTried) {
      (nominee as any).documentServiceTried = true;
      this.loadNomineePhotoFromDocuments(nomineeIndex);
    } else {
      // Hide the image and mark as no photo available
      event.target.style.display = 'none';
      (nominee as any).nomineePhotoUrl = null;
      (nominee as any).photoLoadingComplete = true;
    }
  }

  onNomineeImageLoad(nomineeIndex: number): void {
    console.log(`Nominee ${nomineeIndex + 1} image loaded successfully`);
    if (this.selectedEmployee?.nominationEntries && this.selectedEmployee.nominationEntries[nomineeIndex]) {
      const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
      (nominee as any).photoLoadingComplete = true;
    }
  }

  // View nominee photo in larger size
  viewNomineePhoto(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const photoUrl = (nominee as any).nomineePhotoUrl || this.getNomineeDisplayPhotoUrl(nomineeIndex);

    if (photoUrl) {
      // Open photo in new window/tab
      window.open(photoUrl, '_blank');
    } else {
      console.log('No photo available for nominee', nomineeIndex + 1);
    }
  }

  // Initialize nominee photo loading for all nominees (similar to view component)
  initializeNomineePhotos(): void {
    if (!this.selectedEmployee?.nominationEntries) {
      console.log('No nomination entries found for photo initialization');
      return;
    }

    console.log('Initializing nominee photos for', this.selectedEmployee.nominationEntries.length, 'nominees');
    console.log('Nominee entries:', this.selectedEmployee.nominationEntries);

    this.selectedEmployee.nominationEntries.forEach((nominee, index) => {
      console.log(`Initializing photo for nominee ${index + 1}:`, nominee);

      // Mark as loading initially only if photo URL is not already processed
      if (!(nominee as any).nomineePhotoUrl) {
        (nominee as any).photoLoadingComplete = false;

        // Try to get photo URL - this will trigger loading if needed
        setTimeout(() => {
          const photoUrl = this.getNomineePhotoUrl(index);
          console.log(`Photo URL result for nominee ${index + 1}:`, photoUrl);
        }, 50 * index); // Stagger the requests slightly
      } else {
        console.log(`Nominee ${index + 1} already has photo URL:`, (nominee as any).nomineePhotoUrl);
        (nominee as any).photoLoadingComplete = true;
      }
    });
  }

  // Load nominee photo from document service
  loadNomineePhotoFromDocuments(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const employeeId = this.selectedEmployee.id || this.selectedEmployee.empId;

    console.log(`Loading nominee ${nomineeIndex + 1} photo from document service for employee ID:`, employeeId);

    // Try to get nominee photo from document service
    this.http.get(`http://localhost:8082/document/nominee-photo/${employeeId}/${nomineeIndex}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`Nominee ${nomineeIndex + 1} photo loaded successfully from document service`);
          // Create blob URL for the image
          const photoUrl = window.URL.createObjectURL(blob);
          (nominee as any).nomineePhotoUrl = photoUrl;
          (nominee as any).photoLoadingComplete = true;
        },
        error: (error) => {
          console.log(`No nominee ${nomineeIndex + 1} photo found in document service or error loading photo:`, error);
          // Try alternative endpoint or set to null
          this.tryAlternativeNomineePhotoEndpoint(nomineeIndex);
        }
      });
  }

  // Try alternative nominee photo endpoint
  tryAlternativeNomineePhotoEndpoint(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const employeeId = this.selectedEmployee.id || this.selectedEmployee.empId;

    // Try alternative endpoint pattern
    this.http.get(`http://localhost:8082/document/photo/nominee/${employeeId}/${nomineeIndex}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`Nominee ${nomineeIndex + 1} photo loaded from alternative endpoint`);
          const photoUrl = window.URL.createObjectURL(blob);
          (nominee as any).nomineePhotoUrl = photoUrl;
          (nominee as any).photoLoadingComplete = true;
        },
        error: (error) => {
          console.log(`No nominee ${nomineeIndex + 1} photo found in alternative endpoint:`, error);
          // Set to null if no photo is found anywhere
          (nominee as any).nomineePhotoUrl = null;
          (nominee as any).photoLoadingComplete = true;
        }
      });
  }
}
