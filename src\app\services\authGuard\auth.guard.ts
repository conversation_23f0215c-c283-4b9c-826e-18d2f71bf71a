import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

 export const authGuard: CanActivateFn = () => {
  const router = inject(Router);

  // Check if the user is authenticated (e.g., token exists)
  const token = sessionStorage.getItem('token') || localStorage.getItem('token');
  if (token) {
    console.log('Auth guard: Token found, allowing access');
    return true; // Allow access to protected routes
  } else {
    console.log('Auth guard: No token found, redirecting to login');
    // Redirect to login if not authenticated
    return router.createUrlTree(['/login']);
  }
};

