<div class="mt-4">
    <h2 class="text-danger">Rejected Employees</h2>
  
    <!-- Employee count display -->
    <div class="row mb-3">
      <div class="col-md-6 d-flex align-items-center">
        <input type="text" class="form-control me-2" placeholder="Search rejected employees..." [(ngModel)]="searchTerm" (input)="searchEmployees()">
        <button class="btn btn-outline-secondary flex-shrink-0" type="button" (click)="searchEmployees()" title="Search">
          <i class="bi bi-search"></i> Search
        </button>
      </div>
    </div>
  
    <div class="table-responsive">
      <table class="table table-striped table-bordered">
        <thead>
          <tr>
            <!-- <th class="th-color">Emp ID</th> -->
            <th class="th-color">Name</th>
            <th class="th-color">Designation</th>
            <!-- <th class="th-color">Location</th> -->
            <th class="th-color">District</th>
            <!-- <th class="th-color">Date of Entry</th> -->
            <th class="th-color">Status</th>
            <th class="th-color">Rejected By</th>
            <th class="th-color">Remarks</th>
            <th class="th-color">Date & Time</th>
            <th class="th-color">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let employee of filteredEmployees; let i = index">
            <!-- <td>{{ employee.empId }}</td> -->
            <td>{{ employee.employeeName }}</td>
            <td>{{ employee.designation }}</td>
            <!-- <td>{{ employee.location }}</td> -->
            <td>{{ employee.district }}</td>
            <!-- <td>{{ employee.dateOfEntryIntoService | date:'dd/MM/yyyy' }}</td> -->
            <td>
              <span class="badge bg-danger">Rejected</span>
            </td>
            <td>{{ employee.rejectedBy || 'N/A' }}</td>
            <td>
              <span *ngIf="employee.remarks; else noRemarks"
                    class="text-truncate d-inline-block"
                    style="max-width: 150px;"
                    [title]="employee.remarks">
                {{ employee.remarks }}
              </span>
              <ng-template #noRemarks>
                <span class="text-muted">No remarks</span>
              </ng-template>
            </td>
            <td>{{ employee.updatedAt | date:'dd/MM/yyyy HH:mm' || 'N/A' }}</td>
            <td>
              <button class="btn btn-sm btn-primary me-1" data-toggle="modal" data-target="#employeeModal" (click)="viewEmployee(employee)" title="View Details">
                <i class="bi bi-eye"></i>
              </button>
              <button *ngIf="employee.remarks" class="btn btn-sm btn-warning me-1" data-toggle="modal" data-target="#remarksModal" (click)="viewRemarks(employee)" title="View Remarks">
                <i class="bi bi-chat-text"></i>
              </button>
              <button *ngIf="employee.remarks" class="btn btn-sm btn-success me-1" (click)="sendToOperator(employee)" title="Send To Operator">
                <i class="bi bi-send"></i>
              </button>
  
            </td>
          </tr>
          <tr *ngIf="filteredEmployees.length === 0">
            <td colspan="11" class="text-center">
              <div class="py-4">
                <i class="bi bi-inbox text-muted" style="font-size: 48px;"></i>
                <h5 class="mt-3 text-muted">No Rejected Employees Found</h5>
                <p class="text-muted">No employees have been rejected yet or they don't match your search criteria.</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <label>Items per page:</label>
        <select class="form-control form-control-sm d-inline-block w-auto">
          <option>10</option>
          <option>20</option>
          <option>50</option>
        </select>
      </div>
      <nav>
        <ul class="pagination">
          <li class="page-item"><a class="page-link" href="#">1</a></li>
          <li class="page-item"><a class="page-link" href="#">2</a></li>
          <li class="page-item"><a class="page-link" href="#">3</a></li>
          <li class="page-item"><a class="page-link" href="#">Next</a></li>
        </ul>
      </nav>
    </div>
  
    <!-- Employee Details Modal -->
    <div class="modal fade" id="employeeModal" tabindex="-1" role="dialog" aria-labelledby="employeeModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="employeeModalLabel">Employee Details</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" *ngIf="selectedEmployee">
            <div class="row">
              <div class="col-md-6">
                <h6 class="text-primary">Personal Information</h6>
                <p><strong>Employee ID:</strong> {{ selectedEmployee.empId }}</p>
                <p><strong>Name:</strong> {{ selectedEmployee.employeeName }}</p>
                <p><strong>Father's Name:</strong> {{ selectedEmployee.fatherName }}</p>
                <p><strong>Date of Birth:</strong> {{ selectedEmployee.dateOfBirth | date:'dd/MM/yyyy' }}</p>
                <p><strong>Religion:</strong> {{ selectedEmployee.religion }}</p>
                <p><strong>Community:</strong> {{ selectedEmployee.community }}</p>
                <p><strong>District:</strong> {{ selectedEmployee.district }}</p>
              </div>
              <div class="col-md-6">
                <h6 class="text-primary">Professional Information</h6>
                <p><strong>Authority:</strong> {{ selectedEmployee.Authority }}</p>
                <p><strong>Designation:</strong> {{ selectedEmployee.designation }}</p>
                <p><strong>Date of Entry:</strong> {{ selectedEmployee.dateOfEntryIntoService | date:'dd/MM/yyyy' }}</p>
                <p><strong>Education:</strong> {{ selectedEmployee.educationalQualification }}</p>
                <p><strong>Personal Marks:</strong> {{ selectedEmployee.personalIdentificationMarks }}</p>
                <p><strong>Rejected By:</strong> {{ selectedEmployee.rejectedBy || 'N/A' }}</p>
                <p *ngIf="selectedEmployee.rejectedDate"><strong>Rejected Date:</strong> {{ selectedEmployee.rejectedDate | date:'dd/MM/yyyy' }}</p>
                <p *ngIf="selectedEmployee.updatedAt"><strong>Last Updated:</strong> {{ selectedEmployee.updatedAt | date:'dd/MM/yyyy HH:mm' }}</p>
              </div>
            </div>
  
            <!-- Remarks Section -->
            <div class="row mt-3" *ngIf="selectedEmployee.remarks">
              <div class="col-12">
                <h6 class="text-danger">Rejection Remarks</h6>
                <div class="alert alert-danger" role="alert">
                  <i class="bi bi-exclamation-triangle-fill me-2"></i>
                  <strong>Reason for Rejection:</strong>
                  <p class="mb-0 mt-2">{{ selectedEmployee.remarks }}</p>
                </div>
              </div>
            </div>
  
            <div class="row mt-3">
              <div class="col-12">
                <p><strong>Location:</strong> {{ selectedEmployee.location }}</p>
              </div>
            </div>
  
            <div class="row mt-3" *ngIf="selectedEmployee.serviceEntries && selectedEmployee.serviceEntries.length > 0">
              <div class="col-12">
                <h6 class="text-primary">Service History</h6>
                <div class="table-responsive">
                  <table class="table table-sm table-bordered">
                    <thead>
                      <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let service of selectedEmployee.serviceEntries">
                        <td>{{ service.date | date:'dd/MM/yyyy' }}</td>
                        <td>{{ service.type }}</td>
                        <td>{{ service.status }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
  
            <div class="row mt-3" *ngIf="selectedEmployee.leaveEntries && selectedEmployee.leaveEntries.length > 0">
              <div class="col-12">
                <h6 class="text-primary">Leave Balance</h6>
                <div class="table-responsive">
                  <table class="table table-sm table-bordered">
                    <thead>
                      <tr>
                        <th>Leave Type</th>
                        <th>Balance Count</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let leave of selectedEmployee.leaveEntries">
                        <td>{{ leave.leaveType }}</td>
                        <td>{{ leave.leaveBalanceCount }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  
    <!-- PDF Files Modal -->
    <div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="pdfModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="pdfModalLabel">Uploaded PDF Files</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div *ngIf="selectedEmployeePDFs && selectedEmployeePDFs.length > 0; else noPDFs">
              <div class="row">
                <div class="col-md-4 mb-3" *ngFor="let pdf of selectedEmployeePDFs; let i = index">
                  <div class="pdf-card border rounded p-3 text-center h-100">
                    <div class="pdf-icon mb-2" (click)="openPDF(pdf)" style="cursor: pointer;">
                      <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                    </div>
                    <h6 class="pdf-title text-truncate" [title]="pdf.name">{{ pdf.name }}</h6>
                    <small class="text-muted">{{ formatFileSize(pdf.size) }}</small>
                    <div class="mt-2">
                      <button class="btn btn-sm btn-primary" (click)="openPDF(pdf)" title="Open PDF">
                        <i class="bi bi-eye"></i> View
                      </button>
                      <button class="btn btn-sm btn-secondary ms-1" (click)="downloadPDF(pdf)" title="Download PDF">
                        <i class="bi bi-download"></i> Download
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <ng-template #noPDFs>
              <div class="text-center py-4">
                <i class="bi bi-file-earmark-pdf text-muted" style="font-size: 64px;"></i>
                <h5 class="mt-3 text-muted">No PDF Files Found</h5>
                <p class="text-muted">This employee hasn't uploaded any PDF files yet.</p>
              </div>
            </ng-template>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  
    <!-- Remarks Modal -->
    <div class="modal fade" id="remarksModal" tabindex="-1" role="dialog" aria-labelledby="remarksModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="remarksModalLabel">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              Rejection Remarks
            </h5>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" *ngIf="selectedEmployee">
            <div class="mb-3">
              <h6 class="text-primary">Employee Information</h6>
              <p><strong>Employee ID:</strong> {{ selectedEmployee.empId }}</p>
              <p><strong>Name:</strong> {{ selectedEmployee.employeeName }}</p>
              <p><strong>Designation:</strong> {{ selectedEmployee.designation }}</p>
            </div>
  
            <div class="mb-3">
              <h6 class="text-danger">Rejection Details</h6>
              <p><strong>Rejected By:</strong> {{ selectedEmployee.rejectedBy || 'N/A' }}</p>
              <p *ngIf="selectedEmployee.rejectedDate"><strong>Rejected Date:</strong> {{ selectedEmployee.rejectedDate | date:'dd/MM/yyyy HH:mm' }}</p>
              <p *ngIf="selectedEmployee.updatedAt"><strong>Last Updated:</strong> {{ selectedEmployee.updatedAt | date:'dd/MM/yyyy HH:mm' }}</p>
            </div>
  
            <div class="alert alert-danger" role="alert">
              <h6 class="alert-heading">
                <i class="bi bi-chat-text me-2"></i>
                Reason for Rejection:
              </h6>
              <hr>
              <p class="mb-0">{{ selectedEmployee.remarks || 'No remarks provided' }}</p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  