<div class="mt-4">
    <h2>Employee Details</h2>
  
  
  <div class="row mb-3">
    <!-- <div class="col-md-6 d-flex align-items-center">
      <input type="text" class="form-control me-2" placeholder="Search employees..." [(ngModel)]="searchTerm" (input)="searchEmployees()">
      <button class="btn btn-outline-secondary flex-shrink-0" type="button" (click)="searchEmployees()" title="Search">
        <i class="bi bi-search"></i> Search
      </button>
    </div> -->
  </div>
    <div class="table-responsive">
      <table class="table table-striped table-bordered">
        <thead>
          <tr >
            <!-- <th class="th-color">Emp ID</th> -->
            <th class="th-color">Name</th>
            <th class="th-color">Designation</th>
            <th class="th-color">District</th>
            <th class="th-color">Date of Entry</th>
            <th class="th-color">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let employee of rmLists; let i = index">
            <!-- <td>{{ employee.empId || employee.ecpfNumber }}</td> -->
            <td>{{ employee.employeeName }}</td>
            <td>{{ employee.designation }}</td>
            <td>{{ employee.district }}</td>
            <td>{{ employee.dateOfEntry | date:'dd/MM/yyyy' }}</td>
            <td>
              <!-- <button class="btn btn-sm btn-primary me-1" data-toggle="modal" data-target="#employeeModal" (click)="viewEmployee(employee)" title="View">
                <i class="bi bi-eye"></i>
              </button>
              <button *ngIf="employee.remarks" class="btn btn-sm btn-warning me-1" data-toggle="modal" data-target="#remarksModal" (click)="viewRemarks(employee)" title="View Remarks">
                <i class="bi bi-chat-text"></i>
              </button> -->
  
              <!-- <button class="btn btn-sm btn-success me-1" (click)="sendToUser(employee)" title="Send to User">
                <i class="bi bi-send"></i>
              </button> -->
              <button
              class="btn btn-sm btn-success me-1"
              data-bs-toggle="modal"
              data-bs-target="#approvalModal"
              (click)="openApprovalModal(employee)"
              title="Approve">
              <i class="bi bi-check-circle"></i>
            </button>
              <!-- <button class="btn btn-sm btn-danger me-1" data-toggle="modal" data-target="#rejectionModal" (click)="rejectEmployee(employee)" title="Reject">
                <i class="bi bi-x-circle"></i>
              </button> -->

            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="modal fade" id="approvalModal" tabindex="-1" aria-labelledby="approvalModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="approvalModalLabel">Approve Employee</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <form [formGroup]="statusFormGroup">
                <div class="mb-3">
                  <label for="employeeName" class="form-label">Employee Name</label>
                  <input type="text" class="form-control" [value]="selectedEmployee?.employeeName" readonly>
                </div>
                <div class="mb-3">
                  <label for="status" class="form-label">Status</label>
                  <select class="form-select" formControlName="status">
                    <option value="">Select Status</option>
                    <option value="ACCEPTED">Approved</option>
                    <option value="REJECTED">Rejected</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="remarks" class="form-label">Remarks</label>
                  <textarea class="form-control" formControlName="remarks" rows="3"></textarea>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button class="btn btn-primary" (click)="statusSubmit(selectedEmployee.id)">Submit</button>
            </div>
          </div>
        </div>
      </div>