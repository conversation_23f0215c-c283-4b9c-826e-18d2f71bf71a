.sidebar {
    width: 250px;
    background-color: #203664;
    height: 100%;
    padding: 20px;
    min-height: 100vh;
}

.btn-link {
    display: block;
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 10px;
    cursor: pointer;
    color: white;
}

.btn-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
}

.link-content {
    display: flex;
    align-items: center;
}

.link-icon-wrapper {
    margin-right: 10px;
}

.link-label {
    flex-grow: 1;
}

.link-expand-wrapper {
    margin-left: auto;
}

.submenus {
    padding-left: 20px;
}

.sub-item {
    padding: 5px 0;
}

.sub-item-dot {
    width: 6px;
    height: 6px;
    background-color: #6c757d;
    border-radius: 50%;
    margin-right: 10px;
}

.sub-item a {
    color: white;
    text-decoration: none;
}

.sub-item a:hover {
    color: #ccc;
    text-decoration: none;
}

.menus {
    list-style: none;
    padding: 0;
    margin: 0;
}

.item {
    margin-bottom: 5px;
}

/* Remove underlines from all links and text elements */
.sidebar a,
.sidebar button,
.sidebar .btn-link,
.sidebar .link-label,
.sidebar .label {
    text-decoration: none !important;
}

.sidebar a:hover,
.sidebar button:hover,
.sidebar .btn-link:hover,
.sidebar .link-label:hover,
.sidebar .label:hover {
    text-decoration: none !important;
}

/* Ensure no underlines on focus states */
.sidebar a:focus,
.sidebar button:focus,
.sidebar .btn-link:focus {
    text-decoration: none !important;
    outline: none;
}

/* Remove underlines from routerLink elements */
.sidebar [routerLink],
.sidebar [routerLink]:hover,
.sidebar [routerLink]:focus,
.sidebar [routerLink]:active {
    text-decoration: none !important;
}