import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Employee } from '../form-fill/employee.model';
import { ActivatedRoute, Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { StatusServiceService } from '../../services/status-service.service';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';
interface ApprovalData {
  status: string;
  remarks: string;
}
@Component({
  selector: 'app-view-pending',
  imports: [CommonModule,ReactiveFormsModule,FormsModule],
  templateUrl: './view-pending.component.html',
  styleUrl: './view-pending.component.css'
})
export class ViewPendingComponent implements OnInit{
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  searchTerm: string = '';
  selectedEmployee: Employee | null = null;
  selectedEmployeePDFs: File[] = [];
  downloadingFiles: Set<number> = new Set();
  isLoading: boolean = false;

  constructor(
    private employeeService: EmployeeService,
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.loadEmployees();
  }

  loadEmployees(): void {
    this.isLoading = true;
    // Fetch employees from API
    this.http.get<any[]>('http://localhost:8082/employee/getList')
      .subscribe({
        next: (response) => {
          console.log('Employees fetched from API:', response);
          this.employees = this.mapApiResponseToEmployees(response);
          this.filteredEmployees = [...this.employees];
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching employees from API:', error);
          // Fallback to localStorage if API fails
          this.loadEmployeesFromLocalStorage();
          this.isLoading = false;
        }
      });
  }

  private loadEmployeesFromLocalStorage(): void {
    // Fallback method - load from localStorage if API fails
    const existingEmployees = this.employeeService.loadEmployees();

    if (existingEmployees.length === 0) {
      // this.employeeService.generateSampleData();
      this.employees = this.employeeService.loadEmployees();
    } else {
      this.employees = existingEmployees;
    }

    this.filteredEmployees = [...this.employees];
  }

  private mapApiResponseToEmployees(apiData: any[]): Employee[] {
    return apiData.map(emp => ({
      // Required fields from API response
      id: emp.id || emp.employeeId || emp.ecpfNumber || '',
      empId: emp.ecpfNumber || emp.id?.toString() || '',
      employeeName: emp.employeeName || '',
      designation: emp.currentDesignation || '',
      Authority: emp.section || '',
      district: emp.district || '',
      location: emp.district || '', // Using district as location since nativePlaceAndTaluk not in API
      fatherName: '', // Not in API response
      dateOfBirth: '', // Not in API response
      religion: '', // Not in API response
      community: '', // Not in API response
      personalIdentificationMarks: '', // Not in API response
      dateOfEntryIntoService: emp.dateOfEntry || '',
      createdAt: emp.createdAt || '',
      // Missing required fields from Employee interface
      remarks: emp.remarks || '',
      rejectedBy: emp.rejectedBy || '',
      status: emp.status || '',

      educationalQualification: '', // Not in API response
      serviceRecords: [],
      leaveEntries: [],

      // Optional fields from API response
      mainEmployeeType: '',
      ecpfNumber: emp.ecpfNumber || '',
      motherName: '',
      email: emp.email || '',
      gender: emp.gender || '',
      registerNumber: emp.registerNumber || '',
      caste: '',
      nativeDistrict: emp.district || '',
      nativeAndTaluk: emp.district || '',
      dateOfEntry: emp.dateOfEntry || '',
      presentAddress: '',
      permanentAddress: '',
      personalIdMark1: '',
      personalIdMark2: '',
      basicQualification1: '',
      schoolName: '',
      collegeName: '',
      universityName: '',
      bankAccountNo: '',
      ifscCode: '',
      bankName: '',
      panNumber: emp.panNumber || '',
      uanNumber: '',
      aadharNumber: '',

      // Array fields - empty since not in API response
      serviceEntries: [],
      trainingEntries: [],
      punishmentEntries: [],
      nominationEntries: [],
      educationEnties: [] // Fixed: changed from educationEntries to educationEnties to match the interface
    }));
  }




  searchEmployees(): void {
    if (!this.searchTerm.trim()) {
      this.filteredEmployees = [...this.employees];
      return;
    }

    const searchTermLower = this.searchTerm.toLowerCase();
    this.filteredEmployees = this.employees.filter(employee =>
      employee.employeeName.toLowerCase().includes(searchTermLower) ||
      employee.designation.toLowerCase().includes(searchTermLower) ||
      employee.district.toLowerCase().includes(searchTermLower) ||
      employee.ecpfNumber.toLowerCase().includes(searchTermLower) ||
      employee.email.toLowerCase().includes(searchTermLower)
    );
  }

  viewEmployee(employee: Employee): void {
    console.log('Viewing employee details for employee:', employee);
    console.log('Employee ID to be used for API call:', employee.id);

    // Use employee.id specifically for the API call
    if (!employee.id) {
      console.error('No employee.id found');
      this.selectedEmployee = employee;
      alert('Could not find employee ID. Showing basic information.');
      return;
    }

    // Call API to get complete employee details using employee.id
    this.http.get<any>(`http://localhost:8082/employee/getEmp/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Complete employee data received:', response);
          this.selectedEmployee = this.mapCompleteEmployeeData(response);
        },
        error: (error) => {
          console.error('Error fetching employee details:', error);
          console.error('API URL called:', `http://localhost:8082/employee/getEmp/${employee.id}`);
          // Fallback to existing data if API fails
          this.selectedEmployee = employee;
          alert('Could not load complete employee details. Showing basic information.');
        }
      });
  }

  editEmployee(employee: Employee): void {
    this.router.navigate(['/dashboard'], {
      queryParams: { edit: true, empId: employee.id || employee.empId }
    });
  }

  // Simple method to get nominee photo URL for template use
  getNomineeDisplayPhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // If already processed, return it
    if ((nominee as any).nomineePhotoUrl) {
      return (nominee as any).nomineePhotoUrl;
    }

    // If has raw photo path, construct URL immediately
    if (nominee.nomineePhoto && nominee.nomineePhoto !== 'pending_upload' && nominee.nomineePhoto.trim() !== '') {
      const baseUrl = 'http://localhost:8082';
      const photoPath = nominee.nomineePhoto;

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      // Store for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      return fullPhotoUrl;
    }

    return null;
  }

  // View nominee photo in larger size
  viewNomineePhoto(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const photoUrl = (nominee as any).nomineePhotoUrl || this.getNomineeDisplayPhotoUrl(nomineeIndex);

    if (photoUrl) {
      // Open photo in new window/tab
      window.open(photoUrl, '_blank');
    } else {
      console.log('No photo available for nominee', nomineeIndex + 1);
    }
  }

  // Handle nominee image error
  onNomineeImageError(event: any, nomineeIndex: number): void {
    console.log(`Error loading nominee ${nomineeIndex + 1} photo:`, event);
    // Hide the image and show placeholder
    event.target.style.display = 'none';
  }

  deleteEmployee(employee: Employee): void {
    Swal.fire({
      title: 'Delete Employee',
      text: `Are you sure you want to delete ${employee.employeeName}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Delete!',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        const employeeId = employee.id || employee.empId;

        // Show loading
        Swal.fire({
          title: 'Deleting...',
          text: 'Please wait while we delete the employee record',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        // Try to delete from API first
        this.http.delete(`http://localhost:8082/employee/deleteEmp/${employeeId}`)
          .subscribe({
            next: (response) => {
              console.log('Employee deleted successfully from API:', response);

              Swal.fire({
                title: 'Deleted!',
                text: `${employee.employeeName} has been deleted successfully!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              }).then(() => {
                this.loadEmployees(); // Refresh the list
              });
            },
            error: (error) => {
              console.error('Error deleting employee from API:', error);

              // Fallback to localStorage deletion
              this.employeeService.deleteEmployee(employee.empId);

              Swal.fire({
                title: 'Deleted!',
                text: `${employee.employeeName} has been deleted from local storage!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              }).then(() => {
                this.loadEmployees(); // Refresh the list
              });
            }
          });
      }
    });
  }

  // Navigation methods
  addNewEmployee(): void {
    this.router.navigate(['/dashboard']);
  }

  refreshData(): void {
    console.log('Refreshing employee data from API...');
    this.loadEmployees();
    this.searchTerm = ''; // Clear search term
  }

  private mapCompleteEmployeeData(apiData: any): Employee {
    return {
      // Basic Information
      id: apiData.id || apiData.employeeId || apiData.ecpfNumber || '',
      empId: apiData.ecpfNumber || apiData.empId || '',
      ecpfNumber: apiData.ecpfNumber || '',
      employeeName: apiData.employeeName || '',
      designation: apiData.currentDesignation || apiData.designation || '',
      Authority: apiData.section || '',
      district: apiData.district || '',
      location: apiData.nativePlaceAndTaluk || '',
      fatherName: apiData.fatherName || apiData.profile?.fatherName || '',
      motherName: apiData.motherName || apiData.profile?.motherName || '',
      dateOfBirth: apiData.dateOfBirth || apiData.profile?.dateOfBirth || '',
      religion: apiData.religion || '',
      createdAt: apiData.createdAt || '',
      community: apiData.community || apiData.profile?.community || '',
      caste: apiData.caste || apiData.profile?.caste || '',
      personalIdentificationMarks: (apiData.personalIdentificationmark1 || '') +
        (apiData.personalIdentificationmark2 ? ', ' + apiData.personalIdentificationmark2 : ''),
      dateOfEntryIntoService: apiData.dateOfEntry || '',
      educationalQualification: this.formatEducationQualifications(apiData.educationQualifications),

      // Missing required fields
      status: apiData.status || '',
      dateOfEntry: apiData.dateOfEntry || '',
      educationEnties: this.mapEducationEntries(apiData.educationQualifications || []),

      // Contact Information
      email: apiData.email || apiData.profile?.email || '',
      presentAddress: apiData.presentAddress || apiData.profile?.presentaddress || '',
      permanentAddress: apiData.permanentAddress || apiData.profile?.permanentaddress || '',

      // Account Details
      bankAccountNo: apiData.accountDetails?.bankaccountnumber || '',
      ifscCode: apiData.accountDetails?.ifsccode || '',
      bankName: apiData.accountDetails?.bankname || '',
      panNumber: apiData.panNumber || '',
      uanNumber: apiData.accountDetails?.uannumber || '',
      aadharNumber: apiData.accountDetails?.aadharnumber || '',

      // Service Records
      serviceRecords: apiData.serviceHistory || [],
      leaveEntries: this.mapLeaveBalances(apiData.leaveBalances || []),

      // Additional Information
      registerNumber: apiData.registerNumber || '',
      personalIdMark1: apiData.personalIdentificationmark1 || '',
      personalIdMark2: apiData.personalIdentificationmark2 || '',
      nativeAndTaluk: apiData.nativePlaceAndTaluk || '',

      // Arrays
      serviceEntries: this.mapServiceHistory(apiData.serviceHistory || []),
      trainingEntries: this.mapTrainingDetails(apiData.trainingDetails || []),
      punishmentEntries: this.mapPunishmentDetails(apiData.punishmentDetails || []),
      nominationEntries: this.mapNominees(apiData.nominees || []),

      // Salary Details
      salaryDetails: {
        lastSalaryRevisedDate: apiData.salaryDetails?.lastSalaryRevisedDate || '',
        group: apiData.salaryDetails?.group || apiData.group || '',
        payband: apiData.salaryDetails?.payband || apiData.payband || '',
        gradepay: apiData.salaryDetails?.gradepay || apiData.gradepay || ''
      },

      remarks: apiData.remarks || '',
      rejectedBy: apiData.rejectedBy || ''
    };
  }

  private formatEducationQualifications(educationQualifications: any[]): string {
    if (!educationQualifications || educationQualifications.length === 0) {
      return 'Not specified';
    }
    return educationQualifications.map(edu => edu.qualification).join(', ');
  }

  private mapLeaveBalances(leaveBalances: any[]): any[] {
    return leaveBalances.map(leave => ({
      leaveType: leave.leaveType || '',
      leaveBalanceCount: leave.openingBalance || leave.closingBalance || 0,
      openingBalance: leave.openingBalance || 0,
      closingBalance: leave.closingBalance || 0,
      entryDate: leave.entryDate || ''
    }));
  }

  private mapServiceHistory(serviceHistory: any[]): any[] {
    return serviceHistory.map(service => ({
      date: service.date || service.dateofappointment || service.joiningdate || service.fromdate || service.promoteddate || service.punishmentdate || '',
      type: service.type || '',
      status: service.status || 'Active',

      // Appointment fields
      appointmentType: service.appointmenttype || '',
      modeOfAppointment: service.modeofappointment || '',
      dateOfAppointment: service.dateofappointment || '',
      proceedingOrderNo: service.proceedingorderno || '',
      proceedingOrderDate: service.proceedingorderdate || '',

      // Promotion fields
      joiningDate: service.joiningdate || '',
      fromDesignation: service.fromdesignation || '',
      toPromoted: service.topromoted || '',
      promotedDate: service.promoteddate || '',

      // Transfer fields
      fromDate: service.fromdate || '',
      toDate: service.todate || '',
      fromPlace: service.fromplace || '',
      toPlace: service.toplace || '',

      // Increment fields
      typeOfIncrement: service.typeofincrement || '',
      incrementType: service.incrementtype || '',

      // Deputation fields
      designation: service.designation || '',
      originalDesignation: service.originaldesignation || '',
      parentDepartment: service.parentdepartment || '',

      // Punishment fields
      punishmentType: service.punishmenttype || '',
      punishmentDate: service.punishmentdate || '',
      caseDetails: service.casedetails || ''
    }));
  }

  private mapTrainingDetails(trainingDetails: any[]): any[] {
    return trainingDetails.map(training => ({
      trainingType: training.trainingtype || '',
      trainingDate: training.date || ''
    }));
  }

  private mapPunishmentDetails(punishmentDetails: any[]): any[] {
    return punishmentDetails.map(punishment => ({
      punishmentType: punishment.punishmenttype || '',
      punishmentDate: punishment.date || '',
      caseDetails: punishment.casedetails || ''
    }));
  }

  private mapNominees(nominees: any[]): any[] {
    return nominees.map(nominee => ({
      nomineeName: nominee.nomineename || '',
      address: nominee.address || '',
      relationship: nominee.relationship || '',
      age: nominee.age || 0,
      percentageOfShare: nominee.percentageofshare || 0,
      gender: nominee.gender || ''
    }));
  }

  private mapEducationEntries(educationQualifications: any[]): any[] {
    return educationQualifications.map(education => ({
      qualification: education.qualification || '',
      courseName: education.coursename || '',
      instituteName: education.schoolname || education.collegename || education.universityname || '',
      universityName: education.universityname || '',
      specialization: education.specialization || ''
    }));
  }

  // PDF related methods
  viewPDFs(employee: Employee): void {
    this.selectedEmployee = employee;
    console.log('Fetching documents for employee ID:', employee.id);

    if (!employee.id) {
      console.error('No employee ID found for document fetch');
      this.selectedEmployeePDFs = [];
      return;
    }

    // Call API to get employee documents
    this.http.get<any[]>(`http://localhost:8082/documents/employee/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Documents received:', response);
          this.selectedEmployeePDFs = this.mapDocumentsToFiles(response);
        },
        error: (error) => {
          console.error('Error fetching documents:', error);
          // Fallback to empty array if API fails
          this.selectedEmployeePDFs = [];
          alert('Could not load documents for this employee.');
        }
      });
  }

  private mapDocumentsToFiles(documents: any[]): any[] {
    // Filter to keep only PDF files, excluding photos and signatures
    const pdfDocuments = documents.filter(doc => {
      const fileName = (doc.fileName || '').toLowerCase();
      const fileType = (doc.fileType || '').toLowerCase();

      // Check if it's a PDF file
      const isPdf = fileType === 'application/pdf' || fileName.endsWith('.pdf');

      // Exclude photo and signature files (even if they are PDFs)
      const photoSignatureKeywords = [
        'photo', 'signature', 'sign', 'pic', 'image', 'img', 'passport',
        'profile', 'headshot', 'mugshot', 'selfie', 'avatar', 'thumb',
        'thumbnail', 'jpeg', 'jpg', 'png', 'gif', 'bmp', 'tiff'
      ];

      const isPhotoOrSignature = photoSignatureKeywords.some(keyword =>
        fileName.includes(keyword)
      );

      // Also check if the file type is an image type
      const isImageType = fileType.startsWith('image/');

      return isPdf && !isPhotoOrSignature && !isImageType;
    });

    return pdfDocuments.map(doc => ({
      id: doc.id,
      employeeId: doc.employeeId,
      employeeName: doc.employeeName,
      name: doc.fileName,
      type: doc.fileType,
      size: doc.fileSize,
      filePath: doc.filePath,
      fileUrl: doc.fileUrl,
      uploadDate: doc.uploadDate,
      downloadUrl: `http://localhost:8082${doc.fileUrl}`
    }));
  }

  private generateSamplePDFs(empId: string): File[] {
    // Generate sample PDF files for demonstration
    const samplePDFs = [
      new File([''], `${empId}_Resume.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_Certificate.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_ID_Proof.pdf`, { type: 'application/pdf' })
    ];

    // Add some realistic file sizes
    Object.defineProperty(samplePDFs[0], 'size', { value: 245760 }); // 240 KB
    Object.defineProperty(samplePDFs[1], 'size', { value: 512000 }); // 500 KB
    Object.defineProperty(samplePDFs[2], 'size', { value: 1048576 }); // 1 MB

    return samplePDFs;
  }

  openPDF(file: any): void {
    console.log('Opening PDF:', file);

    if (file.downloadUrl) {
      // Open the actual PDF file from the server
      window.open(file.downloadUrl, '_blank');
    } else {
      // Fallback for old File objects
      alert(`Opening PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would open the PDF file.`);
    }
  }

  downloadPDF(file: any): void {
    console.log('Downloading PDF:', file);

    if (file.downloadUrl) {
      // Add file to downloading set
      this.downloadingFiles.add(file.id);

      // Use HttpClient to download the file as blob
      this.http.get(file.downloadUrl, { responseType: 'blob' })
        .subscribe({
          next: (blob) => {
            // Create blob URL and force download
            const blobUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = file.name;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL
            setTimeout(() => {
              window.URL.revokeObjectURL(blobUrl);
            }, 100);

            // Remove from downloading set
            this.downloadingFiles.delete(file.id);
            console.log('File downloaded successfully:', file.name);
          },
          error: (error) => {
            console.error('Error downloading file:', error);
            // Remove from downloading set
            this.downloadingFiles.delete(file.id);
            alert('Error downloading file. Please try again.');
          }
        });
    } else {
      // Fallback for old File objects
      alert(`Downloading PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would download the PDF file.`);
    }
  }

  isDownloading(fileId: number): boolean {
    return this.downloadingFiles.has(fileId);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
