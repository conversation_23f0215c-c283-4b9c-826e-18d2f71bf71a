<body>
  <div class="wrapper">
    <div class="left-panel">
      <img src="logo_tncsc.png" alt="TNCSC Logo" />
    </div>

    <div class="right-panel">
      <div class="right-content">
        <h2 class="heading">DATA DIGITIZATION</h2>

        <!-- Toggle Buttons -->
        <div class="user-type-toggle mb-3">
          <button class="btn btn-toggle" [class.active]="userType === 'admin'" (click)="setUserType('admin')">Admin/Operator</button>
          <button class="btn btn-toggle" [class.active]="userType === 'employee'" (click)="setUserType('employee')">Employee</button>
        </div>

        <div class="login-form">
          <div class="flip-container" [class.flipped]="userType === 'employee'">
            <div class="flipper">
              <!-- ADMIN/OPERATOR FORM -->
              <div class="form-front">
                <form [formGroup]="formGroup" (ngSubmit)="onLogin()">
                  <h3 class="text-center mb-4">Login</h3>
                  <div class="field">
                    <label for="username" class="form-label">Username</label>
                    <input id="username" class="form-control" formControlName="username" />
                  </div>
                  <div class="field position-relative">
                    <label for="password" class="form-label">Password</label>
                    <input id="password" [type]="showPassword ? 'text' : 'password'" class="form-control" formControlName="password" />
                    <span class="bi password-toggle" [class.bi-eye]="!showPassword" [class.bi-eye-slash]="showPassword" (click)="togglePassword()"></span>
                  </div>
                  <div *ngIf="errorMessage" class="alert alert-danger">{{ errorMessage }}</div>
                  <button class="btn btn-primary w-100" type="submit" [disabled]="formGroup.invalid">Login</button>
                </form>
              </div>

              <!-- EMPLOYEE FORM -->
              <div class="form-back">
                <form [formGroup]="employeeLoginFormGroup" (ngSubmit)="loginEmployee()">
                  <h3 class="text-center mb-4">Employee Login</h3>

                  <!-- ECPF -->
                  <div class="mb-3">
                    <label for="empId" class="form-label">ECPF</label>
                    <input id="empId" type="text" class="form-control" formControlName="empId" [class.is-invalid]="isFieldInvalid('empId')" />
                    <div class="invalid-feedback" *ngIf="isFieldInvalid('empId')">
                      ECPF is required.
                    </div>
                  </div>

                  <!-- PAN Number -->
                  <div class="mb-3">
                    <label for="panNumber" class="form-label">PAN Number</label>
                    <input id="panNumber" type="text" class="form-control" formControlName="panNumber" [class.is-invalid]="isFieldInvalid('panNumber')" />
                    <div class="invalid-feedback" *ngIf="isFieldInvalid('panNumber')">
                      Enter a valid PAN number (e.g., **********).
                    </div>
                  </div>

                  <!-- Alert Message -->
                  <div *ngIf="loginMessage" class="mt-3">
                    <div class="alert" [ngClass]="isLoginSuccess ? 'alert-success' : 'alert-danger'">
                      {{ loginMessage }}
                    </div>
                  </div>

                  <!-- Submit Button -->
                  <button type="submit" class="btn btn-primary w-100" [disabled]="employeeLoginFormGroup.invalid">
                    Login
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>