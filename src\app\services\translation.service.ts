import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface Translations {
  [key: string]: string;
}

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private currentLanguage = new BehaviorSubject<string>('en');
  public currentLanguage$ = this.currentLanguage.asObservable();

  private translations: { [lang: string]: Translations } = {
    en: {
      // Header
      'header.profile': 'Profile',
      'header.logout': 'Logout',

      // Sidebar
      'sidebar.dashboard': 'Dashboard',
      'sidebar.register': 'Register',
      'sidebar.form': 'Form',
      'sidebar.employeeDetails': 'Employee Details',
      'sidebar.digitization': 'Digitization',
      'sidebar.viewEmployees': 'View Employees',
      'sidebar.reports': 'Reports',
      'sidebar.settings': 'Settings',

      // Dashboard
      'dashboard.title': 'Employee Details',
      'dashboard.editTitle': 'Edit Employee Details',
      'dashboard.basicInfo': 'Basic Information',
      'dashboard.personalDetails': 'Personal Details',
      'dashboard.accountDetails': 'Account Details',
      'dashboard.serviceHistory': 'Service History Track',
      'dashboard.leaveBalance': 'Leave Balance Information',
      'dashboard.serviceRecords': 'Service Record',
      'dashboard.trainingHistory': 'Training History',
      'dashboard.punishmentDetails': 'Punishment Details',
      'dashboard.nominationDetails': 'Nomination Details',
      'dashboard.educationQualification': 'Education Qualification',
      'dashboard.familyInfo': 'Family Information',
      'dashboard.personalIdentification': 'Personal Identification',
      'dashboard.religiousSocialInfo': 'Religious & Social Information',
      'dashboard.locationServiceInfo': 'Location & Service Information',
      'dashboard.documents': 'Documents',
      'dashboard.documentUpload': 'Document Upload',
      'dashboard.uploadDocuments': 'Upload Documents',
      'dashboard.uploadInstructions': 'Upload Instructions',

      // Form Fields
      'field.empId': 'Emp ID/ECPF Number',
      'field.employeeName': 'Employee Name',
      'field.fatherName': 'Father\'s Name',
      'field.motherName': 'Mother\'s Name',
      'field.email': 'Email',
      'field.designation': 'Designation',
      'field.dateOfBirth': 'Date of Birth',
      'field.personalIdMark1': 'Personal ID Mark 1',
      'field.personalIdMark2': 'Personal ID Mark 2',
      'field.religion': 'Religion',
      'field.community': 'Community',
      'field.caste': 'Caste',
      'field.section': 'Section',
      'field.nativeDistrict': 'Native District',
      'field.nativeAndTaluk': 'Native & Taluk',
      'field.dateOfEntry': 'Date of Entry into Service',
      'field.presentAddress': 'Present Address',
      'field.permanentAddress': 'Permanent Address',
      'field.bankAccountNo': 'Bank Account Number',
      'field.ifscCode': 'IFSC Code',
      'field.bankName': 'Bank Name',
      'field.panNumber': 'PAN Number',
      'field.uanNumber': 'UAN Number',
      'field.aadharNumber': 'Aadhar Number',
      'field.trainingType': 'Training Type',
      'field.trainingDate': 'Training Date',
      'field.employeeType': 'Employee Type',
      'field.type': 'Type',
      'field.currentDesignation': 'Current Designation',
      'field.leaveType': 'Leave Type',
      'field.openingBalance': 'Opening Balance Count',
      'field.closingBalance': 'Closing Balance Count',
      'field.entryDate': 'Entry Date',
      'field.nomineeName': 'Nominee Name',
      'field.address': 'Address',
      'field.gender': 'Gender',
      'field.relationship': 'Relationship',
      'field.age': 'Age',
      'field.percentageShare': '% of Share',
      'field.qualification': 'Qualification',
      'field.courseName': 'Course Name',
      'field.schoolName': 'School Name',
      'field.collegeName': 'College Name',
      'field.universityName': 'University Name',
      'field.profilePhoto': 'Profile Photo',
      'field.signature': 'Signature',
      'field.documents': 'Documents',
      'field.registerNo': 'Register.No',
      'field.punishmentType': 'Punishment Type',
      'field.punishmentDate': 'Punishment Date',
      'field.caseDetails': 'Case Details',
      'field.foreignServiceDesignation': 'Designation (Foreign Service)',
      'field.originalDesignation': 'Original Designation',
      'field.parentDepartment': 'Parent Department',

      // Dropdown Options
      'option.selectEmployeeType': 'Select Employee Type',
      'option.permanent': 'Permanent',
      'option.seasonal': 'Seasonal',
      'option.selectDesignation': 'Select Designation',
      'option.manager': 'Manager',
      'option.assistantManager': 'Assistant Manager',
      'option.seniorManager': 'Senior Manager',
      'option.teamLead': 'Team Lead',
      'option.admin': 'Admin',
      'option.selectReligion': 'Select Religion',
      'option.hinduism': 'Hinduism',
      'option.christianity': 'Christianity',
      'option.islam': 'Islam',
      'option.sikhism': 'Sikhism',
      'option.buddhism': 'Buddhism',
      'option.other': 'Other',
      'option.selectDistrict': 'Select District',
      'option.selectLeaveType': 'Select Leave Type',
      'option.earnedLeave': 'Earned Leave',
      'option.casualLeave': 'Casual Leave',
      'option.maternityLeave': 'Maternity Leave',
      'option.hospitalLeave': 'Hospital Leave',
      'option.specialDisabilityLeave': 'Special Disability Leave',
      'option.extraordinaryLeave': 'Extraordinary Leave',
      'option.specialCasualLeave': 'Special Casual Leave',
      'option.restrictedHolidays': 'Restricted Holidays',
      'option.selectGender': 'Select Gender',
      'option.male': 'Male',
      'option.female': 'Female',
      'option.selectRelationship': 'Select Relationship',
      'option.spouse': 'Spouse',
      'option.son': 'Son',
      'option.daughter': 'Daughter',
      'option.father': 'Father',
      'option.mother': 'Mother',
      'option.brother': 'Brother',
      'option.sister': 'Sister',

      // Education & Punishment Options
      'option.selectQualification': 'Select Qualification',
      'option.8th': '8th',
      'option.10th': '10th',
      'option.12th': '12th',
      'option.highestDegree': 'Highest Degree',
      'option.selectPunishmentType': 'Select Punishment Type',
      'option.warning': 'Warning',
      'option.censure': 'Censure',
      'option.withholdingIncrement': 'Withholding of Increment',
      'option.recoveryFromPay': 'Recovery from Pay',
      'option.reductionInRank': 'Reduction in Rank',
      'option.compulsoryRetirement': 'Compulsory Retirement',
      'option.removalFromService': 'Removal from Service',
      'option.dismissalFromService': 'Dismissal from Service',
      'option.selectCaseDetails': 'Select Case Details',
      'option.stepdown': 'Punishment of step down',
      'option.suspension': 'Suspension',
      'option.penalty': 'Substantive penalty',
      'option.relived': 'Relived from duty',
      'option.blacklist': 'Black list',
      'option.dvacCases': 'Directorate of Vigilance and Anti-Corruption (DV & AC) Cases',
      'option.cscidCases': 'Civil Supplies Criminal Investigation Department (CSCID) Cases',
      'option.cbcidCases': 'Crime Branch – Criminal Investigation Department (CBCID) Cases',
      'option.criminalCases': 'Criminal Cases',
      'option.revenueRecoveryCases': 'Revenue recovery / Civil Suit Cases',
      'option.privateCases': 'Private Case',

      // Buttons
      'button.submit': 'Submit',
      'button.cancel': 'Cancel',
      'button.add': 'Add',
      'button.remove': 'Remove',
      'button.edit': 'Edit',
      'button.delete': 'Delete',
      'button.save': 'Save',
      'button.update': 'Update',

      // Messages
      'message.success': 'Operation completed successfully!',
      'message.error': 'An error occurred. Please try again.',
      'message.required': 'This field is required',
      'message.invalidEmail': 'Please enter a valid email address',

      // Placeholders
      'placeholder.selectOption': 'Select an option',
      'placeholder.enterText': 'Enter text',
      'placeholder.selectCommunity': 'Select Community',
      'placeholder.selectSection': 'Select Section',
      'placeholder.enterSection': 'Enter section',
      'placeholder.enterTrainingType': 'Enter training type',
      'placeholder.enterDesignation': 'Enter current designation',

      // Instructions
      'instruction.supportedFormats': 'Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG',
      'instruction.maxFileSize': 'Maximum file size: 10MB per file',
      'instruction.multipleFiles': 'You can select multiple files at once',
      'instruction.requiredDocuments': 'Please upload all required documents'
    },
    ta: {
      // Header
      'header.profile': 'சுயவிவரம்',
      'header.logout': 'வெளியேறு',

      // Sidebar
      'sidebar.dashboard': 'டாஷ்போர்டு',
      'sidebar.register': 'பதிவு',
      'sidebar.form': 'படிவம்',
      'sidebar.employeeDetails': 'பணியாளர் விவரங்கள்',
      'sidebar.digitization': 'டிஜிட்டல்மயமாக்கல்',
      'sidebar.viewEmployees': 'பணியாளர்களைப் பார்க்கவும்',
      'sidebar.reports': 'அறிக்கைகள்',
      'sidebar.settings': 'அமைப்புகள்',

      // Dashboard
      'dashboard.title': 'பணியாளர் விவரங்கள்',
      'dashboard.editTitle': 'பணியாளர் விவரங்களைத் திருத்து',
      'dashboard.basicInfo': 'அடிப்படை தகவல்',
      'dashboard.personalDetails': 'தனிப்பட்ட விவரங்கள்',
      'dashboard.accountDetails': 'கணக்கு விவரங்கள்',
      'dashboard.serviceHistory': 'சேவை வரலாறு தடம்',
      'dashboard.leaveBalance': 'விடுப்பு இருப்பு தகவல்',
      'dashboard.serviceRecords': 'சேவை பதிவு',
      'dashboard.trainingHistory': 'பயிற்சி வரலாறு',
      'dashboard.punishmentDetails': 'தண்டனை விவரங்கள்',
      'dashboard.nominationDetails': 'நியமன விவரங்கள்',
      'dashboard.educationQualification': 'கல்வித் தகுதி',
      'dashboard.familyInfo': 'குடும்ப தகவல்',
      'dashboard.personalIdentification': 'தனிப்பட்ட அடையாளம்',
      'dashboard.religiousSocialInfo': 'மத & சமூக தகவல்',
      'dashboard.locationServiceInfo': 'இடம் & சேவை தகவல்',
      'dashboard.documents': 'ஆவணங்கள்',
      'dashboard.documentUpload': 'ஆவண பதிவேற்றம்',
      'dashboard.uploadDocuments': 'ஆவணங்களை பதிவேற்றவும்',
      'dashboard.uploadInstructions': 'பதிவேற்ற வழிமுறைகள்',

      // Form Fields
      'field.empId': 'பணியாளர் எண்/ECPF எண்',
      'field.employeeName': 'பணியாளர் பெயர்',
      'field.fatherName': 'தந்தையின் பெயர்',
      'field.motherName': 'தாயின் பெயர்',
      'field.email': 'மின்னஞ்சல்',
      'field.designation': 'பதவி',
      'field.dateOfBirth': 'பிறந்த தேதி',
      'field.personalIdMark1': 'தனிப்பட்ட அடையாள குறி 1',
      'field.personalIdMark2': 'தனிப்பட்ட அடையாள குறி 2',
      'field.religion': 'மதம்',
      'field.community': 'சமுதாயம்',
      'field.caste': 'சாதி',
      'field.section': 'பிரிவு',
      'field.nativeDistrict': 'சொந்த மாவட்டம்',
      'field.nativeAndTaluk': 'சொந்த & தாலுக்',
      'field.dateOfEntry': 'சேவையில் நுழைந்த தேதி',
      'field.presentAddress': 'தற்போதைய முகவரி',
      'field.permanentAddress': 'நிரந்தர முகவரி',
      'field.bankAccountNo': 'வங்கி கணக்கு எண்',
      'field.ifscCode': 'IFSC குறியீடு',
      'field.bankName': 'வங்கி பெயர்',
      'field.panNumber': 'PAN எண்',
      'field.uanNumber': 'UAN எண்',
      'field.aadharNumber': 'ஆதார் எண்',
      'field.trainingType': 'பயிற்சி வகை',
      'field.trainingDate': 'பயிற்சி தேதி',
      'field.employeeType': 'பணியாளர் வகை',
      'field.type': 'வகை',
      'field.currentDesignation': 'தற்போதைய பதவி',
      'field.leaveType': 'விடுப்பு வகை',
      'field.openingBalance': 'தொடக்க இருப்பு எண்ணிக்கை',
      'field.closingBalance': 'இறுதி இருப்பு எண்ணிக்கை',
      'field.entryDate': 'நுழைவு தேதி',
      'field.nomineeName': 'நியமிதரின் பெயர்',
      'field.address': 'முகவரி',
      'field.gender': 'பாலினம்',
      'field.relationship': 'உறவுமுறை',
      'field.age': 'வயது',
      'field.percentageShare': 'பங்கு சதவீதம்',
      'field.qualification': 'தகுதி',
      'field.courseName': 'பாடநெறி பெயர்',
      'field.schoolName': 'பள்ளி பெயர்',
      'field.collegeName': 'கல்லூரி பெயர்',
      'field.universityName': 'பல்கலைக்கழகம் பெயர்',
      'field.profilePhoto': 'சுயவிவர புகைப்படம்',
      'field.signature': 'கையொப்பம்',
      'field.documents': 'ஆவணங்கள்',
      'field.registerNo': 'பதிவு எண்',
      'field.punishmentType': 'தண்டனை வகை',
      'field.punishmentDate': 'தண்டனை தேதி',
      'field.caseDetails': 'வழக்கு விவரங்கள்',
      'field.foreignServiceDesignation': 'பதவி (வெளிநாட்டு சேவை)',
      'field.originalDesignation': 'அசல் பதவி',
      'field.parentDepartment': 'பெற்றோர் துறை',

      // Dropdown Options
      'option.selectEmployeeType': 'பணியாளர் வகையை தேர்ந்தெடுக்கவும்',
      'option.permanent': 'நிரந்தர',
      'option.seasonal': 'பருவகால',
      'option.selectDesignation': 'பதவியை தேர்ந்தெடுக்கவும்',
      'option.manager': 'மேலாளர்',
      'option.assistantManager': 'உதவி மேலாளர்',
      'option.seniorManager': 'மூத்த மேலாளர்',
      'option.teamLead': 'குழு தலைவர்',
      'option.admin': 'நிர்வாகி',
      'option.selectReligion': 'மதத்தை தேர்ந்தெடுக்கவும்',
      'option.hinduism': 'இந்து மதம்',
      'option.christianity': 'கிறிஸ்தவ மதம்',
      'option.islam': 'இஸ்லாம் மதம்',
      'option.sikhism': 'சீக்கிய மதம்',
      'option.buddhism': 'பௌத்த மதம்',
      'option.other': 'மற்றவை',
      'option.selectDistrict': 'மாவட்டத்தை தேர்ந்தெடுக்கவும்',
      'option.selectLeaveType': 'விடுப்பு வகையை தேர்ந்தெடுக்கவும்',
      'option.earnedLeave': 'சம்பாதித்த விடுப்பு',
      'option.casualLeave': 'சாதாரண விடுப்பு',
      'option.maternityLeave': 'மகப்பேறு விடுப்பு',
      'option.hospitalLeave': 'மருத்துவமனை விடுப்பு',
      'option.specialDisabilityLeave': 'சிறப்பு இயலாமை விடுப்பு',
      'option.extraordinaryLeave': 'அசாதாரண விடுப்பு',
      'option.specialCasualLeave': 'சிறப்பு சாதாரண விடுப்பு',
      'option.restrictedHolidays': 'கட்டுப்படுத்தப்பட்ட விடுமுறைகள்',
      'option.selectGender': 'பாலினத்தை தேர்ந்தெடுக்கவும்',
      'option.male': 'ஆண்',
      'option.female': 'பெண்',
      'option.selectRelationship': 'உறவுமுறையை தேர்ந்தெடுக்கவும்',
      'option.spouse': 'மனைவி/கணவர்',
      'option.son': 'மகன்',
      'option.daughter': 'மகள்',
      'option.father': 'தந்தை',
      'option.mother': 'தாய்',
      'option.brother': 'சகோதரன்',
      'option.sister': 'சகோதரி',

      // Education & Punishment Options
      'option.selectQualification': 'தகுதியை தேர்ந்தெடுக்கவும்',
      'option.8th': '8வது',
      'option.10th': '10வது',
      'option.12th': '12வது',
      'option.highestDegree': 'உயர்ந்த பட்டம்',
      'option.selectPunishmentType': 'தண்டனை வகையை தேர்ந்தெடுக்கவும்',
      'option.warning': 'எச்சரிக்கை',
      'option.censure': 'கண்டனம்',
      'option.withholdingIncrement': 'சம்பள உயர்வு நிறுத்தம்',
      'option.recoveryFromPay': 'சம்பளத்திலிருந்து மீட்டெடுப்பு',
      'option.reductionInRank': 'பதவி குறைப்பு',
      'option.compulsoryRetirement': 'கட்டாய ஓய்வு',
      'option.removalFromService': 'சேவையிலிருந்து நீக்கம்',
      'option.dismissalFromService': 'சேவையிலிருந்து பணிநீக்கம்',
      'option.selectCaseDetails': 'வழக்கு விவரங்களை தேர்ந்தெடுக்கவும்',
      'option.dvacCases': 'விழிப்புணர்வு மற்றும் ஊழல் தடுப்பு இயக்ககம் (DV & AC) வழக்குகள்',
      'option.cscidCases': 'சிவில் சப்ளைஸ் குற்றவியல் விசாரணை துறை (CSCID) வழக்குகள்',
      'option.cbcidCases': 'குற்றப் பிரிவு – குற்றவியல் விசாரணை துறை (CBCID) வழக்குகள்',
      'option.criminalCases': 'குற்றவியல் வழக்குகள்',
      'option.revenueRecoveryCases': 'வருவாய் மீட்டெடுப்பு / சிவில் வழக்குகள்',
      'option.privateCases': 'தனியார் வழக்கு',

      // Buttons
      'button.submit': 'சமர்ப்பிக்கவும்',
      'button.cancel': 'ரத்து செய்',
      'button.add': 'சேர்க்கவும்',
      'button.remove': 'அகற்று',
      'button.edit': 'திருத்து',
      'button.delete': 'நீக்கு',
      'button.save': 'சேமி',
      'button.update': 'புதுப்பிக்கவும்',

      // Messages
      'message.success': 'செயல்பாடு வெற்றிகரமாக முடிந்தது!',
      'message.error': 'பிழை ஏற்பட்டது. மீண்டும் முயற்சிக்கவும்.',
      'message.required': 'இந்த புலம் தேவை',
      'message.invalidEmail': 'சரியான மின்னஞ்சல் முகவரியை உள்ளிடவும்',

      // Placeholders
      'placeholder.selectOption': 'ஒரு விருப்பத்தை தேர்ந்தெடுக்கவும்',
      'placeholder.enterText': 'உரையை உள்ளிடவும்',
      'placeholder.selectCommunity': 'சமுதாயத்தை தேர்ந்தெடுக்கவும்',
      'placeholder.selectSection': 'பிரிவை தேர்ந்தெடுக்கவும்',
      'placeholder.enterSection': 'பிரிவை உள்ளிடவும்',
      'placeholder.enterTrainingType': 'பயிற்சி வகையை உள்ளிடவும்',
      'placeholder.enterDesignation': 'தற்போதைய பதவியை உள்ளிடவும்',

      // Instructions
      'instruction.supportedFormats': 'ஆதரிக்கப்படும் வடிவங்கள்: PDF, DOC, DOCX, JPG, JPEG, PNG',
      'instruction.maxFileSize': 'அதிகபட்ச கோப்பு அளவு: ஒரு கோப்புக்கு 10MB',
      'instruction.multipleFiles': 'நீங்கள் ஒரே நேரத்தில் பல கோப்புகளைத் தேர்ந்தெடுக்கலாம்',
      'instruction.requiredDocuments': 'தேவையான அனைத்து ஆவணங்களையும் பதிவேற்றவும்'
    }
  };

  constructor() {
    // Load saved language from localStorage
    const savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang && (savedLang === 'en' || savedLang === 'ta')) {
      this.currentLanguage.next(savedLang);
    }
  }

  setLanguage(lang: string): void {
    if (lang === 'en' || lang === 'ta') {
      this.currentLanguage.next(lang);
      localStorage.setItem('selectedLanguage', lang);
    }
  }

  getCurrentLanguage(): string {
    return this.currentLanguage.value;
  }

  translate(key: string): string {
    const currentLang = this.getCurrentLanguage();
    const translation = this.translations[currentLang]?.[key];
    return translation || key;
  }

  getTranslations(lang?: string): Translations {
    const language = lang || this.getCurrentLanguage();
    return this.translations[language] || this.translations['en'];
  }
}
