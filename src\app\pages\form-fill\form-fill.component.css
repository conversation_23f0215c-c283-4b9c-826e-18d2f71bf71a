.upload-box {
  border: 2px dashed #ccc;
  border-radius: 6px;
  background-color: #f9f9f9;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.upload-box:hover {
  background-color: #f0f0f0;
}

.upload-box input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  cursor: pointer;
}

.required-label::after {
  content: " *";
  color: red;
}

.btn-edit {
  background: none;
  border: none;
  padding: 0;
}

.btn-icon-unstyled {
  background: none;
  border: none;
  padding: 0;
  font-size: 1.5rem;
}

/* .wide-card {
  width: 100%;
  max-width: 1200px;
  margin: auto;
}

.card-body {
  overflow-y: auto;
  height: auto;
} */

/* File Upload Styles */
.upload-box {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-box:hover {
  border-color: #007bff;
  background-color: #e3f2fd;
}

.upload-box.drag-over {
  border-color: #007bff;
  background-color: #e3f2fd;
  transform: scale(1.02);
}

.pdf-file-card {
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  min-height: 120px;
}

.pdf-file-card:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.pdf-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.btn-close {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 10px;
}

.uploaded-files {
  max-height: 300px;
  overflow-y: auto;
}
