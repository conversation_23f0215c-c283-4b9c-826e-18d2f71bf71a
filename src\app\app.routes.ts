// import { Routes } from '@angular/router';

// export const routes: Routes = [];
import { Routes } from '@angular/router';

import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { LayoutComponent } from './layout/layout/layout.component';
import { LoginComponent } from './auth/login/login.component';
import { FormFillComponent } from './pages/form-fill/form-fill.component';
import { ViewComponent } from './pages/view/view.component';
import { UserRegisterComponent } from './pages/user-register/user-register.component';
import { UserRoleComponent } from './pages/user-role/user-role.component';
import { UserListComponent } from './pages/user-list/user-list.component';
import { DashboardTotalComponent } from './pages/dashboard-total/dashboard-total.component';
import { authGuard } from './services/authGuard/auth.guard';
import { ApprovedComponent } from './pages/approved/approved.component';
import { PendingComponent } from './pages/pending/pending.component';
import { RejectedComponent } from './pages/rejected/rejected.component';
import { UserapprovalComponent } from './pages/userapproval/userapproval.component';
import { PendingListComponent } from './pages/pending-list/pending-list.component';
import { FirstLevelListComponent } from './pages/first-level-list/first-level-list.component';
import { SecondLevelListComponent } from './pages/second-level-list/second-level-list.component';
import { ViewPendingComponent } from './pages/view-pending/view-pending.component';
import { FormEditComponent } from './pages/form-edit/form-edit.component';
import { RmListComponent } from './pages/rm-list/rm-list.component';


export const routes: Routes = [
  // Login route (unprotected)
  { path: 'login', component: LoginComponent },

  // Protected routes with layout
  {
    path: '',
    component: LayoutComponent,
    // canActivate: [authGuard],
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'form-fill', component: FormFillComponent },
      { path: 'view', component: ViewComponent },
      { path: 'user-register', component: UserRegisterComponent },
      { path: 'user-role', component: UserRoleComponent },
      { path: 'user-list', component: UserListComponent },
      { path: 'dashboard-count', component: DashboardTotalComponent },
      {path: 'approved', component: ApprovedComponent},
      {path: 'pending', component: PendingComponent},
      {path: 'rejected', component: RejectedComponent},
      {path:'userapproval', component: UserapprovalComponent},
      {path:'pending-list', component: PendingListComponent},
      {path:'first-level-list', component: FirstLevelListComponent},
      {path:'second-level-list', component: SecondLevelListComponent},
      {path:'approved-list', component: ApprovedComponent},
      {path:'pending-view/:id', component:ViewPendingComponent},
      {path:'form-edit', component: FormEditComponent},
      {path:'approval-page', component:RmListComponent}

    ]
  },

  // Catch-all - redirect to login if no route matches
  { path: '**', redirectTo: 'login' }
];
