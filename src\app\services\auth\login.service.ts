import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from '../../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class LoginService {

   private currentUserSubject: BehaviorSubject<string>;
  public currentUser: Observable<string>;
  private baseUrl: string;
  isExpiredToken: boolean;

  constructor(
    private http: HttpClient,
  ) {
        this.currentUserSubject = new BehaviorSubject<string>('');
    this.baseUrl = environment.AUTH;
  }

  public get getAuthToken(): string {
    this.currentUserSubject = new BehaviorSubject<string>(sessionStorage.getItem('token'));

    this.currentUser = this.currentUserSubject.asObservable();
    return this.currentUserSubject.value;
  }


  public get getRole(): string {
    const jwt = this.getAuthToken;
    if (jwt) {
      const jwtData = jwt.split('.')[1];
      const decodedJwtJsonData = window.atob(jwtData);
      const decodedJwtData = JSON.parse(decodedJwtJsonData);
      return (decodedJwtData.roles).split(',')[0];
    } else {
      return "";
    }
  }

  setAuthToken(token: string): void {
    if (this.currentUserSubject) {
      this.currentUserSubject.next(token);
    } else {
      console.error('currentUserSubject is not initialized.');
    }
  }

  public get getUserId(): string {
    const jwt = this.getAuthToken;
    if (jwt) {
      const jwtData = jwt.split('.')[1];
      const decodedJwtJsonData = window.atob(jwtData);
      const decodedJwtData = JSON.parse(decodedJwtJsonData);
      return decodedJwtData.sub;
    } else {
      return "";
    }
  }

  public get getUserName(): string {
    const jwt = this.getAuthToken;
    if (jwt) {
        const jwtData = jwt.split('.')[1];
        const decodedJwtJsonData = window.atob(jwtData);
        const decodedJwtData = JSON.parse(decodedJwtJsonData);
        return decodedJwtData.sub;
    } else {
        return "";
    }
}

  isLogged(): boolean {
    if (sessionStorage.getItem('token')) {
      return true;
    }
    return false;
  }

  login(username: string, password: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/login`, { username, password });
  }

  loginUser(username: string, password: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/login/user`, { username, password });
  }

  loginEmployee(employe:any): Observable<any>{
        return this.http.post<any>(`${this.baseUrl}/login/employee`, employe);

  }



  logout() {
    const username = sessionStorage.getItem('username');

    // Call backend logout (but don't wait for it to complete)
    this.backendlogout(false, username).subscribe({
      next: (response) => {
        console.log('Backend logout successful:', response);
      },
      error: (error) => {
        console.error('Backend logout failed:', error);
        // Continue with local logout even if backend fails
      }
    });

    // Clear all session and local storage immediately
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('username');
    localStorage.removeItem('username');
    localStorage.removeItem('token');
    sessionStorage.removeItem('user_id');
    sessionStorage.removeItem('role');
    localStorage.removeItem('role');

    // Clear any other auth-related storage
    sessionStorage.removeItem('AUTH_TOKEN');
    sessionStorage.removeItem('rulesJson');

    // Reset the current user subject
    if (this.currentUserSubject != null) {
      this.currentUserSubject.next(null);
    }

    console.log('All session data cleared');
  }

  backendlogout(logstatus: any, username: any) {
    return this.http.post<any>(`${this.baseUrl}/logout`, { logstatus, username });
  }

  removeSessionData(): void {
    if (localStorage.getItem('AUTH_TOKEN')) {
      localStorage.removeItem('AUTH_TOKEN');
      localStorage.removeItem('rulesJson');
      localStorage.removeItem('role');
      localStorage.removeItem('employeeResponseData');
      localStorage.removeItem('username');
      localStorage.removeItem('employeeId');
      localStorage.removeItem('employeeName');
      localStorage.removeItem('token');
      location.replace('/');
    }
    if (this.currentUserSubject != null) {
      this.currentUserSubject.next(null);
    }
  }





  isAuthorized() {
    const roletype = localStorage.getItem('role');
    return !!roletype;
  }
  public get id(): string {
    return localStorage.getItem('id');
  }


  gettocken() {
    return sessionStorage.getItem('AUTH_TOKEN');
  }

  getRolePermission(){
    return sessionStorage.getItem('role');
  }
}
