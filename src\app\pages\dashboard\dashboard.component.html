<form [formGroup]="employeeForm" (ngSubmit)="onSubmit()">
  <!-- Service Record Card -->


  <!-- Personal Details Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.personalDetails' | translate }}</h2>

      <!-- Basic Information Section -->
      <div class="row">
        <div class="col-md-12">
          <h5 class="text-primary mb-3">{{ 'BasicInfo' | translate }}</h5>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <label class="required-label">{{ 'Emp Id / ECPF Number' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="ecpfNumber" required>
          <div class="text-danger" *ngIf="employeeForm.get('ecpfNumber')?.invalid && employeeForm.get('ecpfNumber')?.touched">
            <small *ngIf="employeeForm.get('ecpfNumber')?.errors?.['required']">Employee ID is required</small>
            <small *ngIf="employeeForm.get('ecpfNumber')?.errors?.['invalidEcpfNumber']">ECPF number must be exactly 12 digits</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'Employee Name' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="employeeName" required>
          <div class="text-danger" *ngIf="employeeForm.get('employeeName')?.invalid && (employeeForm.get('employeeName')?.touched || employeeForm.get('employeeName')?.dirty)">
            <small *ngIf="employeeForm.get('employeeName')?.errors?.['required']">Employee name is required</small>
            <small *ngIf="employeeForm.get('employeeName')?.errors?.['invalidEmployeeName']">Name should start with capital letter and contain only letters and spaces</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'Date Of Birth' | translate }}<span class="text-danger">*</span></label>
          <input type="date" class="form-control" formControlName="dateOfBirth" required>
          <div class="text-danger" *ngIf="employeeForm.get('dateOfBirth')?.invalid && employeeForm.get('dateOfBirth')?.touched">
            <small *ngIf="employeeForm.get('dateOfBirth')?.errors?.['required']">Date of birth is required</small>
            <small *ngIf="employeeForm.get('dateOfBirth')?.errors?.['ageRestriction']">Age must be 18 years or older</small>
          </div>
        </div>
      </div>

      <!-- Family Information Section -->
      <div class="row mt-4">
        <div class="col-md-12">
          <h5 class="text-primary mb-3">{{ translate('dashboard.familyInfo') }}</h5>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <label class="required-label">{{ 'Father Name' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="fatherName" required>
          <div class="text-danger" *ngIf="employeeForm.get('fatherName')?.invalid && (employeeForm.get('fatherName')?.touched || employeeForm.get('fatherName')?.dirty)">
            <small *ngIf="employeeForm.get('fatherName')?.errors?.['required']">Father name is required</small>
            <small *ngIf="employeeForm.get('fatherName')?.errors?.['invalidEmployeeName']">Name should start with capital letter and contain only letters and spaces</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'field.MotherName' | translate }}</label>
          <input type="text" class="form-control" formControlName="motherName">
          <div class="text-danger" *ngIf="employeeForm.get('motherName')?.invalid && (employeeForm.get('motherName')?.touched || employeeForm.get('motherName')?.dirty)">
            <small *ngIf="employeeForm.get('motherName')?.errors?.['invalidEmployeeName']">Name should start with capital letter and contain only letters and spaces</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'Marital Status' | translate }}<span class="text-danger">*</span></label>
          <div class="mt-2">
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" formControlName="maritalStatus" id="married" value="Married" (change)="onMaritalStatusChange($event)">
              <label class="form-check-label" for="married">Married</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" formControlName="maritalStatus" id="unmarried" value="Unmarried" (change)="onMaritalStatusChange($event)">
              <label class="form-check-label" for="unmarried">Unmarried</label>
            </div>
          </div>
          <div class="text-danger" *ngIf="employeeForm.get('maritalStatus')?.invalid && employeeForm.get('maritalStatus')?.touched">
            <small *ngIf="employeeForm.get('maritalStatus')?.errors?.['required']">Marital status is required</small>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="employeeForm.get('maritalStatus')?.value === 'Married'">
        <div class="col-md-4">
          <label class="required-label">{{ 'Spouse Name' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="spouseName">
          <div class="text-danger" *ngIf="employeeForm.get('spouseName')?.invalid && (employeeForm.get('spouseName')?.touched || employeeForm.get('spouseName')?.dirty)">
            <small *ngIf="employeeForm.get('spouseName')?.errors?.['required']">Spouse name is required</small>
            <small *ngIf="employeeForm.get('spouseName')?.errors?.['invalidEmployeeName']">Name should start with capital letter and contain only letters and spaces</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'field.MobileNumber' | translate }}<span class="text-danger">*</span></label>
          <input type="tel" class="form-control" formControlName="mobileNumber" required placeholder="Enter 10-digit mobile number">
          <div class="text-danger" *ngIf="employeeForm.get('mobileNumber')?.invalid && employeeForm.get('mobileNumber')?.touched">
            <small *ngIf="employeeForm.get('mobileNumber')?.errors?.['required']">Mobile number is required</small>
            <small *ngIf="employeeForm.get('mobileNumber')?.errors?.['invalidPhone']">Mobile number should be 10 digits starting with 6-9</small>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="employeeForm.get('maritalStatus')?.value === 'Unmarried'">
        <div class="col-md-4">
          <label class="required-label">{{ 'field.MobileNumber' | translate }}<span class="text-danger">*</span></label>
          <input type="tel" class="form-control" formControlName="mobileNumber" required placeholder="Enter 10-digit mobile number">
          <div class="text-danger" *ngIf="employeeForm.get('mobileNumber')?.invalid && employeeForm.get('mobileNumber')?.touched">
            <small *ngIf="employeeForm.get('mobileNumber')?.errors?.['required']">Mobile number is required</small>
            <small *ngIf="employeeForm.get('mobileNumber')?.errors?.['invalidPhone']">Mobile number should be 10 digits starting with 6-9</small>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <label class="required-label">{{ 'Email' | translate }}<span class="text-danger">*</span></label>
          <input type="email" class="form-control" formControlName="email" required>
          <div class="text-danger" *ngIf="employeeForm.get('email')?.invalid && employeeForm.get('email')?.touched">
            <small *ngIf="employeeForm.get('email')?.errors?.['required']">Email is required</small>
            <small *ngIf="employeeForm.get('email')?.errors?.['invalidEmail']">Please enter a valid email format</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'Gender' | translate }}<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="gender" required>
            <option value="" disabled>Select Gender</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
            <option value="Other">Other</option>
          </select>
          <div class="text-danger" *ngIf="employeeForm.get('gender')?.invalid && employeeForm.get('gender')?.touched">
            <small *ngIf="employeeForm.get('gender')?.errors?.['required']">Gender is required</small>
          </div>
        </div>
      </div>

      <!-- Personal Identification Section -->
      <div class="row mt-4">
        <div class="col-md-12">
          <h5 class="text-primary mb-3">{{ 'dashboard.personalIdentification' | translate }}</h5>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <label class="required-label">{{ 'Personal Id Mark1' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="personalIdentificationmark1" required>
          <div class="text-danger" *ngIf="employeeForm.get('personalIdentificationmark1')?.invalid && employeeForm.get('personalIdentificationmark1')?.touched">
            <small *ngIf="employeeForm.get('personalIdentificationmark1')?.errors?.['required']">Personal identification mark 1 is required</small>
          </div>
        </div>
        <div class="col-md-6">
          <label class="required-label">{{ 'Personal Id Mark2' | translate }}</label>
          <input type="text" class="form-control" formControlName="personalIdentificationmark2">
        </div>
      </div>

      <!-- Religious & Social Information Section -->
      <div class="row mt-4">
        <div class="col-md-12">
          <h5 class="text-primary mb-3">{{ 'dashboard.religiousSocialInfo' | translate }}</h5>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <label class="required-label">{{ 'Religion' | translate }}<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="religion" required>
            <option value="" disabled selected>{{ 'option.selectReligion' | translate }}</option>
            <option>{{ 'option.hinduism' | translate }}</option>
            <option>{{ 'option.christianity' | translate }}</option>
            <option>{{ 'option.islam' | translate }}</option>
            <option>{{ 'option.sikhism' | translate }}</option>
            <option>{{ 'option.buddhism' | translate }}</option>
            <option>{{ 'option.other' | translate }}</option>
          </select>
          <div class="text-danger" *ngIf="employeeForm.get('religion')?.invalid && employeeForm.get('religion')?.touched">
            <small *ngIf="employeeForm.get('religion')?.errors?.['required']">Religion is required</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'Community' | translate }}<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="community" required>
            <option value="" disabled selected>{{ 'placeholder.selectCommunity' | translate }}</option>
            <option *ngFor="let comm of communities" [value]="comm">{{ comm }}</option>
          </select>
          <div class="text-danger" *ngIf="employeeForm.get('community')?.invalid && employeeForm.get('community')?.touched">
            <small *ngIf="employeeForm.get('community')?.errors?.['required']">Community is required</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="required-label">{{ 'Caste' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="caste" required>
          <div class="text-danger" *ngIf="employeeForm.get('caste')?.invalid && employeeForm.get('caste')?.touched">
            <small *ngIf="employeeForm.get('caste')?.errors?.['required']">Caste is required</small>
          </div>
        </div>
      </div>

      <!-- Service and Location Information Section -->
      <div class="row mt-4">
        <div class="col-md-12">
          <h5 class="text-primary mb-3">
            <i class="bi bi-briefcase me-2"></i>Service & Location Information
          </h5>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3">
          <label class="required-label">Current Designation<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="currentDesignation" required>
          <div class="text-danger" *ngIf="employeeForm.get('currentDesignation')?.invalid && employeeForm.get('currentDesignation')?.touched">
            <small *ngIf="employeeForm.get('currentDesignation')?.errors?.['required']">Current designation is required</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'Date Of Entry' | translate }}<span class="text-danger">*</span></label>
          <input type="date" class="form-control" formControlName="dateOfEntry" required>
          <div class="text-danger" *ngIf="employeeForm.get('dateOfEntry')?.invalid && employeeForm.get('dateOfEntry')?.touched">
            <small *ngIf="employeeForm.get('dateOfEntry')?.errors?.['required']">Date of entry is required</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'Section' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="section" required placeholder="Enter section">
          <div class="text-danger" *ngIf="employeeForm.get('section')?.invalid && employeeForm.get('section')?.touched">
            <small *ngIf="employeeForm.get('section')?.errors?.['required']">Section is required</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'District' | translate }}<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="district" required>
            <option value="" disabled>{{ 'option.selectDistrict' | translate }}</option>
            <option *ngFor="let district of districts" [value]="district">{{ district }}</option>
          </select>
          <div class="text-danger" *ngIf="employeeForm.get('district')?.invalid && employeeForm.get('district')?.touched">
            <small *ngIf="employeeForm.get('district')?.errors?.['required']">District is required</small>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-6">
          <label class="required-label">{{ 'Native Place & Taluk' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="nativePlaceAndTaluk" required placeholder="Enter native place and taluk">
          <div class="text-danger" *ngIf="employeeForm.get('nativePlaceAndTaluk')?.invalid && employeeForm.get('nativePlaceAndTaluk')?.touched">
            <small *ngIf="employeeForm.get('nativePlaceAndTaluk')?.errors?.['required']">Native place and taluk is required</small>
          </div>
        </div>

        <div class="col-md-6">
          <label class="required-label">Register Number<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="registerNumber" required>
          <div class="text-danger" *ngIf="employeeForm.get('registerNumber')?.invalid && employeeForm.get('registerNumber')?.touched">
            <small *ngIf="employeeForm.get('registerNumber')?.errors?.['required']">Register number is required</small>
            <small *ngIf="employeeForm.get('registerNumber')?.errors?.['invalidRegisterNumber']">Register number should be 6-10 characters</small>
          </div>
        </div>


      </div>
      <div class="row mt-3">
      <div class="col-md-6">
        <label class="required-label">{{ 'field.employeeType' | translate }}<span class="text-danger">*</span></label>
        <select class="form-control" formControlName="mainEmployeeType" required (change)="onMainEmployeeTypeChange($event)">
          <option value="" disabled selected>{{ 'option.selectEmployeeType' | translate }}</option>
          <option value="Permanent">{{ 'option.permanent' | translate }}</option>
          <option value="Seasonal">{{ 'option.seasonal' | translate }}</option>
          <option value="Load Man">{{ 'option.loadMan' | translate }}</option>
          <option value="Supernumeric">Supernumeric</option>
          <option value="Others">Others</option>
        </select>
      </div>

      <div class="col-md-6" *ngIf="employeeForm.get('mainEmployeeType')?.value === 'Seasonal'">

          <label class="required-label">Seasonal Category<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="seasonalCategory">
            <option value="" disabled selected>Select Seasonal Category</option>
            <option value="Helpers">Helpers</option>
            <option value="Bill Clerk">Bill Clerk</option>
            <option value="Watchman">Watchman</option>
          </select>

      </div>

      <div class="col-md-6" *ngIf="employeeForm.get('mainEmployeeType')?.value === 'Load Man'">

          <label class="required-label">Load Man Category<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="loadManCategory">
            <option value="" disabled selected>Select Load Man Category</option>
            <option value="CardLess">CardLess</option>
            <option value="Green">Green Card</option>
            <option value="Pink">Pink Card</option>
          </select>
        </div>
      </div>

      <!-- Supernumeric Fields -->
      <div class="row mt-3" *ngIf="employeeForm.get('mainEmployeeType')?.value === 'Supernumeric'">
        <div class="col-md-6">
          <label class="required-label">Date of Joining<span class="text-danger">*</span></label>
          <input type="date" class="form-control" formControlName="supernumericDateOfJoining" required>
          <div class="text-danger" *ngIf="employeeForm.get('supernumericDateOfJoining')?.invalid && employeeForm.get('supernumericDateOfJoining')?.touched">
            <small *ngIf="employeeForm.get('supernumericDateOfJoining')?.errors?.['required']">Date of joining is required</small>
          </div>
        </div>
        <div class="col-md-6">
          <label class="required-label">Remarks (Case Details)<span class="text-danger">*</span></label>
          <textarea class="form-control" formControlName="supernumericRemarks" rows="3" placeholder="Enter case details" required></textarea>
          <div class="text-danger" *ngIf="employeeForm.get('supernumericRemarks')?.invalid && employeeForm.get('supernumericRemarks')?.touched">
            <small *ngIf="employeeForm.get('supernumericRemarks')?.errors?.['required']">Remarks are required</small>
          </div>
        </div>
      </div>

      <!-- Others Fields -->
      <div class="row mt-3" *ngIf="employeeForm.get('mainEmployeeType')?.value === 'Others'">
        <div class="col-md-12">
          <label class="required-label">Remarks<span class="text-danger">*</span></label>
          <textarea class="form-control" formControlName="othersRemarks" rows="3" placeholder="Enter remarks" required></textarea>
          <div class="text-danger" *ngIf="employeeForm.get('othersRemarks')?.invalid && employeeForm.get('othersRemarks')?.touched">
            <small *ngIf="employeeForm.get('othersRemarks')?.errors?.['required']">Remarks are required</small>
          </div>
        </div>
      </div>


      <!-- Address Information Section -->
      <div class="row mt-4">
        <div class="col-md-12">
          <h5 class="text-primary mb-3">{{ 'AddressInfo' | translate }}</h5>
        </div>
      </div>
      <div class="row">
        <!-- Present Address Section -->
        <div class="col-md-6">
          <h6 class="text-primary mb-2">Present Address</h6>
          <!-- Add invisible placeholder to match permanent address checkbox height -->
          <div class="mb-2" style="height: 24px;"></div>
          <div class="row">
            <div class="col-md-6">
              <label class="required-label">Door No.<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="presentDoorNo" required>
              <div class="text-danger" *ngIf="employeeForm.get('presentDoorNo')?.invalid && employeeForm.get('presentDoorNo')?.touched">
                <small *ngIf="employeeForm.get('presentDoorNo')?.errors?.['required']">Door No. is required</small>
              </div>
            </div>
            <div class="col-md-6">
              <label class="required-label">Building Name<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="presentBuildingName" required>
              <div class="text-danger" *ngIf="employeeForm.get('presentBuildingName')?.invalid && employeeForm.get('presentBuildingName')?.touched">
                <small *ngIf="employeeForm.get('presentBuildingName')?.errors?.['required']">Building name is required</small>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-12">
              <label class="required-label">Street Address<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="presentStreetAddress" required>
              <div class="text-danger" *ngIf="employeeForm.get('presentStreetAddress')?.invalid && employeeForm.get('presentStreetAddress')?.touched">
                <small *ngIf="employeeForm.get('presentStreetAddress')?.errors?.['required']">Street address is required</small>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <label class="required-label">City<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="presentCity" required>
              <div class="text-danger" *ngIf="employeeForm.get('presentCity')?.invalid && employeeForm.get('presentCity')?.touched">
                <small *ngIf="employeeForm.get('presentCity')?.errors?.['required']">City is required</small>
              </div>
            </div>
            <div class="col-md-6">
              <label class="required-label">Pincode<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="presentPincode" required pattern="[0-9]{6}">
              <div class="text-danger" *ngIf="employeeForm.get('presentPincode')?.invalid && employeeForm.get('presentPincode')?.touched">
                <small *ngIf="employeeForm.get('presentPincode')?.errors?.['required']">Pincode is required</small>
                <small *ngIf="employeeForm.get('presentPincode')?.errors?.['pattern']">Pincode must be 6 digits</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Permanent Address Section -->
        <div class="col-md-6">
          <h6 class="text-primary mb-2">Permanent Address</h6>
          <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" formControlName="sameAsPresentAddress"
                   (change)="onSameAsPresentAddressChange($event)" id="sameAsPresentAddress">
            <label class="form-check-label" for="sameAsPresentAddress">
              Same as Present Address
            </label>
          </div>
          <div class="row">
            <div class="col-md-6">
              <label class="required-label">Door No.<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="permanentDoorNo" required>
              <div class="text-danger" *ngIf="employeeForm.get('permanentDoorNo')?.invalid && employeeForm.get('permanentDoorNo')?.touched">
                <small *ngIf="employeeForm.get('permanentDoorNo')?.errors?.['required']">Door No. is required</small>
              </div>
            </div>
            <div class="col-md-6">
              <label class="required-label">Building Name<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="permanentBuildingName" required>
              <div class="text-danger" *ngIf="employeeForm.get('permanentBuildingName')?.invalid && employeeForm.get('permanentBuildingName')?.touched">
                <small *ngIf="employeeForm.get('permanentBuildingName')?.errors?.['required']">Building name is required</small>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-12">
              <label class="required-label">Street Address<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="permanentStreetAddress" required>
              <div class="text-danger" *ngIf="employeeForm.get('permanentStreetAddress')?.invalid && employeeForm.get('permanentStreetAddress')?.touched">
                <small *ngIf="employeeForm.get('permanentStreetAddress')?.errors?.['required']">Street address is required</small>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <label class="required-label">City<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="permanentCity" required>
              <div class="text-danger" *ngIf="employeeForm.get('permanentCity')?.invalid && employeeForm.get('permanentCity')?.touched">
                <small *ngIf="employeeForm.get('permanentCity')?.errors?.['required']">City is required</small>
              </div>
            </div>
            <div class="col-md-6">
              <label class="required-label">Pincode<span class="text-danger">*</span></label>
              <input type="text" class="form-control" formControlName="permanentPincode" required pattern="[0-9]{6}">
              <div class="text-danger" *ngIf="employeeForm.get('permanentPincode')?.invalid && employeeForm.get('permanentPincode')?.touched">
                <small *ngIf="employeeForm.get('permanentPincode')?.errors?.['required']">Pincode is required</small>
                <small *ngIf="employeeForm.get('permanentPincode')?.errors?.['pattern']">Pincode must be 6 digits</small>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class="row">
        <div class="col-md-6">
          <h5 class="text-primary mb-3">Photo Upload<span class="text-danger">*</span></h5>
          <label class="required-label">Profile Picture<span class="text-danger">*</span></label>

          <!-- Show file input only if no photo is loaded -->
          <div *ngIf="!profilePhotoPreview">
            <input type="file" class="form-control" (change)="onProfilePhotoSelected($event)" accept=".png,.jpg,.jpeg" required>
            <small class="text-muted">Only PNG, JPG, JPEG files allowed (Max: 5MB)</small>
          </div>

          <!-- Show "Image Selected" when photo is loaded -->
          <div *ngIf="profilePhotoPreview" class="mb-2">
            <div class="alert alert-success d-flex align-items-center py-2">
              <i class="bi bi-check-circle-fill me-2"></i>
              <span class="me-auto">Image Selected</span>
              <button type="button" class="btn btn-sm btn-outline-primary" (click)="changeProfilePhoto()">
                <i class="bi bi-pencil me-1"></i>Change
              </button>
            </div>
          </div>

          <!-- Hidden file input for changing photo -->
          <input type="file"
                 class="d-none"
                 #profilePhotoInput
                 (change)="onProfilePhotoSelected($event)"
                 accept=".png,.jpg,.jpeg"
                 id="profilePhotoInput">

          <div class="text-danger" *ngIf="profilePhotoError">
            <small>{{profilePhotoError}}</small>
          </div>

          <!-- Profile Photo Preview -->
          <div class="mt-2" *ngIf="profilePhotoPreview">
            <div class="position-relative">
              <img [src]="profilePhotoPreview"
                   alt="Profile Preview"
                   style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; object-fit: cover;">
              <div class="position-absolute top-0 end-0 bg-success text-white rounded-circle" style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; margin: 2px;">
                <i class="bi bi-check"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="col-md-6">
          <label class="required-label">{{ 'field.signature' | translate }}<span class="text-danger">*</span></label>
          <input type="file" class="form-control" (change)="onSignatureSelected($event)" accept=".png,.jpg,.jpeg" required>
          <small class="text-muted">Only PNG, JPG, JPEG files allowed (Max: 5MB)</small>
          <div class="text-danger" *ngIf="signatureError">
            <small>{{signatureError}}</small>
          </div>
          <div class="mt-2" *ngIf="signaturePreview">
            <img [src]="signaturePreview" alt="Signature Preview" style="max-width: 150px; max-height: 100px; border: 1px solid #ddd; border-radius: 4px;">
          </div>
        </div> -->
      </div>
    </div>
  </div>



  <!-- Document Upload Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.documentUpload' | translate }}</h2>

      <div class="row">
        <div class="col-md-6">
          <label class="required-label">{{ 'field.documents' | translate }}<span class="text-danger">*</span></label>
          <div class="position-relative">
            <input type="file"
                   class="form-control"
                   [class.file-selected]="selectedDocuments && selectedDocuments.length > 0"
                   (change)="onDocumentSelected($event)"
                   accept=".pdf"
                   multiple
                   required>
            <!-- Document Upload Status Indicator Inside Field -->
            <div class="position-absolute top-50 start-0 translate-middle-y ms-3" *ngIf="selectedDocuments && selectedDocuments.length > 0"
                 style="pointer-events: none; font-size: 14px; color: #0d6efd; background: white; padding: 0 4px;">
              {{ selectedDocuments.length }} file{{ selectedDocuments.length > 1 ? 's' : '' }} uploaded - click to add files
            </div>
          </div>
          <small class="text-muted">Only PDF files allowed (Max: 10MB per file)</small>
          <div class="text-danger" *ngIf="documentError">
            <small>{{documentError}}</small>
          </div>

          <!-- Document List -->
          <div class="mt-2" *ngIf="selectedDocuments && selectedDocuments.length > 0">
            <div class="uploaded-files">
              <h6 class="text-success">Selected Files:</h6>
              <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center" *ngFor="let doc of selectedDocuments; let i = index">
                  <div class="d-flex align-items-center flex-grow-1">
                    <i class="bi bi-file-earmark-pdf me-2 text-danger"></i>
                    <span class="file-name-clickable"
                          (click)="openPDFInNewTab(doc)"
                          [title]="'Click to view PDF'"
                          style="cursor: pointer; color: #0066cc; text-decoration: underline;">
                      {{ doc.name }}
                    </span>
                    <small class="text-muted ms-2">({{ formatFileSize(doc.size) }})</small>
                  </div>
                  <button type="button" class="btn btn-sm btn-outline-danger" (click)="removeDocument(i)" title="Remove">
                    <i class="bi bi-trash"></i>
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="upload-instructions">
            <h6 class="text-info">Document Types</h6>
            <div class="alert alert-light border">
              <h6 class="text-primary mb-3"><i class="bi bi-file-earmark-pdf me-2"></i>Required Documents</h6>
              <ul class="text-muted mb-0">
                <li><strong>SR Book PDF</strong> - Service Record Book</li>
                <li>Educational Certificates</li>
                <li>Identity Documents</li>
                <li>Service Orders & Proceedings</li>
                <li>Other relevant official documents</li>
              </ul>
            </div>
            <div class="mt-3">
              <small class="text-muted">
                <i class="bi bi-info-circle me-1"></i>
                Ensure all documents are in PDF format and clearly readable
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Account Details Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.accountDetails' | translate }}</h2>
      <div class="row">
        <div class="col-md-3">
          <label class="required-label">{{ 'field.bankAccountNo' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="bankAccountNumber" required>
          <div class="text-danger" *ngIf="employeeForm.get('bankAccountNumber')?.invalid && employeeForm.get('bankAccountNumber')?.touched">
            <small *ngIf="employeeForm.get('bankAccountNumber')?.errors?.['required']">Bank account number is required</small>
            <small *ngIf="employeeForm.get('bankAccountNumber')?.errors?.['invalidBankAccount']">Bank account should be 9-18 digits</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'field.ifscCode' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="ifscCode" required>
          <div class="text-danger" *ngIf="employeeForm.get('ifscCode')?.invalid && employeeForm.get('ifscCode')?.touched">
            <small *ngIf="employeeForm.get('ifscCode')?.errors?.['required']">IFSC code is required</small>
            <small *ngIf="employeeForm.get('ifscCode')?.errors?.['invalidIfsc']">Invalid IFSC format (e.g., ABCD0123456)</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'field.bankName' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="bankName" required>
          <div class="text-danger" *ngIf="employeeForm.get('bankName')?.invalid && employeeForm.get('bankName')?.touched">
            <small *ngIf="employeeForm.get('bankName')?.errors?.['required']">Bank name is required</small>
          </div>
        </div>

      </div>
      <div class="row mt-3">
        <div class="col-md-3">
          <label class="required-label">{{ 'field.panNumber' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="panNumber" required>
          <div class="text-danger" *ngIf="employeeForm.get('panNumber')?.invalid && employeeForm.get('panNumber')?.touched">
            <small *ngIf="employeeForm.get('panNumber')?.errors?.['required']">PAN number is required</small>
            <small *ngIf="employeeForm.get('panNumber')?.errors?.['invalidPan']">Invalid PAN format (e.g., **********)</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'field.uanNumber' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="uanNumber" required>
          <div class="text-danger" *ngIf="employeeForm.get('uanNumber')?.invalid && employeeForm.get('uanNumber')?.touched">
            <small *ngIf="employeeForm.get('uanNumber')?.errors?.['required']">UAN number is required</small>
            <small *ngIf="employeeForm.get('uanNumber')?.errors?.['invalidUan']">UAN should be 12 digits</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">{{ 'field.aadharNumber' | translate }}<span class="text-danger">*</span></label>
          <input type="text" class="form-control" formControlName="aadharNumber" required>
          <div class="text-danger" *ngIf="employeeForm.get('aadharNumber')?.invalid && employeeForm.get('aadharNumber')?.touched">
            <small *ngIf="employeeForm.get('aadharNumber')?.errors?.['required']">Aadhar number is required</small>
            <small *ngIf="employeeForm.get('aadharNumber')?.errors?.['invalidAadhar']">Aadhar should be 12 digits</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Education & Technical Qualification Details Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.educationQualification' | translate }}</h2>
      <div formArrayName="educationEntries">
        <div *ngFor="let entry of educationEntries.controls; let i = index" [formGroupName]="i" class="row education-entry mt-2">
          <div class="col-md-2">
            <label class="required-label">{{ 'field.qualification' | translate }}<span class="text-danger">*</span></label>
            <select class="form-control" formControlName="qualification" required (change)="onEducationQualificationChange(i, $event)">
              <option value="" disabled>Select Qualification</option>
              <option *ngFor="let qual of educationQualifications" [value]="qual">{{ qual }}</option>
            </select>
            <div class="text-danger" *ngIf="entry.get('qualification')?.invalid && entry.get('qualification')?.touched">
              <small *ngIf="entry.get('qualification')?.errors?.['required']">Qualification is required</small>
            </div>
          </div>

          <!-- School Name - Only for non-highest degree -->
          <div class="col-md-3" *ngIf="!isHighestDegree(i)">
            <label class="required-label">{{ 'field.schoolName' | translate }}<span class="text-danger">*</span></label>
            <input type="text" class="form-control" formControlName="schoolname" required>
            <div class="text-danger" *ngIf="entry.get('schoolname')?.invalid && entry.get('schoolname')?.touched">
              <small *ngIf="entry.get('schoolname')?.errors?.['required']">School name is required</small>
            </div>
          </div>

          <!-- Course Name - Only for non-highest degree and optional -->
          <div class="col-md-2" *ngIf="!isHighestDegree(i)">
            <label class="required-label">Course Name/Department Name</label>
            <input type="text" class="form-control" formControlName="coursename" placeholder="Enter course or department name">
          </div>

          <!-- University Name - Only for Highest Degree -->
          <div class="col-md-4" *ngIf="isHighestDegree(i)">
            <label class="required-label">University Name<span class="text-danger">*</span></label>
            <input type="text" class="form-control" formControlName="universityname" required>
            <div class="text-danger" *ngIf="entry.get('universityname')?.invalid && entry.get('universityname')?.touched">
              <small *ngIf="entry.get('universityname')?.errors?.['required']">University name is required</small>
            </div>
          </div>

          <!-- Specialization - Only for Highest Degree -->
          <div class="col-md-3" *ngIf="isHighestDegree(i)">
            <label class="required-label">Specialization<span class="text-danger">*</span></label>
            <input type="text" class="form-control" formControlName="specialization" required>
            <div class="text-danger" *ngIf="entry.get('specialization')?.invalid && entry.get('specialization')?.touched">
              <small *ngIf="entry.get('specialization')?.errors?.['required']">Specialization is required</small>
            </div>
          </div>



          <div class="col-md-1 d-flex align-items-end">
            <button type="button" class="btn btn-danger btn-sm me-1" (click)="removeEducationEntry(i)" *ngIf="educationEntries.length > 1">-</button>
            <button type="button" class="btn btn-success btn-sm" (click)="addEducationEntry()">+</button>
          </div>
        </div>
      </div>

      <!-- Technical Qualification Section -->
      <div class="mt-4">
        <h4 class="text-primary mb-3">
          Technical Qualification
        </h4>
        <div formArrayName="technicalQualificationEntries">
          <div *ngFor="let entry of technicalQualificationEntries.controls; let i = index" [formGroupName]="i" class="row technical-qualification-entry mt-2">
            <div class="col-md-3">
              <label class="required-label">Technical Type<span class="text-danger">*</span></label>
              <select class="form-control" formControlName="technicalType" required>
                <option value="" disabled>Select Technical Type</option>
                <option value="Type Writer">Type Writer</option>
                <option value="Stenography">Stenography</option>
                <option value="Short Hand">Short Hand</option>
                <option value="Others">Others</option>
              </select>
              <div class="text-danger" *ngIf="entry.get('technicalType')?.invalid && entry.get('technicalType')?.touched">
                <small *ngIf="entry.get('technicalType')?.errors?.['required']">Technical type is required</small>
              </div>
            </div>

            <div class="col-md-4">
              <label class="required-label">Remarks</label>
              <textarea class="form-control" formControlName="remarks" rows="2" placeholder="Enter remarks"></textarea>
            </div>

            <div class="col-md-3">
              <label class="required-label">Grade<span class="text-danger">*</span></label>
              <select class="form-control" formControlName="grade" required>
                <option value="" disabled>Select Grade</option>
                <option value="High">High</option>
                <option value="Medium">Medium</option>
                <option value="Low">Low</option>
              </select>
              <div class="text-danger" *ngIf="entry.get('grade')?.invalid && entry.get('grade')?.touched">
                <small *ngIf="entry.get('grade')?.errors?.['required']">Grade is required</small>
              </div>
            </div>

            <div class="col-md-2 d-flex align-items-end">
              <button type="button" class="btn btn-danger btn-sm me-1" (click)="removeTechnicalQualificationEntry(i)" *ngIf="technicalQualificationEntries.length > 1">-</button>
              <button type="button" class="btn btn-success btn-sm" (click)="addTechnicalQualificationEntry()">+</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Service History Track Card - Shows only for Permanent employees -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>
        <i class="bi bi-clock-history me-2"></i>Service {{ defaultLabel }}
      </h2>
      <div class="alert alert-info" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        <strong>Note:</strong> Add service track entries in chronological order. Each entry represents a significant event in the employee's career.
      </div>

      <div formArrayName="serviceEntries">
        <div *ngFor="let entry of serviceEntries.controls; let i = index" [formGroupName]="i" class="service-entry-container mb-4">
          <div class="card border-left-primary">
            <div class="card-header bg-light">
              <div class="row align-items-center">
                <div class="col-md-8">
                  <h6 class="mb-0">
                    <i class="bi bi-calendar-event me-2"></i>Service Entry #{{ i + 1 }}
                  </h6>
                </div>
                <div class="col-md-4 text-end">
                  <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-danger btn-sm" (click)="removeServiceEntry(i)" *ngIf="serviceEntries.length > 1">
                      <i class="bi bi-trash"></i> Remove
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" (click)="addServiceEntry()">
                      <i class="bi bi-plus"></i> Add New
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <label class="required-label">
                    <i class="bi bi-tag me-1"></i>Service Type<span class="text-danger">*</span>
                  </label>
                  <select class="form-control" formControlName="type" required (change)="onTypeChange(i)">
                    <option value="" disabled>Select Service Type</option>
                    <option *ngFor="let serviceType of getServiceTypesForEmployee()" [value]="serviceType">
                      <span *ngIf="serviceType === 'Appointment'">📋</span>
                      <span *ngIf="serviceType === 'Promotion'">📈</span>
                      <span *ngIf="serviceType === 'Increment'">💰</span>
                      <span *ngIf="serviceType === 'Transfer'">🔄</span>
                      <span *ngIf="serviceType === 'Deputation'">🏢</span>
                      <span *ngIf="serviceType === 'Punishment'">⚠️</span>
                      <span *ngIf="serviceType === 'Retirement'">🎯</span>
                      <span *ngIf="serviceType === 'Probation'">⏳</span>
                      {{ serviceType }}
                    </option>
                  </select>
                  <div class="text-danger" *ngIf="entry.get('type')?.invalid && entry.get('type')?.touched">
                    <small *ngIf="entry.get('type')?.errors?.['required']">Service type is required</small>
                  </div>
                </div>
                <div class="col-md-9">
                  <div class="alert alert-secondary mb-0" *ngIf="!entry.get('type')?.value">
                    <i class="bi bi-arrow-up me-2"></i>
                    <small>Please select a service type above to see relevant fields</small>
                  </div>
                </div>
              </div>

              <!-- Appointment Fields -->
              <div class="mt-3" *ngIf="entry.get('type')?.value === 'Appointment'">
                <div class="alert alert-primary" role="alert">
                  <i class="bi bi-briefcase me-2"></i>
                  <strong>Appointment Details</strong> - Fill in the appointment information
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-person-badge me-1"></i>Appointment Type<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="appointmentType" (change)="onAppointmentTypeChange($event, i)">
                      <option value="" disabled>Select Appointment Type</option>
                      <option>Direct Recruitment</option>
                      <option>Promotion</option>
                      <option>Transfer</option>
                      <option>Deputation</option>
                      <option>Contract</option>
                      <option>Foreign Service</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-gear me-1"></i>Mode of Appointment<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="modeOfAppointment">
                      <option value="" disabled>Select Mode of Appointment</option>
                      <option>Direct Recruitment</option>
                      <option>Compassionate Ground</option>
                      <option>under 12(3) of Industrial Dispute Act</option>
                      <option>Transfer from other Category</option>
                      <option>Court direction</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-calendar me-1"></i>Date of Appointment<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="dateOfAppointment">
                  </div>
                </div>
                <div class="row mt-2">
                  <div class="col-md-6">
                    <label class="required-label">
                      <i class="bi bi-file-text me-1"></i>Proceeding Order No.<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="proceedingOrderNo" placeholder="Enter proceeding order number">
                  </div>
                  <div class="col-md-6">
                    <label class="required-label">
                      <i class="bi bi-calendar-check me-1"></i>Proceeding Order Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="proceedingOrderDate">
                  </div>
                </div>

                <!-- Foreign Service Fields - All Required Fields -->
                <div class="row mt-3" *ngIf="entry.get('appointmentType')?.value === 'Foreign Service'">
                  <div class="col-md-12">
                    <div class="alert alert-primary" role="alert">
                      <i class="bi bi-globe me-2"></i>
                      <strong>Foreign Service Details</strong> - Fill in all foreign service information
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="required-label">
                      <i class="bi bi-diagram-3 me-1"></i>From Department<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="fromDepartment" placeholder="Enter from department" required>
                    <div class="text-danger" *ngIf="entry.get('fromDepartment')?.invalid && entry.get('fromDepartment')?.touched">
                      <small *ngIf="entry.get('fromDepartment')?.errors?.['required']">From department is required</small>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="required-label">
                      <i class="bi bi-diagram-3-fill me-1"></i>To Department<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="toDepartment" placeholder="Enter to department" required>
                    <div class="text-danger" *ngIf="entry.get('toDepartment')?.invalid && entry.get('toDepartment')?.touched">
                      <small *ngIf="entry.get('toDepartment')?.errors?.['required']">To department is required</small>
                    </div>
                  </div>
                </div>
                <div class="row mt-2" *ngIf="entry.get('appointmentType')?.value === 'Foreign Service'">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-building me-1"></i>From Organization<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="fromOrganization" placeholder="Enter from organization" required>
                    <div class="text-danger" *ngIf="entry.get('fromOrganization')?.invalid && entry.get('fromOrganization')?.touched">
                      <small *ngIf="entry.get('fromOrganization')?.errors?.['required']">From organization is required</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-person-badge me-1"></i>From Designation<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="fromDesignationForeign" placeholder="Enter from designation" required>
                    <div class="text-danger" *ngIf="entry.get('fromDesignationForeign')?.invalid && entry.get('fromDesignationForeign')?.touched">
                      <small *ngIf="entry.get('fromDesignationForeign')?.errors?.['required']">From designation is required</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-person-check me-1"></i>To Designation<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="toDesignationForeign" placeholder="Enter to designation" required>
                    <div class="text-danger" *ngIf="entry.get('toDesignationForeign')?.invalid && entry.get('toDesignationForeign')?.touched">
                      <small *ngIf="entry.get('toDesignationForeign')?.errors?.['required']">To designation is required</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Promotion Fields -->
              <div class="mt-3" *ngIf="entry.get('type')?.value === 'Promotion'">
                <div class="alert alert-success" role="alert">
                  <i class="bi bi-arrow-up-circle me-2"></i>
                  <strong>Promotion Details</strong> - Record promotion information
                </div>
                <div class="row">
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-calendar-plus me-1"></i>Joining Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="joiningDate">
                  </div>
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-arrow-down me-1"></i>From Designation<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="fromDesignation">
                      <option value="" disabled>Select Current Designation</option>
                      <option>Junior Assistant</option>
                      <option>Assistant</option>
                      <option>Senior Assistant</option>
                      <option>Manager</option>
                      <option>Senior Manager</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-arrow-up me-1"></i>To Promoted<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="toPromoted">
                      <option value="" disabled>Select Promoted Designation</option>
                      <option>Assistant Manager</option>
                      <option>Manager</option>
                      <option>Senior Manager</option>
                      <option>Deputy General Manager</option>
                      <option>General Manager</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-calendar-event me-1"></i>Promoted Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="promotedDate">
                  </div>
                </div>
              </div>

              <!-- Transfer Fields -->
              <div class="mt-3" *ngIf="entry.get('type')?.value === 'Transfer'">
                <div class="alert alert-info" role="alert">
                  <i class="bi bi-arrow-left-right me-2"></i>
                  <strong>Transfer Details</strong> - Record transfer information
                </div>
                <div class="row">
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-calendar-x me-1"></i>Transfer Order Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="fromDate">
                  </div>
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-calendar-check me-1"></i>Transfer Joining Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="toDate">
                  </div>
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-geo-alt me-1"></i>From Location<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="fromPlace" placeholder="Current workplace">
                  </div>
                  <div class="col-md-3">
                    <label class="required-label">
                      <i class="bi bi-geo-alt-fill me-1"></i>To Location<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="toPlace" placeholder="Transfer destination">
                  </div>
                </div>
              </div>

              <!-- Deputation Fields -->
              <div class="mt-3" *ngIf="entry.get('type')?.value === 'Deputation'">
                <div class="alert alert-info" role="alert">
                  <i class="bi bi-building me-2"></i>
                  <strong>Deputation Details</strong> - Record foreign service assignment
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-person-badge me-1"></i>Designation (Foreign Service)<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="designation">
                      <option value="" disabled>Select Foreign Service Designation</option>
                      <option value="Construction Engineer">Construction Engineer</option>
                      <option value="Divisional Accountant">Divisional Accountant</option>
                      <option value="Financial Advisor & Chief Accounts Officer">Financial Advisor & Chief Accounts Officer</option>
                      <option value="General Manager">General Manager</option>
                      <option value="Manager (Accounts)">Manager (Accounts)</option>
                      <option value="Manager (Distribution)">Manager (Distribution)</option>
                      <option value="Regional Managers">Regional Managers</option>
                      <option value="Senior Manager (Accounts)">Senior Manager (Accounts)</option>
                      <option value="Senior Manager (Audit)">Senior Manager (Audit)</option>
                      <option value="Senior Regional Managers">Senior Regional Managers</option>
                      <option value="Vigilance Office">Vigilance Office</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-person-circle me-1"></i>Original Designation<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="originalDesignation">
                      <option value="" disabled>Select Original Designation</option>
                      <option value="Additional Registrar">Additional Registrar</option>
                      <option value="Class III Officer">Class III Officer</option>
                      <option value="Chief Accounts Officer">Chief Accounts Officer</option>
                      <option value="Chief Engineer">Chief Engineer</option>
                      <option value="Deputy Director">Deputy Director</option>
                      <option value="Deputy Collector">Deputy Collector</option>
                      <option value="Deputy Commercial">Deputy Commercial</option>
                      <option value="District Revenue Officer">District Revenue Officer</option>
                      <option value="Inspector">Inspector</option>
                      <option value="Joint Registrar">Joint Registrar</option>
                      <option value="Joint Secretary">Joint Secretary</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-building-gear me-1"></i>Parent Department<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="parentDepartment">
                      <option value="" disabled>Select Parent Department</option>
                      <option value="Finance Department">Finance Department</option>
                      <option value="IRTS">IRTS</option>
                      <option value="Local Fund Audit Department">Local Fund Audit Department</option>
                      <option value="Public Works Department">Public Works Department</option>
                      <option value="Revenue / Co-operative Department">Revenue / Co-operative Department</option>
                      <option value="Treasury and Account Department">Treasury and Account Department</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Increment Fields -->
              <div class="mt-3" *ngIf="entry.get('type')?.value === 'Increment'">
                <div class="alert alert-success" role="alert">
                  <i class="bi bi-cash-coin me-2"></i>
                  <strong>Increment Details</strong> - Record salary increment information
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-calendar-plus me-1"></i>From Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="fromDate">
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-calendar-check me-1"></i>To Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="toDate">
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-cash me-1"></i>Type of Increment<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="incrementType" placeholder="Enter increment type (e.g., Annual Increment, Special Increment, Merit Increment)">
                  </div>
                </div>

                <!-- Additional fields for Seasonal employees -->
                <div class="row mt-3" *ngIf="employeeForm.get('mainEmployeeType')?.value === 'Seasonal'">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-currency-rupee me-1"></i>Basic Pay<span class="text-danger">*</span>
                    </label>
                    <input type="number" class="form-control" formControlName="basicPay" placeholder="Enter basic pay amount" min="0" step="0.01">
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-currency-rupee me-1"></i>DA<span class="text-danger">*</span>
                    </label>
                    <input type="number" class="form-control" formControlName="daField" placeholder="Enter DA amount" min="0" step="0.01">
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-currency-rupee me-1"></i>Basic + DA<span class="text-danger">*</span>
                    </label>
                    <input type="number" class="form-control" formControlName="basicPlusDA" placeholder="Enter Basic + DA amount" min="0" step="0.01">
                  </div>
                </div>
              </div>

          <!-- Loan Recoveries Fields -->
          <div class="col-md-10" *ngIf="entry.get('type')?.value === 'Loan Recoveries'">
            <div class="row">
              <div class="col-md-4">
                <label class="required-label">From Date<span class="text-danger">*</span></label>
                <input type="date" class="form-control" formControlName="fromDate">
              </div>
              <div class="col-md-4">
                <label class="required-label">To Date<span class="text-danger">*</span></label>
                <input type="date" class="form-control" formControlName="toDate">
              </div>
              <div class="col-md-4">
                <label class="required-label">Type of Loan Recovery<span class="text-danger">*</span></label>
                <input type="text" class="form-control" formControlName="loanRecoveryType">
              </div>
            </div>
          </div>

          <!-- Retirement Fields -->
          <div class="col-md-10" *ngIf="entry.get('type')?.value === 'Retirement'">
            <div class="row">
              <div class="col-md-6">
                <label class="required-label">From Date<span class="text-danger">*</span></label>
                <input type="date" class="form-control" formControlName="fromDate">
              </div>
              <div class="col-md-6">
                <label class="required-label">To Date<span class="text-danger">*</span></label>
                <input type="date" class="form-control" formControlName="toDate">
              </div>
            </div>
          </div>

          <!-- Probation Fields -->
          <div class="col-md-10" *ngIf="entry.get('type')?.value === 'Probation'">
            <div class="alert alert-info" role="alert">
              <i class="bi bi-hourglass-split me-2"></i>
              <strong>Probation Details</strong> - Record probation period information
            </div>
            <div class="row">
              <div class="col-md-6">
                <label class="required-label">
                  <i class="bi bi-calendar-plus me-1"></i>Date of Joining<span class="text-danger">*</span>
                </label>
                <input type="date" class="form-control" formControlName="probationDateOfJoining" required>
                <div class="text-danger" *ngIf="entry.get('probationDateOfJoining')?.invalid && entry.get('probationDateOfJoining')?.touched">
                  <small *ngIf="entry.get('probationDateOfJoining')?.errors?.['required']">Date of joining is required</small>
                </div>
              </div>
              <div class="col-md-6">
                <label class="required-label">
                  <i class="bi bi-calendar-x me-1"></i>End Date<span class="text-danger">*</span>
                </label>
                <input type="date" class="form-control" formControlName="probationEndDate" required>
                <div class="text-danger" *ngIf="entry.get('probationEndDate')?.invalid && entry.get('probationEndDate')?.touched">
                  <small *ngIf="entry.get('probationEndDate')?.errors?.['required']">End date is required</small>
                </div>
              </div>
            </div>
          </div>

              <!-- Punishment Fields -->
              <div class="mt-3" *ngIf="entry.get('type')?.value === 'Punishment'">
                <div class="alert alert-warning" role="alert">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  <strong>Punishment Details</strong> - Record disciplinary action information
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-shield-exclamation me-1"></i>Punishment Type<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="punishmentType" required>
                      <option value="" disabled>Select Punishment Type</option>
                      <option *ngFor="let punishmentType of getPunishmentTypesForEmployee()" [value]="punishmentType">
                        {{ punishmentType }}
                      </option>
                    </select>
                    <div class="text-danger" *ngIf="entry.get('punishmentType')?.invalid && entry.get('punishmentType')?.touched">
                      <small *ngIf="entry.get('punishmentType')?.errors?.['required']">Punishment type is required</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-calendar-x me-1"></i>Punishment Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="punishmentDate" required>
                    <div class="text-danger" *ngIf="entry.get('punishmentDate')?.invalid && entry.get('punishmentDate')?.touched">
                      <small *ngIf="entry.get('punishmentDate')?.errors?.['required']">Punishment date is required</small>
                    </div>
                  </div>
                </div>

                <!-- Additional fields for Withholding of Increment (Permanent employees only) -->
                <div class="row mt-3" *ngIf="employeeForm.get('mainEmployeeType')?.value === 'Permanent' && entry.get('punishmentType')?.value === 'Withholding of Increment'">
                  <div class="col-md-6">
                    <label class="required-label">
                      <i class="bi bi-calendar-plus me-1"></i>Withholding From Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="withholdingFromDate" required>
                    <div class="text-danger" *ngIf="entry.get('withholdingFromDate')?.invalid && entry.get('withholdingFromDate')?.touched">
                      <small *ngIf="entry.get('withholdingFromDate')?.errors?.['required']">From date is required</small>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="required-label">
                      <i class="bi bi-calendar-check me-1"></i>Withholding To Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="withholdingToDate" required>
                    <div class="text-danger" *ngIf="entry.get('withholdingToDate')?.invalid && entry.get('withholdingToDate')?.touched">
                      <small *ngIf="entry.get('withholdingToDate')?.errors?.['required']">To date is required</small>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <!-- Case Details Section - Only shown for Punishment service type -->
            <div class="card-body" *ngIf="entry.get('type')?.value === 'Punishment'">
                <div class="alert alert-warning" role="alert">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  <strong>Case Details</strong> - Required for punishment entries
                </div>
                <!-- Case Details Radio Button -->
                <div class="row mb-3">
                  <div class="col-md-12">
                    <label class="required-label">
                      <i class="bi bi-question-circle me-1"></i>Do you have case details?<span class="text-danger">*</span>
                    </label>
                    <div class="mt-2">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="hasCaseDetails" value="yes" id="caseDetailsYes{{i}}">
                        <label class="form-check-label" for="caseDetailsYes{{i}}">
                          <i class="bi bi-check-circle text-success me-1"></i>Yes
                        </label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" formControlName="hasCaseDetails" value="no" id="caseDetailsNo{{i}}">
                        <label class="form-check-label" for="caseDetailsNo{{i}}">
                          <i class="bi bi-x-circle text-danger me-1"></i>No
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Case Details Fields (shown only when "Yes" is selected) -->
                <div class="row" *ngIf="entry.get('hasCaseDetails')?.value === 'yes'">
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-file-text me-1"></i>Case Details<span class="text-danger">*</span>
                    </label>
                    <select class="form-control" formControlName="caseDetails" [required]="entry.get('hasCaseDetails')?.value === 'yes'" [disabled]="entry.get('hasCaseDetails')?.value === 'no'">
                      <option value="" disabled>Select Case Type</option>
                      <option value="(DV & AC) Cases">(DV & AC) Cases</option>
                      <option value="(CSCID) Cases">(CSCID) Cases</option>
                      <option value="(CBCID) Cases">(CBCID) Cases</option>
                      <option value="Criminal Cases">Criminal Cases</option>
                      <option value="Revenue Recovery / Civil Suit Cases">Revenue Recovery / Civil Suit Cases</option>
                      <option value="Private Case">Private Case</option>
                    </select>
                    <div class="text-danger" *ngIf="entry.get('caseDetails')?.invalid && entry.get('caseDetails')?.touched && entry.get('hasCaseDetails')?.value === 'yes'">
                      <small *ngIf="entry.get('caseDetails')?.errors?.['required']">Case details are required</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-file-text me-1"></i>Case Number<span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" formControlName="caseNumber" [required]="entry.get('hasCaseDetails')?.value === 'yes'" [placeholder]="'placeholder.enterCaseNumber' | translate" [disabled]="entry.get('hasCaseDetails')?.value === 'no'">
                    <div class="text-danger" *ngIf="entry.get('caseNumber')?.invalid && entry.get('caseNumber')?.touched && entry.get('hasCaseDetails')?.value === 'yes'">
                      <small *ngIf="entry.get('caseNumber')?.errors?.['required']">Case number is required</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="required-label">
                      <i class="bi bi-calendar-x me-1"></i>Case Date<span class="text-danger">*</span>
                    </label>
                    <input type="date" class="form-control" formControlName="caseDate" [required]="entry.get('hasCaseDetails')?.value === 'yes'" [disabled]="entry.get('hasCaseDetails')?.value === 'no'">
                    <div class="text-danger" *ngIf="entry.get('caseDate')?.invalid && entry.get('caseDate')?.touched && entry.get('hasCaseDetails')?.value === 'yes'">
                      <small *ngIf="entry.get('caseDate')?.errors?.['required']">Case date is required</small>
                    </div>
                  </div>


              </div>
              <div class="row mt-2" *ngIf="entry.get('hasCaseDetails')?.value === 'yes'">
                <div class="col-md-4 mt-2">
                  <label class="required-label">
                    <i class="bi bi-file-text me-1"></i>Description<span class="text-danger">*</span>
                  </label>
                  <input type="text" class="form-control" formControlName="description" [required]="entry.get('hasCaseDetails')?.value === 'yes'" [placeholder]="'placeholder.enterDescription' | translate" [disabled]="entry.get('hasCaseDetails')?.value === 'no'">
                  <div class="text-danger" *ngIf="entry.get('description')?.invalid && entry.get('description')?.touched && entry.get('hasCaseDetails')?.value === 'yes'">
                    <small *ngIf="entry.get('description')?.errors?.['required']">Description is required</small>
                  </div>
                </div>
                <div class="col-md-4 mt-2" style="position: relative; z-index: 1000;">
                  <label class="required-label">
                    <i class="bi bi-people me-1"></i>Involved Persons<span class="text-danger">*</span>
                  </label>

                  <div style="position: relative; z-index: 999999;">
                    <app-multi-select-dropdown
                      [options]="employeeOptions"
                      [selectedValues]="entry.get('involvedPersons')?.value || []"
                      label=""
                      placeholder="Select involved persons..."
                      (selectionChange)="onEmployeeSelectionChange($event, i)">
                    </app-multi-select-dropdown>
                  </div>

                  <!-- Display selected involved persons -->
                  <div class="mt-2" *ngIf="entry.get('involvedPersons')?.value?.length > 0">
                    <small class="text-muted">
                      <strong>Selected:</strong> {{ getInvolvedPersonNames(entry.get('involvedPersons')?.value) }}
                    </small>
                  </div>

                  <div class="text-danger" *ngIf="entry.get('hasCaseDetails')?.value === 'yes' && (!entry.get('involvedPersons')?.value || entry.get('involvedPersons')?.value?.length === 0) && entry.get('personInvolved')?.touched">
                    <small>At least one involved person must be selected</small>
                  </div>

                  <!-- Display selected employees as badges -->
                  <div class="mt-2" *ngIf="selectedEmployees.length > 0">
                    <div class="d-flex flex-wrap gap-1">
                      <span class="badge bg-primary" *ngFor="let employee of selectedEmployees">
                        {{ employee.name }}
                      </span>
                    </div>
                  </div>

                  <!-- Hidden input to maintain form validation -->
                  <input type="hidden" class="form-control" formControlName="personInvolved" [value]="getSelectedEmployeeNames()" [required]="entry.get('hasCaseDetails')?.value === 'yes'" [disabled]="entry.get('hasCaseDetails')?.value === 'no'">
                </div>
                <div class="col-md-4 mt-2">
                  <label class="required-label">
                    <i class="bi bi-file-text me-1"></i>Present Status<span class="text-danger">*</span>
                  </label>
                  <select class="form-control" formControlName="presentStatus" [required]="entry.get('hasCaseDetails')?.value === 'yes'" [disabled]="entry.get('hasCaseDetails')?.value === 'no'">
                    <option value="" disabled>Select Present Status</option>
                    <option value="Open">Open</option>
                    <option value="Closed">Closed</option>
                  </select>
                  <div class="text-danger" *ngIf="entry.get('presentStatus')?.invalid && entry.get('presentStatus')?.touched && entry.get('hasCaseDetails')?.value === 'yes'">
                    <small *ngIf="entry.get('presentStatus')?.errors?.['required']">Present status is required</small>
                  </div>
                </div>
              </div>

            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Training History Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.trainingHistory' | translate }}</h2>
      <div formArrayName="trainingEntries">
        <div *ngFor="let entry of trainingEntries.controls; let i = index" [formGroupName]="i" class="row training-entry mt-2">
          <div class="col-md-4">
            <label class="required-label">{{ 'field.trainingType' | translate }}<span class="text-danger">*</span></label>
            <input type="text" class="form-control" formControlName="trainingtype" required [placeholder]="'placeholder.enterTrainingType' | translate">
            <div class="text-danger" *ngIf="entry.get('trainingtype')?.invalid && entry.get('trainingtype')?.touched">
              <small *ngIf="entry.get('trainingtype')?.errors?.['required']">Training type is required</small>
            </div>
          </div>
          <div class="col-md-4">
            <label class="required-label">{{ 'field.trainingDate' | translate }}<span class="text-danger">*</span></label>
            <input type="date" class="form-control" formControlName="date" required>
            <div class="text-danger" *ngIf="entry.get('date')?.invalid && entry.get('date')?.touched">
              <small *ngIf="entry.get('date')?.errors?.['required']">Training date is required</small>
            </div>
          </div>
          <div class="col-md-4 d-flex align-items-end">
            <button type="button" class="btn btn-danger btn-sm me-1" (click)="removeTrainingEntry(i)" *ngIf="trainingEntries.length > 1">-</button>
            <button type="button" class="btn btn-success btn-sm" (click)="addTrainingEntry()">+</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Salary Details Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>Salary Details</h2>
      <div class="row">
        <div class="col-md-3">
          <label class="required-label">Last Salary Revised Date<span class="text-danger">*</span></label>
          <input type="date" class="form-control" formControlName="lastSalaryRevisedDate" required>
          <div class="text-danger" *ngIf="employeeForm.get('lastSalaryRevisedDate')?.invalid && employeeForm.get('lastSalaryRevisedDate')?.touched">
            <small *ngIf="employeeForm.get('lastSalaryRevisedDate')?.errors?.['required']">Last salary revised date is required</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">Group<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="group" required>
            <option value="" disabled selected>Select Group</option>
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
            <option value="D">D</option>
          </select>
          <div class="text-danger" *ngIf="employeeForm.get('group')?.invalid && employeeForm.get('group')?.touched">
            <small *ngIf="employeeForm.get('group')?.errors?.['required']">Group is required</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">Pay Band<span class="text-danger">*</span></label>
          <select class="form-control" formControlName="payband" required>
            <option value="" disabled selected>Select Pay Band</option>
            <option value="Rs.4800 - 10000">Rs.4800 - 10000</option>
            <option value="Rs.5200 - 20200">Rs.5200 - 20200</option>
            <option value="Rs.15600 - 39100">Rs.15600 - 39100</option>
            <option value="Rs.37400-67000">Rs.37400-67000</option>
          </select>
          <div class="text-danger" *ngIf="employeeForm.get('payband')?.invalid && employeeForm.get('payband')?.touched">
            <small *ngIf="employeeForm.get('payband')?.errors?.['required']">Pay band is required</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class="required-label">Grade Pay<span class="text-danger">*</span></label>
          <input type="number" class="form-control" formControlName="gradepay" required placeholder="Enter grade pay">
          <div class="text-danger" *ngIf="employeeForm.get('gradepay')?.invalid && employeeForm.get('gradepay')?.touched">
            <small *ngIf="employeeForm.get('gradepay')?.errors?.['required']">Grade pay is required</small>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Nomination Details Card - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.nominationDetails' | translate }}</h2>
      <div formArrayName="nominationEntries">
        <div *ngFor="let entry of nominationEntries.controls; let i = index" [formGroupName]="i" class="nomination-entry-container mb-4">
          <div class="card border-left-primary">
            <div class="card-header bg-light">
              <h6 class="mb-0">
                <i class="bi bi-person-heart me-2"></i>Nominee {{ i + 1 }} Details
              </h6>
            </div>
            <div class="card-body">
              <!-- First Row: Basic Information -->
              <div class="row">
                <div class="col-md-3">
                  <label class="required-label">{{ 'field.nomineeName' | translate }}<span class="text-danger">*</span></label>
                  <input type="text" class="form-control" formControlName="nomineename" required>
                  <div class="text-danger" *ngIf="entry.get('nomineename')?.invalid && entry.get('nomineename')?.touched">
                    <small *ngIf="entry.get('nomineename')?.errors?.['required']">Nominee name is required</small>
                    <small *ngIf="entry.get('nomineename')?.errors?.['invalidEmployeeName']">Name should start with capital letter and contain only letters and spaces</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <label class="required-label">{{ 'field.relationship' | translate }}<span class="text-danger">*</span></label>
                  <select class="form-control" formControlName="relationship" required>
                    <option value="" disabled>{{ 'option.selectRelationship' | translate }}</option>
                    <option value="Spouse">{{ 'option.spouse' | translate }}</option>
                    <option value="Son">{{ 'option.son' | translate }}</option>
                    <option value="Daughter">{{ 'option.daughter' | translate }}</option>
                    <option value="Father">{{ 'option.father' | translate }}</option>
                    <option value="Mother">{{ 'option.mother' | translate }}</option>
                    <option value="Brother">{{ 'option.brother' | translate }}</option>
                    <option value="Sister">{{ 'option.sister' | translate }}</option>
                    <option value="Other">{{ 'option.other' | translate }}</option>
                  </select>
                  <div class="text-danger" *ngIf="entry.get('relationship')?.invalid && entry.get('relationship')?.touched">
                    <small *ngIf="entry.get('relationship')?.errors?.['required']">Relationship is required</small>
                  </div>
                </div>
                <div class="col-md-2">
                  <label class="required-label">{{ 'field.age' | translate }}<span class="text-danger">*</span></label>
                  <input type="number" class="form-control" formControlName="age" min="1" max="120" required>
                  <div class="text-danger" *ngIf="entry.get('age')?.invalid && entry.get('age')?.touched">
                    <small *ngIf="entry.get('age')?.errors?.['required']">Age is required</small>
                    <small *ngIf="entry.get('age')?.errors?.['min'] || entry.get('age')?.errors?.['max']">Age must be between 1 and 120</small>
                  </div>
                </div>
                <div class="col-md-2">
                  <label class="required-label">{{ 'field.gender' | translate }}<span class="text-danger">*</span></label>
                  <select class="form-control" formControlName="gender" required>
                    <option value="" disabled>{{ 'option.selectGender' | translate }}</option>
                    <option value="Male">{{ 'option.male' | translate }}</option>
                    <option value="Female">{{ 'option.female' | translate }}</option>
                    <option value="Other">{{ 'option.other' | translate }}</option>
                  </select>
                  <div class="text-danger" *ngIf="entry.get('gender')?.invalid && entry.get('gender')?.touched">
                    <small *ngIf="entry.get('gender')?.errors?.['required']">Gender is required</small>
                  </div>
                </div>
                <div class="col-md-2">
                  <label class="required-label">{{ 'field.percentageShare' | translate }}<span class="text-danger">*</span></label>
                  <input type="number" class="form-control" formControlName="percentageofshare" min="1" max="100" required>
                  <div class="text-danger" *ngIf="entry.get('percentageofshare')?.invalid && entry.get('percentageofshare')?.touched">
                    <small *ngIf="entry.get('percentageofshare')?.errors?.['required']">Percentage is required</small>
                    <small *ngIf="entry.get('percentageofshare')?.errors?.['min'] || entry.get('percentageofshare')?.errors?.['max']">Percentage must be between 1 and 100</small>
                  </div>
                </div>
              </div>

              <!-- Second Row: Address and Photo -->
              <div class="row mt-3">
                <div class="col-md-6">
                  <label class="required-label">{{ 'field.address' | translate }}<span class="text-danger">*</span></label>
                  <textarea class="form-control" formControlName="address" required rows="3" placeholder="Enter complete address"></textarea>
                  <div class="text-danger" *ngIf="entry.get('address')?.invalid && entry.get('address')?.touched">
                    <small *ngIf="entry.get('address')?.errors?.['required']">Address is required</small>
                  </div>
                </div>
                <div class="col-md-6">
                  <label class="required-label">
                    <i class="bi bi-camera me-1"></i>Nominee Photo<span class="text-danger">*</span>
                  </label>

                  <!-- Show file input only if no photo is loaded -->
                  <div *ngIf="!hasNomineePhoto(i)">
                    <input type="file" class="form-control" (change)="onNomineePhotoSelected($event, i)" accept=".png,.jpg,.jpeg">
                    <small class="text-muted">Only PNG, JPG, JPEG files allowed (Max: 5MB)</small>
                  </div>

                  <!-- Show "Image Selected" when photo is loaded -->
                  <div *ngIf="hasNomineePhoto(i)" class="mb-2">
                    <div class="alert alert-success d-flex align-items-center py-2">
                      <i class="bi bi-check-circle-fill me-2"></i>
                      <span class="me-auto">Image Selected</span>
                      <button type="button" class="btn btn-sm btn-outline-primary" (click)="changeNomineePhoto(i)">
                        <i class="bi bi-pencil me-1"></i>Change
                      </button>
                    </div>
                  </div>

                  <!-- Hidden file input for changing photo -->
                  <input type="file"
                         class="d-none"
                         #nomineePhotoInput
                         (change)="onNomineePhotoSelected($event, i)"
                         accept=".png,.jpg,.jpeg"
                         [id]="'nomineePhotoInput' + i">

                  <div class="text-danger" *ngIf="nomineePhotoErrors && nomineePhotoErrors[i]">
                    <small>{{nomineePhotoErrors[i]}}</small>
                  </div>

                  <!-- Nominee Photo Preview -->
                  <div class="mt-2" *ngIf="hasNomineePhoto(i)">
                    <div class="position-relative">
                      <img
                        [src]="nomineePhotoPreviews[i]"
                        alt="Nominee {{i + 1}} Preview"
                        style="max-width: 120px; max-height: 120px; border: 1px solid #ddd; border-radius: 4px; object-fit: cover;"
                        (error)="onNomineeImageError($event, i)"
                        (load)="console.log('Nominee ' + (i + 1) + ' photo loaded successfully')">
                      <div class="position-absolute top-0 end-0 bg-success text-white rounded-circle" style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; margin: 2px;">
                        <i class="bi bi-check"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="row mt-3">
                <div class="col-md-12 text-end">
                  <button type="button" class="btn btn-danger btn-sm me-2" (click)="removeNominationEntry(i)" *ngIf="nominationEntries.length > 1">
                    <i class="bi bi-trash me-1"></i>Remove Nominee
                  </button>
                  <button type="button" class="btn btn-success btn-sm" (click)="addNominationEntry()">
                    <i class="bi bi-plus me-1"></i>Add Nominee
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Leave Balance Section - Shows for all -->
  <div class="card wide-card mt-4">
    <div class="card-body">
      <h2>{{ 'dashboard.leaveBalance' | translate }}</h2>
      <div formArrayName="leaveEntries">
        <div *ngFor="let entry of leaveEntries.controls; let i = index" [formGroupName]="i" class="row leave-entry mt-2">
          <div class="col-md-2">
            <label class="required-label">{{ 'field.leaveType' | translate }}<span class="text-danger">*</span></label>
            <select class="form-control" formControlName="leaveType" required>
              <option value="" disabled>{{ 'option.selectLeaveType' | translate }}</option>
              <option value="Earned Leave">{{ 'option.earnedLeave' | translate }}</option>
              <option value="Casual Leave">{{ 'option.casualLeave' | translate }}</option>
              <option value="Maternity Leave">{{ 'option.maternityLeave' | translate }}</option>
              <option value="Hospital Leave">{{ 'option.hospitalLeave' | translate }}</option>
              <option value="Special Disability Leave">{{ 'option.specialDisabilityLeave' | translate }}</option>
              <option value="Extraordinary Leave">{{ 'option.extraordinaryLeave' | translate }}</option>
              <option value="Special Casual Leave">{{ 'option.specialCasualLeave' | translate }}</option>
              <option value="Restricted Holidays">{{ 'option.restrictedHolidays' | translate }}</option>
            </select>
            <div class="text-danger" *ngIf="entry.get('leaveType')?.invalid && entry.get('leaveType')?.touched">
              <small *ngIf="entry.get('leaveType')?.errors?.['required']">Leave type is required</small>
            </div>
          </div>
          <div class="col-md-2">
            <label class="required-label">{{ 'field.openingBalance' | translate }}<span class="text-danger">*</span></label>
            <input type="number" class="form-control" formControlName="openingBalance" min="0" required>
            <div class="text-danger" *ngIf="entry.get('openingBalance')?.invalid && entry.get('openingBalance')?.touched">
              <small *ngIf="entry.get('openingBalance')?.errors?.['required']">Opening balance is required</small>
              <small *ngIf="entry.get('openingBalance')?.errors?.['min']">Opening balance must be 0 or greater</small>
            </div>
          </div>
          <div class="col-md-2">
            <label class="required-label">{{ 'field.closingBalance' | translate }}<span class="text-danger">*</span></label>
            <input type="number" class="form-control" formControlName="closingBalance" min="0" required>
            <div class="text-danger" *ngIf="entry.get('closingBalance')?.invalid && entry.get('closingBalance')?.touched">
              <small *ngIf="entry.get('closingBalance')?.errors?.['required']">Closing balance is required</small>
              <small *ngIf="entry.get('closingBalance')?.errors?.['min']">Closing balance must be 0 or greater</small>
            </div>
          </div>
          <div class="col-md-2">
            <label class="required-label">{{ 'field.entryDate' | translate }}<span class="text-danger">*</span></label>
            <input type="date" class="form-control" formControlName="entryDate" required>
            <div class="text-danger" *ngIf="entry.get('entryDate')?.invalid && entry.get('entryDate')?.touched">
              <small *ngIf="entry.get('entryDate')?.errors?.['required']">Entry date is required</small>
            </div>
          </div>
          <div class="col-md-2 d-flex align-items-end">
            <button type="button" class="btn btn-danger btn-sm me-1" (click)="removeLeaveEntry(i)" *ngIf="leaveEntries.length > 1">-</button>
            <button type="button" class="btn btn-success btn-sm" (click)="addLeaveEntry()">+</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Submit Buttons -->
  <div class="text-center mt-4 mb-5">
    <button type="submit" class="btn btn-primary btn-lg">
      {{ isEditMode ? ('button.update' | translate) : ('button.save' | translate) }}
    </button>
    <button type="button" class="btn btn-secondary btn-lg ms-2" (click)="router.navigate(['/view'])">
      {{ 'button.cancel' | translate }}
    </button>
  </div>
</form>












