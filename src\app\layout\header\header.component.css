.header-component {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  color: #203664;
  padding: 20px 20px;
  min-height: 80px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hederlogo img {
  height: 70px;
}

.btn-menu {
  background-color: #203664;
  border: none;
  color: white;
  font-size: 1.3rem;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-menu:hover {
  background-color: #1a2d54;
  transform: scale(1.05);
}

.btn-menu:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(32, 54, 100, 0.3);
}

.btn-avatar {
  border-radius: 50%;
  background-color: #203664;
  color: white;
  width: 45px;
  height: 45px;
  font-weight: bold;
  text-align: center;
  padding: 0;
  line-height: 45px;
  border: 2px solid white;
}

.btn-avatar:hover {
  background-color: #1a2d54;
  border-color: #ccc;
}

.custom-toggle-group .btn {
  border-radius: 0;
  border-color: #203664;
  color: #203664;
  background-color: white;
  font-weight: 500;
  padding: 8px 16px;
}

.custom-toggle-group .btn:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.custom-toggle-group .btn:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.custom-toggle-group .btn.active {
  background-color: #203664;
  color: white;
  border-color: #203664;
}

.custom-toggle-group .btn:hover {
  background-color: #1a2d54;
  border-color: #1a2d54;
  color: white;
}

.custom-toggle-group .btn.active:hover {
  background-color: #1a2d54;
  color: white;
  border-color: #1a2d54;
}

.profileName {
  margin-right: 15px;
  color: #203664;
  font-weight: 500;
  font-size: 1.1rem;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
}

.dropdown-menu a {
  color: #333 !important;
  padding: 10px 16px;
  display: flex;
  align-items: center;
}

.dropdown-menu a:hover {
  background-color: #203664;
  color: white !important;
}

.dropdown-menu i {
  font-size: 1.1rem;
}

 /* .header-component {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color:white #203664;
      color: #203664;
      padding: 10px 20px;
    }
    .hederlogo img {
      height: 60px;
    }
    .btn-menu {
      background: none;
      border: none;
      color: white;
    }
    .btn-avatar {
      border-radius: 50%;
      background-color: white;
      color: #203664;
      width: 40px;
      height: 40px;
      font-weight: bold;
      text-align: center;
      padding: 0;
      line-height: 40px;
    }
    .custom-toggle-group .btn {
      border-radius: 0;
    }
    .profileName {
      margin-right: 10px;
    }
    .dropdown-menu a {
      color: #000 !important;
    } */