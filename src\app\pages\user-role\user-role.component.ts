import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { UserserviceService } from '../../services/userservice.service';

@Component({
  selector: 'app-user-role',
  imports: [ ReactiveFormsModule,CommonModule],
  templateUrl: './user-role.component.html',
  styleUrl: './user-role.component.css'
})
export class UserRoleComponent {
  roleForm: FormGroup;

  constructor(private fb: FormBuilder, private service:UserserviceService) {
    this.roleForm = this.fb.group({
      role: ['', [Validators.required]]
    });
  }

  onSubmit() {
    if (this.roleForm.valid) {
      this.service.createrole(this.roleForm.value).subscribe({
        next: (res) => {
          alert('Role submitted successfully!');
          this.roleForm.reset();
        },
        error: (err) => {
          console.error('Error submitting role:', err);
        }
      });
    }
  }
 

}
