import { Component, OnInit } from '@angular/core';
import { Employee } from '../form-fill/employee.model';
import { Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { StatusServiceService } from '../../services/status-service.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormGroup, FormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-second-level-list',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './second-level-list.component.html',
  styleUrl: './second-level-list.component.css'
})
export class SecondLevelListComponent implements OnInit {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  statusFormGroup:FormGroup;
  secondLevelList:any[]=[];
  searchTerm: string = '';
  selectedEmployee: Employee | null = null;
  selectedEmployeePDFs: File[] = [];
  selectedEmployeeDetails: any = null;
  isLoadingDetails: boolean = false;
  rejectionRemarks: string;
  isRejectionModalOpen: boolean;
  isSubmitting: boolean;
  submitSuccess: boolean;
  submitMessage: string;
  registrationForm: any;

  constructor(
    private statusService: StatusServiceService, private employeeService: EmployeeService,
    private router: Router, private http:HttpClient, private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.statusFormGroup= this.formBuilder.group({
      status:[''],
      remarks:['']
    })
    this.secondLevelListShow();
  }


  secondLevelListShow(){
    this.statusService.getSecondLevelList().subscribe({
      next: (employees) => {
        console.log('Employees loaded:', employees);
        this.employees = employees;
        this.secondLevelList = [...this.employees];
      },
      error: (error) => {
        console.error('Error loading employees:', error);
        alert('Failed to load employees. Please try again later.');
      }
    });
  }




  openApprovalModal(employee: any): void {
    this.selectedEmployee = employee;
    this.statusFormGroup.reset(); // clear previous data
  }




  statusApproval(){

  }

  statusSubmit(id: any) {
    if (this.statusFormGroup.valid) {
      this.isSubmitting = true;
      const { status, remarks } = this.statusFormGroup.value;
      const requestData = { status, remarks };

      console.log('Submitting status update:', { id, requestData });

      this.statusService.updateStatus(id, requestData).subscribe({
        next: (response) => {
          console.log('Full API Response:', response);
          console.log('Response status:', response?.status);
          console.log('Response type:', typeof response);

          this.isSubmitting = false;
          this.submitSuccess = true;
          this.submitMessage = 'Status updated successfully!';

          // Close the modal
          this.closeModal('approvalModal');

          // Reset the form
          this.statusFormGroup.reset();

          // Show success message using SweetAlert2
          Swal.fire({
            title: 'Success!',
            text: 'Status updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK'
          }).then(() => {
            // Reload the second level list data instead of the entire page
            this.secondLevelListShow();
          });
        },
        error: (error) => {
          console.error('Status update error details:', error);
          console.error('Error status code:', error.status);
          console.error('Error response:', error.error);

          this.isSubmitting = false;
          this.submitSuccess = false;
          this.submitMessage = 'Failed to update status. Please try again.';

          // Check if it's actually a successful response with error structure
          if (error.status === 200 || (error.error && error.error.success)) {
            // Handle case where backend returns 200 but with error structure
            Swal.fire({
              title: 'Success!',
              text: 'Status updated successfully!',
              icon: 'success',
              confirmButtonText: 'OK'
            }).then(() => {
              this.closeModal('approvalModal');
              this.statusFormGroup.reset();
              this.secondLevelListShow();
            });
          } else {
            const errorMessage = error.error?.message || error.message || 'Failed to update status. Please try again.';
            Swal.fire({
              title: 'Error!',
              text: errorMessage,
              icon: 'error',
              confirmButtonText: 'OK'
            });
          }
        },
        complete: () => {
          console.log('Status update request completed');
          this.isSubmitting = false;
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.statusFormGroup.controls).forEach(key => {
        this.statusFormGroup.get(key)?.markAsTouched();
      });
    }
  }

  // Method to close modal programmatically
  private closeModal(modalId: string) {
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
      // For Bootstrap 5
      const modal = (window as any).bootstrap?.Modal?.getInstance(modalElement);
      if (modal) {
        modal.hide();
      } else {
        // Fallback for Bootstrap 4 or if Bootstrap 5 instance not found
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
          backdrop.remove();
        }
      }
    }
  }




  // searchEmployees(): void {
  //   if (!this.searchTerm.trim()) {
  //     this.filteredEmployees = [...this.employees];
  //     return;
  //   }

  //   const searchTermLower = this.searchTerm.toLowerCase();
  //   this.filteredEmployees = this.employees.filter(employee =>
  //     employee.employeeName.toLowerCase().includes(searchTermLower) ||
  //     employee.designation.toLowerCase().includes(searchTermLower) ||
  //     employee.Authority.toLowerCase().includes(searchTermLower) ||
  //     employee.district.toLowerCase().includes(searchTermLower) ||employee.location.toLowerCase().includes(searchTermLower) ||
  //     employee.empId.toLowerCase().includes(searchTermLower)
  //   );
  // }

  editEmployee(employee: Employee): void {
    this.router.navigate(['/dashboard'], {
      queryParams: { edit: true, empId: employee.id || employee.empId }
    });
  }

  viewEmployee(employee: Employee): void {
    console.log('Viewing employee details for employee:', employee);
    console.log('Employee ID to be used for API call:', employee.id);

    // Use employee.id specifically for the API call
    if (!employee.id) {
      console.error('No employee.id found');
      this.selectedEmployee = employee;
      alert('Could not find employee ID. Showing basic information.');
      return;
    }

    this.http.get<any>(`http://localhost:8082/employee/getEmp/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Complete employee data received:', response);
          this.selectedEmployee = this.mapCompleteEmployeeData(response);
        },
        error: (error) => {
          console.error('Error fetching employee details:', error);
          console.error('API URL called:', `http://localhost:8082/employee/getEmp/${employee.id}`);
          // Fallback to existing data if API fails
          this.selectedEmployee = employee;
          alert('Could not load complete employee details. Showing basic information.');
        }
      });
  }

  refreshData(): void {
    this.searchTerm = '';
  }

  firstLevelAprroval(employee: Employee): void {
    this.selectedEmployee = employee;
    this.selectedEmployeeDetails = null;
    this.isLoadingDetails = true;

    // Call API to get detailed employee information
    this.employeeService.getEmployeeDetails(employee.id).subscribe({
      next: (details) => {
        console.log('Employee details loaded:', details);
        this.selectedEmployeeDetails = details;
        this.isLoadingDetails = false;
      },
      error: (error) => {
        console.error('Error loading employee details:', error);
        this.isLoadingDetails = false;
        // Keep the basic employee data if API fails
        alert('Failed to load detailed employee information. Showing basic details only.');
      }
    });
  }

  private mapCompleteEmployeeData(apiData: any): Employee {
    return {
      // Basic Information
      id: apiData.id || apiData.employeeId || apiData.ecpfNumber || '',
      empId: apiData.ecpfNumber || apiData.empId || '',
      ecpfNumber: apiData.ecpfNumber || '',
      employeeName: apiData.employeeName || '',
      designation: apiData.currentDesignation || apiData.designation || '',
      Authority: apiData.section || '',
      district: apiData.district || '',
      location: apiData.nativePlaceAndTaluk || '',
      fatherName: apiData.fatherName || apiData.profile?.fatherName || '',
      motherName: apiData.motherName || apiData.profile?.motherName || '',
      dateOfBirth: apiData.dateOfBirth || apiData.profile?.dateOfBirth || '',
      createdAt: apiData.createdAt || '',
      religion: apiData.religion || '',
      community: apiData.community || apiData.profile?.community || '',
      caste: apiData.caste || apiData.profile?.caste || '',
      personalIdentificationMarks: (apiData.personalIdentificationmark1 || '') +
        (apiData.personalIdentificationmark2 ? ', ' + apiData.personalIdentificationmark2 : ''),
      dateOfEntryIntoService: apiData.dateOfEntry || '',
      educationalQualification: this.formatEducationQualifications(apiData.educationQualifications),

      // Missing required fields
      status: apiData.status || '',
      dateOfEntry: apiData.dateOfEntry || '',
      educationEnties: this.mapEducationEntries(apiData.educationQualifications || []),

      // Contact Information
      email: apiData.email || apiData.profile?.email || '',
      gender: apiData.gender || apiData.profile?.gender || '',
      presentAddress: apiData.presentAddress || apiData.profile?.presentaddress || '',
      permanentAddress: apiData.permanentAddress || apiData.profile?.permanentaddress || '',

      // Account Details
      bankAccountNo: apiData.accountDetails?.bankaccountnumber || '',
      ifscCode: apiData.accountDetails?.ifsccode || '',
      bankName: apiData.accountDetails?.bankname || '',
      panNumber: apiData.panNumber || '',
      uanNumber: apiData.accountDetails?.uannumber || '',
      aadharNumber: apiData.accountDetails?.aadharnumber || '',

      // Service Records
      serviceRecords: apiData.serviceHistory || [],
      leaveEntries: this.mapLeaveBalances(apiData.leaveBalances || []),

      // Additional Information
      registerNumber: apiData.registerNumber || '',
      personalIdMark1: apiData.personalIdentificationmark1 || '',
      personalIdMark2: apiData.personalIdentificationmark2 || '',
      nativeAndTaluk: apiData.nativePlaceAndTaluk || '',

      // Arrays
      serviceEntries: this.mapServiceHistory(apiData.serviceHistory || []),
      trainingEntries: this.mapTrainingDetails(apiData.trainingDetails || []),
      punishmentEntries: this.mapPunishmentDetails(apiData.punishmentDetails || []),
      nominationEntries: this.mapNominees(apiData.nominees || []),
      remarks: apiData.remarks || '',
      rejectedBy: apiData.rejectedBy || ''
    };
  }

  private mapLeaveBalances(leaveBalances: any[]): any[] {
    return leaveBalances.map(leave => ({
      leaveType: leave.leaveType || '',
      leaveBalanceCount: leave.openingBalance || leave.closingBalance || 0,
      openingBalance: leave.openingBalance || 0,
      closingBalance: leave.closingBalance || 0,
      entryDate: leave.entryDate || ''
    }));
  }

  private mapServiceHistory(serviceHistory: any[]): any[] {
    return serviceHistory.map(service => ({
      date: service.date || service.dateofappointment || service.joiningdate || service.fromdate || service.promoteddate || service.punishmentdate || '',
      type: service.type || '',
      status: service.status || 'Active',

      // Appointment fields
      appointmentType: service.appointmenttype || '',
      modeOfAppointment: service.modeofappointment || '',
      dateOfAppointment: service.dateofappointment || '',
      proceedingOrderNo: service.proceedingorderno || '',
      proceedingOrderDate: service.proceedingorderdate || '',

      // Promotion fields
      joiningDate: service.joiningdate || '',
      fromDesignation: service.fromdesignation || '',
      toPromoted: service.topromoted || '',
      promotedDate: service.promoteddate || '',

      // Transfer fields
      fromDate: service.fromdate || '',
      toDate: service.todate || '',
      fromPlace: service.fromplace || '',
      toPlace: service.toplace || '',

      // Increment fields
      typeOfIncrement: service.typeofincrement || '',
      incrementType: service.incrementtype || '',

      // Deputation fields
      designation: service.designation || '',
      originalDesignation: service.originaldesignation || '',
      parentDepartment: service.parentdepartment || '',

      // Punishment fields
      punishmentType: service.punishmenttype || '',
      punishmentDate: service.punishmentdate || '',
      caseDetails: service.casedetails || '',
      caseNumber: service.caseNumber || '',
      caseDate: service.caseDate || '',
      personInvolved: service.personInvolved || '',
      involvedPersonsWithNames: service.involvedPersonsWithNames || [],
      presentStatus: service.presentStatus || '',
      description: service.description || ''
    }));
  }

  private mapTrainingDetails(trainingDetails: any[]): any[] {
    return trainingDetails.map(training => ({
      trainingType: training.trainingtype || '',
      trainingDate: training.date || ''
    }));
  }

  private mapPunishmentDetails(punishmentDetails: any[]): any[] {
    return punishmentDetails.map(punishment => ({
      punishmentType: punishment.punishmenttype || '',
      punishmentDate: punishment.date || '',
      caseDetails: punishment.casedetails || ''
    }));
  }

  private mapNominees(nominees: any[]): any[] {
    return nominees.map(nominee => ({
      nomineeName: nominee.nomineename || '',
      address: nominee.address || '',
      relationship: nominee.relationship || '',
      age: nominee.age || 0,
      percentageOfShare: nominee.percentageofshare || 0,
      gender: nominee.gender || ''
    }));
  }

  private mapEducationEntries(educationQualifications: any[]): any[] {
    return educationQualifications.map(education => ({
      qualification: education.qualification || '',
      courseName: education.coursename || '',
      instituteName: education.schoolname || education.collegename || education.universityname || '',
      universityName: education.universityname || '',
      specialization: education.specialization || ''
    }));
  }

  private formatEducationQualifications(educationQualifications: any[]): string {
    if (!educationQualifications || educationQualifications.length === 0) {
      return 'Not specified';
    }
    return educationQualifications.map(edu => edu.qualification).join(', ');
  }
}
