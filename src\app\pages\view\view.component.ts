import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { Employee } from '../form-fill/employee.model';
import { HttpClient } from '@angular/common/http';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-view',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './view.component.html',
  styleUrl: './view.component.css'
})
export class ViewComponent implements OnInit {

  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  paginatedEmployees: Employee[] = [];
  searchTerm: string = '';
  selectedEmployee: Employee | null = null;
  selectedEmployeePDFs: File[] = [];
  downloadingFiles: Set<number> = new Set();
  isLoading: boolean = false;
  userRole: string | null = null;
  isRejectionModalOpen: boolean = false;
  rejectionRemarks: string = '';
  role: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 0;
  totalItems: number = 0;

  // Make Math available in template
  Math = Math;

  constructor(
    private employeeService: EmployeeService,
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.checkUserRole();
    this.loadEmployees();
    this.role = localStorage.getItem('role');
  }

  loadEmployees(): void {
    this.isLoading = true;

    // Check if there's stored employeeResponseData in localStorage
    const employeeResponseDataStr = localStorage.getItem('employeeResponseData');
    if (employeeResponseDataStr && this.canApproveReject()) {
      try {
        const employeeData = JSON.parse(employeeResponseDataStr);
        console.log('Loading single employee from localStorage:', employeeData);

        // Map the single employee data and display it
        const singleEmployee = this.mapSingleEmployeeFromList(employeeData);
        this.employees = [singleEmployee];
        this.filteredEmployees = [singleEmployee];
        this.updatePagination();
        this.isLoading = false;
        return;
      } catch (error) {
        console.error('Error parsing stored employee responseData:', error);
      }
    }

    // Fetch employees from API if no stored data or user has role
    this.http.get<any[]>('http://localhost:8082/employee/getList/'+sessionStorage.getItem('username'))
      .subscribe({
        next: (response) => {
          console.log('Employees fetched from API:', response);
          this.employees = this.mapApiResponseToEmployees(response);
          this.filteredEmployees = [...this.employees];
          this.updatePagination();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching employees from API:', error);
          // Fallback to localStorage if API fails
          this.loadEmployeesFromLocalStorage();
          this.isLoading = false;
        }
      });
  }

  private loadEmployeesFromLocalStorage(): void {
    // Fallback method - load from localStorage if API fails
    const existingEmployees = this.employeeService.loadEmployees();

    if (existingEmployees.length === 0) {
      // this.employeeService.generateSampleData();
      this.employees = this.employeeService.loadEmployees();
    } else {
      this.employees = existingEmployees;
    }

    this.filteredEmployees = [...this.employees];
    this.updatePagination();
  }





  searchEmployees(): void {
    if (!this.searchTerm.trim()) {
      this.filteredEmployees = [...this.employees];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase();
      this.filteredEmployees = this.employees.filter(employee =>
        employee.employeeName.toLowerCase().includes(searchTermLower) ||
        employee.designation.toLowerCase().includes(searchTermLower) ||
        employee.district.toLowerCase().includes(searchTermLower) ||
        employee.ecpfNumber.toLowerCase().includes(searchTermLower) ||
        employee.email.toLowerCase().includes(searchTermLower)
      );
    }

    this.currentPage = 1; // Reset to first page after search
    this.updatePagination();
  }

  // Pagination methods
  updatePagination(): void {
    this.totalItems = this.filteredEmployees.length;
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);

    // Reset to first page if current page is out of bounds
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = 1;
    }

    // Ensure currentPage is at least 1 if there are items
    if (this.totalItems > 0 && this.currentPage < 1) {
      this.currentPage = 1;
    }

    console.log('Pagination updated:', {
      totalItems: this.totalItems,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsPerPage: this.itemsPerPage
    });

    this.updatePaginatedEmployees();
  }

  updatePaginatedEmployees(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedEmployees = this.filteredEmployees.slice(startIndex, endIndex);
  }

  onItemsPerPageChange(event: any): void {
    this.itemsPerPage = parseInt(event.target.value);
    this.currentPage = 1; // Reset to first page
    this.updatePagination();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePaginatedEmployees();
    }
  }

  goToPreviousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePaginatedEmployees();
    }
  }

  goToNextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePaginatedEmployees();
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      const half = Math.floor(maxVisiblePages / 2);
      let start = Math.max(1, this.currentPage - half);
      let end = Math.min(this.totalPages, start + maxVisiblePages - 1);

      if (end - start < maxVisiblePages - 1) {
        start = Math.max(1, end - maxVisiblePages + 1);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }

    return pages;
  }
  checkUserRole(): void {
    this.userRole = sessionStorage.getItem('role');
    console.log('User role in view component:', this.userRole);
  }

  canApproveReject(): boolean {
    return this.userRole === null || this.userRole === '' || this.userRole === 'null';
  }

  // Check if current session is employee API login
  isEmployeeLogin(): boolean {
    const employeeResponseData = localStorage.getItem('employeeResponseData');
    const token = localStorage.getItem('token');
    return employeeResponseData !== null && token === 'employee_session';
  }

  // Check if approve/reject actions are allowed based on employee status
  canPerformActions(): boolean {
    if (!this.canApproveReject()) {
      return true;
    }

    const employeeResponseDataStr = localStorage.getItem('employeeResponseData');
    if (employeeResponseDataStr) {
      try {
        const employeeData = JSON.parse(employeeResponseDataStr);
        const status = employeeData.status;
        // Only allow actions if status is pending, user approval, or null/undefined
        return status === 'pending' || status === 'PENDING' || status === 'user approval' || !status || status === '';
      } catch (error) {
        console.error('Error parsing employee data for action check:', error);
        return false;
      }
    }
    return false;
  }
  viewEmployee(employee: any): void {
    console.log('Viewing employee details for employee:', employee);
    console.log('Employee ID to be used for API call:', employee.id);
    if (this.canApproveReject()) {
      const employeeResponseDataStr = localStorage.getItem('employeeResponseData');
      if (employeeResponseDataStr) {
        try {
          const employeeData = JSON.parse(employeeResponseDataStr);
          console.log('Using stored employee responseData for detailed view:', employeeData);
          // Use the same mapping logic as the list view
          this.selectedEmployee = this.mapSingleEmployeeFromList(employeeData);

          // The profile photo URL should already be set by mapSingleEmployeeFromList
          console.log('Selected employee with profile photo:', this.selectedEmployee);
          // Initialize nominee photo loading for stored data
          this.initializeNomineePhotos();
          return;
        } catch (error) {
          console.error('Error parsing stored employee responseData:', error);
        }
      }
    }

    // Use employee.id specifically for the API call
    if (!employee.id) {
      console.error('No employee.id found');
      this.selectedEmployee = employee;
      alert('Could not find employee ID. Showing basic information.');
      return;
    }

    // Call API to get complete employee details using employee.id
    this.http.get<any>(`http://localhost:8082/employee/getEmp/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Complete employee data received from API:', response);
          console.log('Nominees in API response:', response.nominees);

          this.selectedEmployee = this.mapCompleteEmployeeData(response);

          console.log('Mapped employee nomination entries:', this.selectedEmployee.nominationEntries);
          console.log('Nominee photos after mapping:', this.selectedEmployee.nominationEntries?.map((nominee, index) => ({
            index: index + 1,
            name: nominee.nomineeName,
            rawPhoto: nominee.nomineePhoto,
            processedPhotoUrl: (nominee as any).nomineePhotoUrl
          })));

          // Load profile photo after setting employee data
          this.loadEmployeeProfilePhoto(employee.id.toString());
          // Initialize nominee photo loading
          this.initializeNomineePhotos();
        },
        error: (error) => {
          console.error('Error fetching employee details:', error);
          console.error('API URL called:', `http://localhost:8082/employee/getEmp/${employee.id}`);
          // Fallback to existing data if API fails
          this.selectedEmployee = employee;
          alert('Could not load complete employee details. Showing basic information.');
        }
      });
  }
  private mapApiResponseToEmployees(apiData: any[]): Employee[] {
    return apiData.map(emp => this.mapSingleEmployeeFromList(emp));
  }

  private mapSingleEmployeeFromList(emp: any): Employee {
    const mappedEmployee = {
      // Required fields from API response
      id: emp.id || emp.employeeId || emp.ecpfNumber || '',
      empId: emp.ecpfNumber || emp.id?.toString() || '',
      employeeName: emp.employeeName || '',
      designation: emp.currentDesignation || '',
      Authority: emp.section || '',
      district: emp.district || '',
      location: emp.nativePlaceAndTaluk || emp.district || '',
      fatherName: emp.fatherName || emp.profile?.fatherName || '',
      dateOfBirth: emp.dateOfBirth || emp.profile?.dateOfBirth || '',
      createdAt:emp.createdAt || '',
      religion: emp.religion || '',
      community: emp.community || emp.profile?.community || '',
      personalIdentificationMarks: (emp.personalIdentificationmark1 || '') +
        (emp.personalIdentificationmark2 ? ', ' + emp.personalIdentificationmark2 : ''),
      dateOfEntryIntoService: emp.dateOfEntry || '',

      // Missing required fields from Employee interface
      remarks: emp.remarks || '',
      rejectedBy: emp.rejectedBy || '',
      status: emp.status || 'pending',

      educationalQualification: this.formatEducationQualifications(emp.educationQualifications || []),
      serviceRecords: emp.serviceHistory || [],
      leaveEntries: this.mapLeaveBalances(emp.leaveBalances || []),

      // Optional fields from API response
      ecpfNumber: emp.ecpfNumber || '',
      motherName: emp.motherName || emp.profile?.motherName || '',
      email: emp.email || emp.profile?.email || '',
      gender: emp.gender || emp.profile?.gender || '',
      registerNumber: emp.registerNumber || '',
      caste: emp.caste || emp.profile?.caste || '',
      nativeAndTaluk: emp.nativePlaceAndTaluk || emp.profile?.nativeplaceandtaluk || '',
      dateOfEntry: emp.dateOfEntry || '',
      presentAddress: emp.presentAddress || emp.profile?.presentaddress || '',
      permanentAddress: emp.permanentAddress || emp.profile?.permanentaddress || '',
      personalIdMark1: emp.personalIdentificationmark1 || '',
      personalIdMark2: emp.personalIdentificationmark2 || '',
      bankAccountNo: emp.accountDetails?.bankaccountnumber || '',
      ifscCode: emp.accountDetails?.ifsccode || '',
      bankName: emp.accountDetails?.bankname || '',
      panNumber: emp.panNumber || '',
      uanNumber: emp.accountDetails?.uannumber || '',
      aadharNumber: emp.accountDetails?.aadharnumber || '',

      // Profile object with photo handling
      profile: {
        ...emp.profile,
        profilephoto: emp.profile?.profilephoto || emp.profile?.profilePhoto || emp.profilePhoto || '',
        profilePhoto: emp.profile?.profilePhoto || emp.profile?.profilephoto || emp.profilePhoto || ''
      },

      // Array fields from detailed response
      serviceEntries: this.mapServiceHistory(emp.serviceHistory || []),
      trainingEntries: this.mapTrainingDetails(emp.trainingDetails || []),
      punishmentEntries: this.mapPunishmentDetails(emp.punishmentDetails || []),
      nominationEntries: this.mapNominees(emp.nominees || []),
      educationEnties: this.mapEducationEntries(emp.educationQualifications || []),

      // Salary Details
      salaryDetails: {
        lastSalaryRevisedDate: emp.salaryDetails?.lastSalaryRevisedDate || '',
        group: emp.salaryDetails?.group || emp.group || '',
        payband: emp.salaryDetails?.payband || emp.payband || '',
        gradepay: emp.salaryDetails?.gradepay || emp.gradepay || ''
      }
    };

    // Handle profile photo URL generation
    if (mappedEmployee.profile?.profilephoto || mappedEmployee.profile?.profilePhoto) {
      const photoPath = mappedEmployee.profile.profilephoto || mappedEmployee.profile.profilePhoto;
      const baseUrl = 'http://localhost:8082';

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      (mappedEmployee as any).profilePhotoUrl = fullPhotoUrl;
      console.log('Profile photo URL set for employee:', fullPhotoUrl);
    }

    return mappedEmployee;
  }

  loadEmployeeProfilePhoto(employeeId: string): void {
    console.log('Loading profile photo for employee ID:', employeeId);

    // Check if profile photo exists in profile object
    if (this.selectedEmployee?.profile?.profilephoto) {
      console.log('Profile photo found in profile object:', this.selectedEmployee.profile.profilephoto);

      // Construct the full URL for the profile photo
      const baseUrl = 'http://localhost:8082';
      const profilePhotoPath = this.selectedEmployee.profile.profilephoto;

      // Handle both absolute and relative paths
      let fullPhotoUrl: string;
      if (profilePhotoPath.startsWith('http')) {
        fullPhotoUrl = profilePhotoPath;
      } else if (profilePhotoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${profilePhotoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}/api${profilePhotoPath}`;
      }

      console.log('Full profile photo URL:', fullPhotoUrl);
      (this.selectedEmployee as any).profilePhotoUrl = fullPhotoUrl;
      return;
    }

    // Fallback: Call API to get employee profile photo from document service
    console.log('No profile photo in profile object, trying document service...');
    this.http.get(`http://localhost:8082/document/photo/${employeeId}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log('Profile photo loaded successfully from document service');
          // Create blob URL for the image
          const photoUrl = window.URL.createObjectURL(blob);
          if (this.selectedEmployee) {
            (this.selectedEmployee as any).profilePhotoUrl = photoUrl;
          }
        },
        error: (error) => {
          console.log('No profile photo found in document service or error loading photo:', error);
          // Set profilePhotoUrl to null if no photo is found
          if (this.selectedEmployee) {
            (this.selectedEmployee as any).profilePhotoUrl = null;
          }
        }
      });
  }

  editEmployee(employee: Employee): void {
    this.router.navigate(['/dashboard'], {
      queryParams: { edit: true, empId: employee.id || employee.empId }
    });
  }

  // Image error handling
  onImageError(event: any): void {
    console.log('Error loading profile photo:', event);
    // Hide the image and show placeholder
    if (this.selectedEmployee) {
      (this.selectedEmployee as any).profilePhotoUrl = null;
    }
  }

  // Image load success
  onImageLoad(event: any): void {
    console.log('Profile photo loaded successfully');
  }

  // Format present address from profile table data
  getFormattedPresentAddress(): string {
    console.log('Formatting present address from profile data:', this.selectedEmployee?.profile);

    if (!this.selectedEmployee?.profile) {
      console.log('No profile data available, using fallback address');
      return this.selectedEmployee?.presentAddress || 'Not provided';
    }

    const profile = this.selectedEmployee.profile;

    // Use profile table address fields (prioritize profile table data)
    const addressParts = [
      profile.presentDoorNo,
      profile.presentBuildingName,
      profile.presentStreetAddress,
      profile.presentCity,
      profile.presentPincode
    ].filter(part => part && part.trim() !== '' && part.trim() !== 'null');

    if (addressParts.length > 0) {
      const formattedAddress = addressParts.join(', ');
      console.log('Formatted present address:', formattedAddress);
      return formattedAddress;
    }

    // Fallback to legacy presentaddress field if structured fields are empty
    if (profile.presentaddress && profile.presentaddress.trim() !== '') {
      console.log('Using legacy presentaddress field:', profile.presentaddress);
      return profile.presentaddress;
    }

    // Final fallback to employee table presentAddress
    return this.selectedEmployee.presentAddress || 'Not provided';
  }

  // Format permanent address from profile table data
  getFormattedPermanentAddress(): string {
    console.log('Formatting permanent address from profile data:', this.selectedEmployee?.profile);

    if (!this.selectedEmployee?.profile) {
      console.log('No profile data available, using fallback address');
      return this.selectedEmployee?.permanentAddress || 'Not provided';
    }

    const profile = this.selectedEmployee.profile;

    // Use profile table address fields (prioritize profile table data)
    const addressParts = [
      profile.permanentDoorNo,
      profile.permanentBuildingName,
      profile.permanentStreetAddress,
      profile.permanentCity,
      profile.permanentPincode
    ].filter(part => part && part.trim() !== '' && part.trim() !== 'null');

    if (addressParts.length > 0) {
      const formattedAddress = addressParts.join(', ');
      console.log('Formatted permanent address:', formattedAddress);
      return formattedAddress;
    }

    // Fallback to legacy permanentaddress field if structured fields are empty
    if (profile.permanentaddress && profile.permanentaddress.trim() !== '') {
      console.log('Using legacy permanentaddress field:', profile.permanentaddress);
      return profile.permanentaddress;
    }

    // Final fallback to employee table permanentAddress
    return this.selectedEmployee.permanentAddress || 'Not provided';
  }

  // Get nominee photo URL
  // getNomineePhotoUrl(nomineeIndex: number): string | null {
  //   if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
  //     return null;
  //   }

  //   const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

  //   // Check if nominee has a photo field
  //   if ((nominee as any).nomineePhoto && (nominee as any).nomineePhoto !== 'pending_upload') {
  //     const baseUrl = 'http://localhost:8082';
  //     const photoPath = (nominee as any).nomineePhoto;

  //     console.log(`Nominee ${nomineeIndex + 1} photo path:`, photoPath);

  //     // Handle different path formats
  //     if (photoPath.startsWith('http')) {
  //       // Already a full URL
  //       return photoPath;
  //     } else if (photoPath.startsWith('/api/files/')) {
  //       // Path like: /api/files/employee_9/echarts.png
  //       return `${baseUrl}${photoPath}`;
  //     } else if (photoPath.startsWith('/files/')) {
  //       // Path like: /files/employee_9/echarts.png
  //       return `${baseUrl}/api${photoPath}`;
  //     } else if (photoPath.startsWith('api/files/')) {
  //       // Path like: api/files/employee_9/echarts.png
  //       return `${baseUrl}/${photoPath}`;
  //     } else {
  //       // Assume it's a relative path that needs /api/files/ prefix
  //       return `${baseUrl}/api/files/${photoPath}`;
  //     }
  //   }

  //   return null;
  // }
  getNomineePhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      console.log(`No nominee found at index ${nomineeIndex}`);
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // Check if nominee already has a processed photo URL
    if ((nominee as any).nomineePhotoUrl) {
      console.log(`Nominee ${nomineeIndex + 1} already has processed URL:`, (nominee as any).nomineePhotoUrl);
      return (nominee as any).nomineePhotoUrl;
    }

    // Check if nominee has a photo field
    if ((nominee as any).nomineePhoto &&
        (nominee as any).nomineePhoto !== 'pending_upload' &&
        (nominee as any).nomineePhoto.trim() !== '') {

      const baseUrl = 'http://localhost:8082';
      const photoPath = (nominee as any).nomineePhoto;

      console.log(`Nominee ${nomineeIndex + 1} raw photo path:`, photoPath);

      // Use the same logic as profile photo (exactly matching profile photo logic)
      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      console.log(`Nominee ${nomineeIndex + 1} constructed full photo URL:`, fullPhotoUrl);

      // Store the processed URL for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      (nominee as any).photoLoadingComplete = true;
      return fullPhotoUrl;
    }

    console.log(`Nominee ${nomineeIndex + 1} has no valid photo path, trying document service...`);
    // If no photo path in nominee data, try to load from document service
    this.loadNomineePhotoFromDocuments(nomineeIndex);
    return null;
  }


  // Load nominee photo from document service
  loadNomineePhotoFromDocuments(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const employeeId = this.selectedEmployee.id || this.selectedEmployee.empId;

    console.log(`Loading nominee ${nomineeIndex + 1} photo from document service for employee ID:`, employeeId);

    // Try to get nominee photo from document service
    // The API endpoint might be different for nominee photos, adjust as needed
    this.http.get(`http://localhost:8082/document/nominee-photo/${employeeId}/${nomineeIndex}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`Nominee ${nomineeIndex + 1} photo loaded successfully from document service`);
          // Create blob URL for the image
          const photoUrl = window.URL.createObjectURL(blob);
          (nominee as any).nomineePhotoUrl = photoUrl;
          (nominee as any).photoLoadingComplete = true;
        },
        error: (error) => {
          console.log(`No nominee ${nomineeIndex + 1} photo found in document service or error loading photo:`, error);
          // Try alternative endpoint or set to null
          this.tryAlternativeNomineePhotoEndpoint(nomineeIndex);
        }
      });
  }

  // Try alternative nominee photo endpoint
  tryAlternativeNomineePhotoEndpoint(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const employeeId = this.selectedEmployee.id || this.selectedEmployee.empId;

    // Try alternative endpoint pattern
    this.http.get(`http://localhost:8082/document/photo/nominee/${employeeId}/${nomineeIndex}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`Nominee ${nomineeIndex + 1} photo loaded from alternative endpoint`);
          const photoUrl = window.URL.createObjectURL(blob);
          (nominee as any).nomineePhotoUrl = photoUrl;
          (nominee as any).photoLoadingComplete = true;
        },
        error: (error) => {
          console.log(`No nominee ${nomineeIndex + 1} photo found in alternative endpoint:`, error);
          // Set to null if no photo is found anywhere
          (nominee as any).nomineePhotoUrl = null;
          (nominee as any).photoLoadingComplete = true;
        }
      });
  }

  // Handle nominee image error
  onNomineeImageError(event: any, nomineeIndex: number): void {
    console.log(`Error loading nominee ${nomineeIndex + 1} photo:`, event);

    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // Try to reload from document service if the current URL failed
    if (!(nominee as any).documentServiceTried) {
      (nominee as any).documentServiceTried = true;
      this.loadNomineePhotoFromDocuments(nomineeIndex);
    } else {
      // Hide the image and mark as no photo available
      event.target.style.display = 'none';
      (nominee as any).nomineePhotoUrl = null;
      (nominee as any).photoLoadingComplete = true;
    }
  }

  // Handle nominee image load success
  onNomineeImageLoad(nomineeIndex: number): void {
    console.log(`Nominee ${nomineeIndex + 1} photo loaded successfully`);
    if (this.selectedEmployee?.nominationEntries && this.selectedEmployee.nominationEntries[nomineeIndex]) {
      const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
      (nominee as any).photoLoadingComplete = true;
    }
  }

  // Initialize nominee photo loading for all nominees
  initializeNomineePhotos(): void {
    if (!this.selectedEmployee?.nominationEntries) {
      console.log('No nomination entries found for photo initialization');
      return;
    }

    console.log('Initializing nominee photos for', this.selectedEmployee.nominationEntries.length, 'nominees');
    console.log('Nominee entries:', this.selectedEmployee.nominationEntries);

    this.selectedEmployee.nominationEntries.forEach((nominee, index) => {
      console.log(`Initializing photo for nominee ${index + 1}:`, nominee);

      // Mark as loading initially only if photo URL is not already processed
      if (!(nominee as any).nomineePhotoUrl) {
        (nominee as any).photoLoadingComplete = false;

        // Try to get photo URL - this will trigger loading if needed
        setTimeout(() => {
          const photoUrl = this.getNomineePhotoUrl(index);
          console.log(`Photo URL result for nominee ${index + 1}:`, photoUrl);
        }, 50 * index); // Stagger the requests slightly
      } else {
        console.log(`Nominee ${index + 1} already has photo URL:`, (nominee as any).nomineePhotoUrl);
        (nominee as any).photoLoadingComplete = true;
      }
    });
  }

  // Simple method to get nominee photo URL for template use
  getNomineeDisplayPhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // If already processed, return it
    if ((nominee as any).nomineePhotoUrl) {
      return (nominee as any).nomineePhotoUrl;
    }

    // If has raw photo path, construct URL immediately
    if (nominee.nomineePhoto && nominee.nomineePhoto !== 'pending_upload' && nominee.nomineePhoto.trim() !== '') {
      const baseUrl = 'http://localhost:8082';
      const photoPath = nominee.nomineePhoto;

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      // Store for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      return fullPhotoUrl;
    }

    return null;
  }

  // View nominee photo in larger size
  viewNomineePhoto(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const photoUrl = (nominee as any).nomineePhotoUrl || this.getNomineeDisplayPhotoUrl(nomineeIndex);

    if (photoUrl) {
      // Open photo in new window/tab
      window.open(photoUrl, '_blank');
    } else {
      console.log('No photo available for nominee', nomineeIndex + 1);
    }
  }

  deleteEmployee(employee: Employee): void {
    Swal.fire({
      title: 'Delete Employee',
      text: `Are you sure you want to delete ${employee.employeeName}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Delete!',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        const employeeId = employee.id || employee.empId;

        // Show loading
        Swal.fire({
          title: 'Deleting...',
          text: 'Please wait while we delete the employee record',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        // Try to delete from API first
        this.http.delete(`http://localhost:8082/employee/deleteEmp/${employeeId}`)
          .subscribe({
            next: (response) => {
              console.log('Employee deleted successfully from API:', response);

              Swal.fire({
                title: 'Deleted!',
                text: `${employee.employeeName} has been deleted successfully!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              }).then(() => {
                this.loadEmployees(); // Refresh the list
              });
            },
            error: (error) => {
              console.error('Error deleting employee from API:', error);

              // Fallback to localStorage deletion
              this.employeeService.deleteEmployee(employee.empId);

              Swal.fire({
                title: 'Deleted!',
                text: `${employee.employeeName} has been deleted from local storage!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              }).then(() => {
                this.loadEmployees(); // Refresh the list
              });
            }
          });
      }
    });
  }

  // Navigation methods
  addNewEmployee(): void {
    this.router.navigate(['/dashboard']);
  }

  refreshData(): void {
    console.log('Refreshing employee data from API...');
    this.loadEmployees();
    this.searchTerm = ''; // Clear search term
  }

  private mapCompleteEmployeeData(apiData: any): Employee {
    return {
      // Basic Information
      id: apiData.id || apiData.employeeId || apiData.ecpfNumber || '',
      empId: apiData.ecpfNumber || apiData.empId || '',
      ecpfNumber: apiData.ecpfNumber || '',
      employeeName: apiData.employeeName || '',
      designation: apiData.currentDesignation || apiData.designation || '',
      Authority: apiData.section || '',
      district: apiData.district || '',
      location: apiData.nativePlaceAndTaluk || '',
      fatherName: apiData.fatherName || apiData.profile?.fatherName || '',
      motherName: apiData.motherName || apiData.profile?.motherName || '',
      dateOfBirth: apiData.dateOfBirth || apiData.profile?.dateOfBirth || '',
      religion: apiData.religion || '',
      community: apiData.community || apiData.profile?.community || '',
      createdAt: apiData.createdAt || '',
      caste: apiData.caste || apiData.profile?.caste || '',
      personalIdentificationMarks: (apiData.personalIdentificationmark1 || '') +
        (apiData.personalIdentificationmark2 ? ', ' + apiData.personalIdentificationmark2 : ''),
      dateOfEntryIntoService: apiData.dateOfEntry || '',
      educationalQualification: this.formatEducationQualifications(apiData.educationQualifications),

      // Profile object (including profile photo and address data)
      profile: {
        ...apiData.profile,
        // Ensure all address fields are mapped
        presentDoorNo: apiData.profile?.presentDoorNo || '',
        presentBuildingName: apiData.profile?.presentBuildingName || '',
        presentStreetAddress: apiData.profile?.presentStreetAddress || '',
        presentCity: apiData.profile?.presentCity || '',
        presentPincode: apiData.profile?.presentPincode || '',
        permanentDoorNo: apiData.profile?.permanentDoorNo || '',
        permanentBuildingName: apiData.profile?.permanentBuildingName || '',
        permanentStreetAddress: apiData.profile?.permanentStreetAddress || '',
        permanentCity: apiData.profile?.permanentCity || '',
        permanentPincode: apiData.profile?.permanentPincode || '',
        profilephoto: apiData.profile?.profilephoto || apiData.profile?.profilePhoto || apiData.profilePhoto || '',
        profilePhoto: apiData.profile?.profilePhoto || apiData.profile?.profilephoto || apiData.profilePhoto || '',
        mobileNumber: apiData.profile?.mobileNumber || apiData.mobileNumber || ''
      },

      // Missing required fields
      status: apiData.status || '',
      dateOfEntry: apiData.dateOfEntry || '',
      educationEnties: this.mapEducationEntries(apiData.educationQualifications || []),

      // Contact Information
      email: apiData.email || apiData.profile?.email || '',
      gender: apiData.gender || apiData.profile?.gender || '',
      mobileNumber: apiData.mobileNumber || apiData.profile?.mobileNumber || '',
      presentAddress: apiData.presentAddress || apiData.profile?.presentaddress || '',
      permanentAddress: apiData.permanentAddress || apiData.profile?.permanentaddress || '',

      // Account Details
      bankAccountNo: apiData.accountDetails?.bankaccountnumber || '',
      ifscCode: apiData.accountDetails?.ifsccode || '',
      bankName: apiData.accountDetails?.bankname || '',
      panNumber: apiData.panNumber || '',
      uanNumber: apiData.accountDetails?.uannumber || '',
      aadharNumber: apiData.accountDetails?.aadharnumber || '',

      // Service Records
      serviceRecords: apiData.serviceHistory || [],
      leaveEntries: this.mapLeaveBalances(apiData.leaveBalances || []),

      // Additional Information
      registerNumber: apiData.registerNumber || '',
      personalIdMark1: apiData.personalIdentificationmark1 || '',
      personalIdMark2: apiData.personalIdentificationmark2 || '',
      nativeAndTaluk: apiData.nativePlaceAndTaluk || '',

      // Arrays
      serviceEntries: this.mapServiceHistory(apiData.serviceHistory || []),
      trainingEntries: this.mapTrainingDetails(apiData.trainingDetails || []),
      punishmentEntries: this.mapPunishmentDetails(apiData.punishmentDetails || []),
      nominationEntries: this.mapNominees(apiData.nominees || []),
      technicalQualificationEntries: this.mapTechnicalQualifications(apiData.technicalQualifications || []),

      // Salary Details
      salaryDetails: {
        lastSalaryRevisedDate: apiData.salaryDetails?.lastSalaryRevisedDate || '',
        group: apiData.salaryDetails?.group || apiData.group || '',
        payband: apiData.salaryDetails?.payband || apiData.payband || '',
        gradepay: apiData.salaryDetails?.gradepay || apiData.gradepay || ''
      },

      remarks: apiData.remarks || '',
      rejectedBy: apiData.rejectedBy || ''
    };
  }

  private formatEducationQualifications(educationQualifications: any[]): string {
    if (!educationQualifications || educationQualifications.length === 0) {
      return 'Not specified';
    }
    return educationQualifications.map(edu => edu.qualification).join(', ');
  }

  private mapLeaveBalances(leaveBalances: any[]): any[] {
    return leaveBalances.map(leave => ({
      leaveType: leave.leaveType || '',
      leaveBalanceCount: leave.openingBalance || leave.closingBalance || 0,
      openingBalance: leave.openingBalance || 0,
      closingBalance: leave.closingBalance || 0,
      entryDate: leave.entryDate || ''
    }));
  }

  private mapServiceHistory(serviceHistory: any[]): any[] {
    return serviceHistory.map(service => ({
      date: service.date || service.dateofappointment || service.joiningdate || service.fromdate || service.promoteddate || service.punishmentdate || '',
      type: service.type || '',
      status: service.status || 'Active',

      // Appointment fields
      appointmentType: service.appointmenttype || '',
      modeOfAppointment: service.modeofappointment || '',
      dateOfAppointment: service.dateofappointment || '',
      proceedingOrderNo: service.proceedingorderno || '',
      proceedingOrderDate: service.proceedingorderdate || '',

      // Promotion fields
      joiningDate: service.joiningdate || '',
      fromDesignation: service.fromdesignation || '',
      toPromoted: service.topromoted || '',
      promotedDate: service.promoteddate || '',

      // Transfer fields
      fromDate: service.fromdate || '',
      toDate: service.todate || '',
      fromPlace: service.fromplace || '',
      toPlace: service.toplace || '',

      // Increment fields
      typeOfIncrement: service.typeofincrement || '',
      incrementType: service.incrementtype || '',

      // Deputation fields
      designation: service.designation || '',
      originalDesignation: service.originaldesignation || '',
      parentDepartment: service.parentdepartment || '',

      // Punishment fields
      punishmentType: service.punishmenttype || '',
      punishmentDate: service.punishmentdate || '',
      caseDetails: service.casedetails || '',
      caseNumber: service.caseNumber || '',
      caseDate: service.caseDate || '',
      personInvolved: service.personInvolved || '',
      presentStatus: service.presentStatus || '',
      description: service.description || '',
      involvedPersonsWithNames: service.involvedPersonsWithNames || [],

    }));
  }

  private mapTrainingDetails(trainingDetails: any[]): any[] {
    return trainingDetails.map(training => ({
      trainingType: training.trainingtype || '',
      trainingDate: training.date || ''
    }));
  }

  private mapPunishmentDetails(punishmentDetails: any[]): any[] {
    return punishmentDetails.map(punishment => ({
      punishmentType: punishment.punishmenttype || '',
      punishmentDate: punishment.date || '',
      caseDetails: punishment.casedetails || '',
      involvedPersonsWithNames: punishment.involvedPersonsWithNames || []
    }));
  }

  private mapNominees(nominees: any[]): any[] {
    console.log('Mapping nominees data:', nominees);
    return nominees.map((nominee, index) => {
      console.log(`Nominee ${index + 1} data:`, nominee);
      console.log(`Nominee ${index + 1} photo path:`, nominee.nomineePhoto);

      const mappedNominee = {
        nomineeName: nominee.nomineename || '',
        address: nominee.address || '',
        relationship: nominee.relationship || '',
        age: nominee.age || 0,
        percentageOfShare: nominee.percentageofshare || 0,
        gender: nominee.gender || '',
        nomineePhoto: nominee.nomineePhoto || null
      };

      // Always process photo URL if nominee has a photo path
      if (nominee.nomineePhoto && nominee.nomineePhoto !== 'pending_upload' && nominee.nomineePhoto.trim() !== '') {
        const baseUrl = 'http://localhost:8082';
        const photoPath = nominee.nomineePhoto;

        console.log(`Processing nominee ${index + 1} photo path:`, photoPath);

        // Use the same logic as profile photo URL construction
        let fullPhotoUrl: string;
        if (photoPath.startsWith('http')) {
          fullPhotoUrl = photoPath;
        } else if (photoPath.startsWith('/api/')) {
          fullPhotoUrl = `${baseUrl}${photoPath}`;
        } else {
          // If path doesn't start with /api/, add it
          fullPhotoUrl = `${baseUrl}${photoPath}`;
        }

        console.log(`Nominee ${index + 1} constructed full photo URL:`, fullPhotoUrl);
        (mappedNominee as any).nomineePhotoUrl = fullPhotoUrl;
        (mappedNominee as any).photoLoadingComplete = true;

        // Also store the raw photo path for debugging
        (mappedNominee as any).rawPhotoPath = photoPath;
      } else {
        console.log(`Nominee ${index + 1} has no photo or invalid photo path:`, nominee.nomineePhoto);
        (mappedNominee as any).nomineePhotoUrl = null;
        (mappedNominee as any).photoLoadingComplete = true;
        (mappedNominee as any).rawPhotoPath = nominee.nomineePhoto || 'No photo';
      }

      return mappedNominee;
    });
  }

  private mapEducationEntries(educationQualifications: any[]): any[] {
    return educationQualifications.map(education => ({
      qualification: education.qualification || '',
      courseName: education.coursename || '',
      instituteName: education.schoolname || education.collegename || education.universityname || '',
      universityName: education.universityname || '',
      specialization: education.specialization || ''
    }));
  }

  private mapTechnicalQualifications(technicalQualifications: any[]): any[] {
    return technicalQualifications.map(technical => ({
      technicalType: technical.technicalType || '',
      remarks: technical.remarks || '',
      grade: technical.grade || ''
    }));
  }

  // PDF related methods
  viewPDFs(employee: Employee): void {
    this.selectedEmployee = employee;
    console.log('Fetching documents for employee ID:', employee.id);

    if (!employee.id) {
      console.error('No employee ID found for document fetch');
      this.selectedEmployeePDFs = [];
      return;
    }

    // Call API to get employee documents
    this.http.get<any[]>(`http://localhost:8082/documents/employee/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Documents received:', response);
          this.selectedEmployeePDFs = this.mapDocumentsToFiles(response);
        },
        error: (error) => {
          console.error('Error fetching documents:', error);
          // Fallback to empty array if API fails
          this.selectedEmployeePDFs = [];
          alert('Could not load documents for this employee.');
        }
      });
  }

  private mapDocumentsToFiles(documents: any[]): any[] {
    // Filter to keep only PDF files, excluding photos and signatures
    const pdfDocuments = documents.filter(doc => {
      const fileName = (doc.fileName || '').toLowerCase();
      const fileType = (doc.fileType || '').toLowerCase();

      // Check if it's a PDF file
      const isPdf = fileType === 'application/pdf' || fileName.endsWith('.pdf');

      // Exclude photo and signature files (even if they are PDFs)
      const photoSignatureKeywords = [
        'photo', 'signature', 'sign', 'pic', 'image', 'img', 'passport',
        'profile', 'headshot', 'mugshot', 'selfie', 'avatar', 'thumb',
        'thumbnail', 'jpeg', 'jpg', 'png', 'gif', 'bmp', 'tiff'
      ];

      const isPhotoOrSignature = photoSignatureKeywords.some(keyword =>
        fileName.includes(keyword)
      );

      // Also check if the file type is an image type
      const isImageType = fileType.startsWith('image/');

      return isPdf && !isPhotoOrSignature && !isImageType;
    });

    return pdfDocuments.map(doc => ({
      id: doc.id,
      employeeId: doc.employeeId,
      employeeName: doc.employeeName,
      name: doc.fileName,
      type: doc.fileType,
      size: doc.fileSize,
      filePath: doc.filePath,
      fileUrl: doc.fileUrl,
      uploadDate: doc.uploadDate,
      downloadUrl: `http://localhost:8082${doc.fileUrl}`
    }));
  }

  private generateSamplePDFs(empId: string): File[] {
    // Generate sample PDF files for demonstration
    const samplePDFs = [
      new File([''], `${empId}_Resume.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_Certificate.pdf`, { type: 'application/pdf' }),
      new File([''], `${empId}_ID_Proof.pdf`, { type: 'application/pdf' })
    ];

    // Add some realistic file sizes
    Object.defineProperty(samplePDFs[0], 'size', { value: 245760 }); // 240 KB
    Object.defineProperty(samplePDFs[1], 'size', { value: 512000 }); // 500 KB
    Object.defineProperty(samplePDFs[2], 'size', { value: 1048576 }); // 1 MB

    return samplePDFs;
  }

  openPDF(file: any): void {
    console.log('Opening PDF:', file);

    if (file.downloadUrl) {
      // Open the actual PDF file from the server
      window.open(file.downloadUrl, '_blank');
    } else {
      // Fallback for old File objects
      alert(`Opening PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would open the PDF file.`);
    }
  }

  downloadPDF(file: any): void {
    console.log('Downloading PDF:', file);

    if (file.downloadUrl) {
      // Add file to downloading set
      this.downloadingFiles.add(file.id);

      // Use HttpClient to download the file as blob
      this.http.get(file.downloadUrl, { responseType: 'blob' })
        .subscribe({
          next: (blob) => {
            // Create blob URL and force download
            const blobUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = file.name;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL
            setTimeout(() => {
              window.URL.revokeObjectURL(blobUrl);
            }, 100);

            // Remove from downloading set
            this.downloadingFiles.delete(file.id);
            console.log('File downloaded successfully:', file.name);
          },
          error: (error) => {
            console.error('Error downloading file:', error);
            // Remove from downloading set
            this.downloadingFiles.delete(file.id);
            alert('Error downloading file. Please try again.');
          }
        });
    } else {
      // Fallback for old File objects
      alert(`Downloading PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would download the PDF file.`);
    }
  }

  isDownloading(fileId: number): boolean {
    return this.downloadingFiles.has(fileId);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Export employee data to PDF
  async exportToPDF(): Promise<void> {
    if (!this.selectedEmployee) {
      alert('No employee data to export');
      return;
    }

    try {
      // Get logo base64 first
      const logoBase64 = await this.getLogoBase64();

      // Create PDF content as HTML string with logo
      const pdfContent = this.generatePDFContent(logoBase64);

      // Directly download as HTML file
      this.downloadAsHTML(pdfContent);

    } catch (error) {
      console.error('Error exporting to PDF:', error);
      alert('Error exporting to PDF. Please try again.');
    }
  }

  private generatePDFContent(logoBase64?: string): string {
    const employee = this.selectedEmployee!;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Employee Details - ${employee.employeeName}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          line-height: 1.5;
          color: #333;
          font-size: 16px;
        }
        .header {
          text-align: center;
          margin-bottom: 25px;
          border-bottom: 2px solid #007bff;
          padding-bottom: 15px;
        }
        .header-row {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          position: relative;
        }
        .header-logo-section {
          flex: 0 0 auto;
          margin-right: 20px;
        }
        .header-logo {
          height: 120px;
          width: auto;
          max-width: 450px;
          object-fit: contain;
        }
        .header-content-section {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          white-space: nowrap;
        }
        .header h1 {
          font-size: 24px;
          margin: 8px 0;
          color: #007bff;
        }
        .header h2 {
          font-size: 20px;
          margin: 6px 0;
          color: #333;
        }
        .header p {
          font-size: 14px;
          margin: 4px 0;
          color: #666;
        }
        .section {
          margin-bottom: 20px;
          page-break-inside: avoid;
        }
        .section-title {
          background-color: #007bff;
          color: white;
          padding: 8px 12px;
          margin-bottom: 12px;
          font-weight: bold;
          font-size: 16px;
        }
        .row {
          display: flex;
          margin-bottom: 10px;
          align-items: flex-start;
        }
        .col {
          flex: 1;
          padding-right: 15px;
        }
        .photo-container {
          text-align: center;
          margin-top: 10px;
        }
        .photo-frame {
          border: 2px solid #007bff;
          padding: 5px;
          display: inline-block;
          background: white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .profile-photo {
          width: 120px;
          height: 150px;
          object-fit: cover;
          display: block;
        }
        .no-photo {
          width: 120px;
          height: 150px;
          background: #f8f9fa;
          border: 1px dashed #ccc;
          text-align: center;
          line-height: 150px;
          color: #666;
          font-size: 12px;
        }
        .label {
          font-weight: bold;
          color: #555;
          min-width: 150px;
          display: inline-block;
          font-size: 15px;
        }
        .value {
          color: #333;
          font-size: 15px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 10px;
          font-size: 14px;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px 10px;
          text-align: left;
        }
        th {
          background-color: #f8f9fa;
          font-weight: bold;
          font-size: 14px;
        }
        .no-data {
          color: #888;
          font-style: italic;
          font-size: 14px;
        }
        @media print {
          body { margin: 0; font-size: 15px; }
          .section { page-break-inside: avoid; }
          table { font-size: 13px; }
          th, td { padding: 6px 8px; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="header-row">
          <div class="header-logo-section">
            ${logoBase64 ? `<img src="${logoBase64}" alt="TNCSC Logo" class="header-logo" />` : ''}
          </div>
          <div class="header-content-section">
            <h1>Employee Details Report</h1>
            <h2>${employee.employeeName} (${employee.ecpfNumber})</h2>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Personal Information</div>
        <div class="row">
          <div class="col" style="flex: 2;">
            <div><span class="label">ECPF Number:</span> <span class="value">${employee.ecpfNumber || 'N/A'}</span></div>
            <div><span class="label">Employee Name:</span> <span class="value">${employee.employeeName || 'N/A'}</span></div>
            <div><span class="label">Father Name:</span> <span class="value">${employee.fatherName || 'N/A'}</span></div>
            <div><span class="label">Mother Name:</span> <span class="value">${employee.motherName || 'N/A'}</span></div>
            <div><span class="label">Date of Birth:</span> <span class="value">${employee.dateOfBirth ? new Date(employee.dateOfBirth).toLocaleDateString('en-IN') : 'N/A'}</span></div>
            <div><span class="label">Religion:</span> <span class="value">${employee.religion || 'N/A'}</span></div>
            <div><span class="label">Community:</span> <span class="value">${employee.community || 'N/A'}</span></div>
            <div><span class="label">Caste:</span> <span class="value">${employee.caste || 'N/A'}</span></div>
            <div><span class="label">Designation:</span> <span class="value">${employee.designation || 'N/A'}</span></div>
            <div><span class="label">District:</span> <span class="value">${employee.district || 'N/A'}</span></div>
            <div><span class="label">Date of Entry:</span> <span class="value">${employee.dateOfEntryIntoService ? new Date(employee.dateOfEntryIntoService).toLocaleDateString('en-IN') : 'N/A'}</span></div>
            <div><span class="label">Date of Operator Entry:</span> <span class="value">${employee.createdAt ? new Date(employee.createdAt).toLocaleDateString('en-IN') + ' ' + new Date(employee.createdAt).toLocaleTimeString('en-IN') : 'N/A'}</span></div>
            <div><span class="label">ID Marks:</span> <span class="value">${employee.personalIdentificationMarks || 'N/A'}</span></div>
          </div>
          <div class="col" style="flex: 1; text-align: center;">
            <div style="margin-bottom: 10px; font-weight: bold; color: #007bff;">Profile Photo</div>
            ${this.getProfilePhotoHTML(employee)}
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Contact Information</div>
        <div><span class="label">Email:</span> <span class="value">${employee.email || 'N/A'}</span></div>
        <div><span class="label">Gender:</span> <span class="value">${employee.gender || employee.profile?.gender || 'N/A'}</span></div>
        <div><span class="label">Mobile Number:</span> <span class="value">${employee.profile?.mobileNumber || employee.mobileNumber || 'N/A'}</span></div>
        <div><span class="label">Present Address:</span> <span class="value">${this.formatAddressForPDF(employee, 'present')}</span></div>
        <div><span class="label">Permanent Address:</span> <span class="value">${this.formatAddressForPDF(employee, 'permanent')}</span></div>
      </div>

      <div class="section">
        <div class="section-title">Account Details</div>
        <div class="row">
          <div class="col">
            <div><span class="label">Bank Account:</span> <span class="value">${employee.bankAccountNo || 'N/A'}</span></div>
            <div><span class="label">IFSC Code:</span> <span class="value">${employee.ifscCode || 'N/A'}</span></div>
            <div><span class="label">Bank Name:</span> <span class="value">${employee.bankName || 'N/A'}</span></div>
          </div>
          <div class="col">
            <div><span class="label">PAN Number:</span> <span class="value">${employee.panNumber || 'N/A'}</span></div>
            <div><span class="label">UAN Number:</span> <span class="value">${employee.uanNumber || 'N/A'}</span></div>
            <div><span class="label">Aadhar Number:</span> <span class="value">${employee.aadharNumber || 'N/A'}</span></div>
          </div>
        </div>
      </div>

      ${this.generateServiceHistoryHTML(employee)}
      ${this.generateLeaveBalanceHTML(employee)}
      ${this.generateEducationHTML(employee)}
      ${this.generateNominationHTML(employee)}

    </body>
    </html>`;
  }

  private generateServiceHistoryHTML(employee: any): string {
    if (!employee.serviceEntries || employee.serviceEntries.length === 0) {
      return '';
    }

    let html = `
    <div class="section">
      <div class="section-title">Service History</div>
      <table>
        <thead>
          <tr>
            <th>Type</th>
            <th>Date</th>
            <th>Details</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>`;

    employee.serviceEntries.forEach((service: any) => {
      const date = service.date || service.dateOfAppointment || service.joiningDate || service.promotedDate || 'N/A';
      const details = this.getServiceDetails(service);

      html += `
        <tr>
          <td>${service.type || 'N/A'}</td>
          <td>${date ? new Date(date).toLocaleDateString('en-IN') : 'N/A'}</td>
          <td>${details}</td>
          <td>${service.status || 'N/A'}</td>
        </tr>`;
    });

    html += `
        </tbody>
      </table>
    </div>`;

    return html;
  }

  private getServiceDetails(service: any): string {
    switch (service.type) {
      case 'Appointment':
        return `${service.appointmentType || ''} - ${service.modeOfAppointment || ''}`;
      case 'Promotion':
        return `From: ${service.fromDesignation || 'N/A'} To: ${service.toPromoted || 'N/A'}`;
      case 'Transfer':
        return `From: ${service.fromPlace || 'N/A'} To: ${service.toPlace || 'N/A'}`;
      case 'Increment':
        return `Type: ${service.typeOfIncrement || 'N/A'}`;
      case 'Deputation':
        return `${service.designation || 'N/A'} - ${service.parentDepartment || 'N/A'}`;
      case 'Punishment':
        return `${service.punishmentType || 'N/A'} - ${service.caseDetails || 'N/A'}`;
      default:
        return service.designation || 'N/A';
    }
  }

  private generateLeaveBalanceHTML(employee: any): string {
    if (!employee.leaveEntries || employee.leaveEntries.length === 0) {
      return '';
    }

    let html = `
    <div class="section">
      <div class="section-title">Leave Balance</div>
      <table>
        <thead>
          <tr>
            <th>Leave Type</th>
            <th>Opening Balance</th>
            <th>Closing Balance</th>
            <th>Entry Date</th>
          </tr>
        </thead>
        <tbody>`;

    employee.leaveEntries.forEach((leave: any) => {
      html += `
        <tr>
          <td>${leave.leaveType || 'N/A'}</td>
          <td>${leave.openingBalance || 0}</td>
          <td>${leave.closingBalance || 0}</td>
          <td>${leave.entryDate ? new Date(leave.entryDate).toLocaleDateString('en-IN') : 'N/A'}</td>
        </tr>`;
    });

    html += `
        </tbody>
      </table>
    </div>`;

    return html;
  }

  private generateEducationHTML(employee: any): string {
    if (!employee.educationEnties || employee.educationEnties.length === 0) {
      return '';
    }

    let html = `
    <div class="section">
      <div class="section-title">Educational Qualifications</div>
      <table>
        <thead>
          <tr>
            <th>Qualification</th>
            <th>Institution</th>
            <th>Course</th>
            <th>University</th>
            <th>Specialization</th>
          </tr>
        </thead>
        <tbody>`;

    employee.educationEnties.forEach((education: any) => {
      html += `
        <tr>
          <td>${education.qualification || 'N/A'}</td>
          <td>${education.instituteName || 'N/A'}</td>
          <td>${education.courseName || 'N/A'}</td>
          <td>${education.universityName || 'N/A'}</td>
          <td>${education.specialization || 'N/A'}</td>
        </tr>`;
    });

    html += `
        </tbody>
      </table>
    </div>`;

    return html;
  }

  private generateNominationHTML(employee: any): string {
    if (!employee.nominationEntries || employee.nominationEntries.length === 0) {
      return '';
    }

    let html = `
    <div class="section">
      <div class="section-title">Nomination Details</div>
      <table>
        <thead>
          <tr>
            <th>Nominee Name</th>
            <th>Relationship</th>
            <th>Age</th>
            <th>Share %</th>
            <th>Gender</th>
          </tr>
        </thead>
        <tbody>`;

    employee.nominationEntries.forEach((nominee: any) => {
      html += `
        <tr>
          <td>${nominee.nomineeName || 'N/A'}</td>
          <td>${nominee.relationship || 'N/A'}</td>
          <td>${nominee.age || 'N/A'}</td>
          <td>${nominee.percentageOfShare || 0}%</td>
          <td>${nominee.gender || 'N/A'}</td>
        </tr>`;
    });

    html += `
        </tbody>
      </table>
    </div>`;

    return html;
  }

  private formatAddressForPDF(employee: any, type: 'present' | 'permanent'): string {
    if (!employee?.profile) {
      return employee?.[`${type}Address`] || 'Not provided';
    }

    const profile = employee.profile;

    // Get address parts based on type
    const addressParts = type === 'present'
      ? [
          profile.presentDoorNo,
          profile.presentBuildingName,
          profile.presentStreetAddress,
          profile.presentCity,
          profile.presentPincode
        ]
      : [
          profile.permanentDoorNo,
          profile.permanentBuildingName,
          profile.permanentStreetAddress,
          profile.permanentCity,
          profile.permanentPincode
        ];

    // Filter out empty/null values
    const filteredParts = addressParts.filter(part => part && part.trim() !== '' && part.trim() !== 'null');

    if (filteredParts.length > 0) {
      return filteredParts.join(', ');
    }

    // Fallback to legacy address field
    const legacyField = type === 'present' ? profile.presentaddress : profile.permanentaddress;
    if (legacyField && legacyField.trim() !== '') {
      return legacyField;
    }

    // Final fallback to employee table address
    return employee[`${type}Address`] || 'Not provided';
  }

  private async getLogoBase64(): Promise<string> {
    return new Promise<string>((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        const base64 = canvas.toDataURL('image/png');
        resolve(base64);
      };
      img.onerror = () => {
        resolve(''); // Return empty string if logo fails to load
      };
      img.src = 'logo_tncsc.png';
    });
  }

  private getProfilePhotoHTML(employee: any): string {
    // Check if profile photo URL exists (this is the full URL constructed in the component)
    if (employee.profilePhotoUrl) {
      return `
        <div style="border: 2px solid #007bff; padding: 3px; display: inline-block; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <img src="${employee.profilePhotoUrl}"
               alt="Profile Photo"
               style="width: 100px; height: 120px; object-fit: cover; display: block;"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
          <div style="display: none; width: 100px; height: 120px; background: #f8f9fa; border: 1px dashed #ccc;
                      text-align: center; line-height: 120px; color: #666; font-size: 9px;">
            Photo not available
          </div>
        </div>`;
    }
    // Check if profile photo path exists in profile object
    else if (employee.profile?.profilephoto || employee.profile?.profilePhoto) {
      const photoPath = employee.profile.profilephoto || employee.profile.profilePhoto;
      const baseUrl = 'http://localhost:8082';

      // Construct full URL
      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      return `
        <div style="border: 2px solid #007bff; padding: 3px; display: inline-block; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <img src="${fullPhotoUrl}"
               alt="Profile Photo"
               style="width: 100px; height: 120px; object-fit: cover; display: block;"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
          <div style="display: none; width: 100px; height: 120px; background: #f8f9fa; border: 1px dashed #ccc;
                      text-align: center; line-height: 120px; color: #666; font-size: 9px;">
            Photo not available
          </div>
        </div>`;
    } else {
      return `
        <div style="border: 2px solid #ccc; padding: 3px; display: inline-block; background: #f8f9fa;">
          <div style="width: 100px; height: 120px; background: #f8f9fa; border: 1px dashed #ccc;
                      text-align: center; line-height: 120px; color: #666; font-size: 9px;">
            No Photo Available
          </div>
        </div>`;
    }
  }

  private downloadAsHTML(content: string): void {
    const blob = new Blob([content], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Employee_${this.selectedEmployee?.employeeName || 'Details'}_${new Date().toISOString().split('T')[0]}.html`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  approveEmployee(employee: Employee): void {
    Swal.fire({
      title: 'Approve Employee',
      text: `Are you sure you want to approve ${employee.employeeName}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Approve!',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        const payload = {
          approvedBy: localStorage.getItem('employeeName') || localStorage.getItem('username') || 'System',
          remarks: 'Approved by employee'
        };

        // Show loading
        Swal.fire({
          title: 'Processing...',
          text: 'Approving employee record',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        this.http.put(`http://localhost:8082/employee-status/approve/${employee.id}`, payload)
          .subscribe({
            next: (response) => {
              console.log('Employee approved successfully:', response);

              Swal.fire({
                title: 'Approved!',
                text: `${employee.employeeName} has been approved successfully!`,
                icon: 'success',
                confirmButtonColor: '#28a745'
              });

              // Clear stored employee data after successful approval
              if (this.canApproveReject()) {
                this.clearStoredEmployeeData();
                // Clear the current employee list since the record is now approved
                this.employees = [];
                this.filteredEmployees = [];
              }
            },
            error: (error) => {
              console.error('Error approving employee:', error);

              Swal.fire({
                title: 'Error!',
                text: `Failed to approve ${employee.employeeName}. Please try again.`,
                icon: 'error',
                confirmButtonColor: '#dc3545'
              });
            }
          });
      }
    });
  }

  rejectEmployee(employee: Employee): void {
    Swal.fire({
      title: 'Change Request',
      text: `Are you sure you want to change values for this ${employee.employeeName}?`,
      input: 'textarea',
      inputLabel: 'Change Request',
      inputPlaceholder: 'Please provide detailed remarks for Changes...',
      inputAttributes: {
        'aria-label': 'Change remarks',
        'rows': '4'
      },
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Change!',
      cancelButtonText: 'Cancel',
      inputValidator: (value) => {
        if (!value || !value.trim()) {
          return 'Please provide remarks for Change!';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const payload = {
          rejectedBy: localStorage.getItem('employeeName') || localStorage.getItem('username') || 'System',
          remarks: result.value.trim()
        };

        // Show loading
        Swal.fire({
          title: 'Processing...',
          text: 'Request sent to employee record',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        this.http.put(`http://localhost:8082/employee-status/reject/${employee.id}`, payload)
          .subscribe({
            next: (response) => {
              console.log('Employee rejected successfully:', response);

              Swal.fire({
                title: 'Change Request Sent!',
                text: `change request has been sent successfully`,
                icon: 'success',
                confirmButtonColor: '#dc3545'
              });

              // Clear stored employee data after successful rejection
              if (this.canApproveReject()) {
                this.clearStoredEmployeeData();
                // Clear the current employee list since the record is now rejected
                this.employees = [];
                this.filteredEmployees = [];
              }
            },
            error: (error) => {
              console.error('Error rejecting employee:', error);

              Swal.fire({
                title: 'Error!',
                text: `Failed to reject ${employee.employeeName}. Please try again.`,
                icon: 'error',
                confirmButtonColor: '#dc3545'
              });
            }
          });
      }
    });
  }

  // Keep this method for backward compatibility with modal (if still used)
  confirmRejectEmployee(): void {
    if (!this.selectedEmployee) return;

    if (!this.rejectionRemarks.trim()) {
      Swal.fire({
        title: 'Missing Information',
        text: 'Please provide remarks for rejection.',
        icon: 'warning',
        confirmButtonColor: '#ffc107'
      });
      return;
    }

    const payload = {
      rejectedBy: localStorage.getItem('employeeName') || localStorage.getItem('username') || 'System',
      remarks: this.rejectionRemarks
    };

    // Show loading
    Swal.fire({
      title: 'Processing...',
      text: 'Rejecting employee record',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.http.put(`http://localhost:8082/employee-status/reject/${this.selectedEmployee.id}`, payload)
      .subscribe({
        next: (response) => {
          console.log('Employee rejected successfully:', response);

          Swal.fire({
            title: 'Rejected!',
            text: `${this.selectedEmployee!.employeeName} has been rejected successfully!`,
            icon: 'success',
            confirmButtonColor: '#dc3545'
          });

          // Clear stored employee data after successful rejection
          if (this.canApproveReject()) {
            this.clearStoredEmployeeData();
            // Clear the current employee list since the record is now rejected
            this.employees = [];
            this.filteredEmployees = [];
          }

          this.closeRejectionModal();
        },
        error: (error) => {
          console.error('Error rejecting employee:', error);

          Swal.fire({
            title: 'Error!',
            text: `Failed to reject ${this.selectedEmployee!.employeeName}. Please try again.`,
            icon: 'error',
            confirmButtonColor: '#dc3545'
          });
        }
      });
  }

  closeRejectionModal(): void {
    this.isRejectionModalOpen = false;
    this.selectedEmployee = null;
    this.rejectionRemarks = '';
  }

  clearStoredEmployeeData(): void {
    localStorage.removeItem('employeeResponseData');
    localStorage.removeItem('role');
    localStorage.removeItem('username');
    localStorage.removeItem('employeeId');
    localStorage.removeItem('employeeName');
    localStorage.removeItem('token');

    // Set a flag to prevent login success message from showing again
    localStorage.setItem('skipLoginMessage', 'true');
    console.log('Cleared stored employee data from localStorage');

    // Navigate to login page after clearing data
    setTimeout(() => {
      this.router.navigate(['/login']);
    }, 2000);
  }

}
