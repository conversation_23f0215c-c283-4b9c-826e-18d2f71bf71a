<hr class="my-5">
<h3 class="text-center mb-4">Registered Users</h3>

<!-- Loading Spinner -->
<div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="min-height: 300px;">
  <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
    <span class="visually-hidden">Loading...</span>
  </div>
  <span class="ms-3 fs-5">Loading users...</span>
</div>

<!-- Table Content -->
<div *ngIf="!isLoading" class="table-responsive">
  <table class="table table-bordered table-hover text-center align-middle">
    <thead class="table-dark">
      <tr>
        <th>S.No</th>
        <th>User Name</th>
        <th>Email</th>
        <th>Phone</th>
        <th>Role</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of userList; let i = index">
        <td>{{ (currentPage - 1) * pageSize + i + 1 }}</td>
        <td>{{ user.username }}</td>
        <td>{{ user.email }}</td>
        <td>{{ user.mobile }}</td>
        <td>{{ user.role?.role || 'N/A' }}</td>
      </tr>
      <tr *ngIf="!userList || userList.length === 0">
        <td colspan="5">No users available.</td>
      </tr>
    </tbody>
  </table>
</div>

<!-- Pagination Controls -->
<div *ngIf="!isLoading && totalRecords > 0" class="row mt-4">
  <!-- Page Size Selector and Info -->
  <div class="col-md-6 d-flex align-items-center">
    <div class="text-muted me-3">{{ getCurrentPageInfo() }}</div>
    <div class="d-flex align-items-center">
      <label class="form-label me-2 mb-0 text-muted">Show:</label>
      <select class="form-select form-select-sm" style="width: auto;"
              [value]="pageSize"
              (change)="onPageSizeChange(+$event.target.value)"
              [disabled]="isLoading">
        <option value="5">5</option>
        <option value="10">10</option>
        <option value="25">25</option>
        <option value="50">50</option>
      </select>
      <span class="text-muted ms-2">per page</span>
    </div>
  </div>

  <!-- Pagination Buttons -->
  <div class="col-md-6 d-flex justify-content-end" *ngIf="totalPages > 1">
    <nav aria-label="User list pagination">
      <ul class="pagination mb-0">
        <!-- Previous Button -->
        <li class="page-item" [class.disabled]="currentPage === 1 || isLoading">
          <button class="page-link" (click)="previousPage()"
                  [disabled]="currentPage === 1 || isLoading">
            <i class="bi bi-chevron-left"></i> Previous
          </button>
        </li>

        <!-- First page if not visible -->
        <li *ngIf="getPageNumbers().length > 0 && getPageNumbers()[0] > 1" class="page-item">
          <button class="page-link" (click)="goToPage(1)" [disabled]="isLoading">1</button>
        </li>
        <li *ngIf="getPageNumbers().length > 0 && getPageNumbers()[0] > 2" class="page-item disabled">
          <span class="page-link">...</span>
        </li>

        <!-- Page Numbers -->
        <li *ngFor="let page of getPageNumbers()"
            class="page-item"
            [class.active]="page === currentPage">
          <button class="page-link" (click)="goToPage(page)" [disabled]="isLoading">{{ page }}</button>
        </li>

        <!-- Last page if not visible -->
        <li *ngIf="getPageNumbers().length > 0 && getPageNumbers()[getPageNumbers().length - 1] < totalPages - 1"
            class="page-item disabled">
          <span class="page-link">...</span>
        </li>
        <li *ngIf="getPageNumbers().length > 0 && getPageNumbers()[getPageNumbers().length - 1] < totalPages"
            class="page-item">
          <button class="page-link" (click)="goToPage(totalPages)" [disabled]="isLoading">{{ totalPages }}</button>
        </li>

        <!-- Next Button -->
        <li class="page-item" [class.disabled]="currentPage === totalPages || isLoading">
          <button class="page-link" (click)="nextPage()"
                  [disabled]="currentPage === totalPages || isLoading">
            Next <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</div>