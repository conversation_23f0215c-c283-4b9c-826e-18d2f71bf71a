import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, catchError, Observable, of, tap } from 'rxjs';
import { Employee } from '../pages/form-fill/employee.model';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  private readonly STORAGE_KEY = 'employees';
  private employeesSubject = new BehaviorSubject<Employee[]>([]);
  public employees$ = this.employeesSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadEmployees();
  }

  // Load employees from localStorage
  loadEmployees(): Employee[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      const employees = stored ? JSON.parse(stored) : [];
      this.employeesSubject.next(employees);
      return employees;
    } catch (error) {
      console.error('Error loading employees from localStorage:', error);
      return [];
    }
  }

  // Save employees to localStorage
  saveEmployees(employees: Employee[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(employees));
      this.employeesSubject.next(employees);
    } catch (error) {
      console.error('Error saving employees to localStorage:', error);
    }
  }
  approveEmployeeByUser(employee: Employee): Observable<any> {
    return this.http.put('http://localhost:8082/employee-status/approve/'+employee.id, employee).pipe(
      tap(response => {
        console.log('Employee approved by user successfully:', response);
      }),
      catchError(error => {
        console.error('Error approving employee by user:', error);
        throw error;
      })
    );
  }

  // Add a new employee
  addEmployee(employee: Employee): void {
    const employees = this.loadEmployees();
    employees.push(employee);
    this.saveEmployees(employees);
  }
  loadUserApprovalEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>('http://localhost:8082/employee-status/status/user approval').pipe(
      tap(data => {
        // Save to localStorage and update BehaviorSubject
        this.saveEmployees(data);
      }),
      // catchError(error => {
      //   console.error('Error fetching user approval employees from server:', error);
      //   // Fallback to localStorage data
      //   // const localData = this.loadEmployeesFromLocalStorage();
      //   // return of(localData.filter(emp => emp.status === 'user-approval'));
      // })
    );
  }
  rejectEmployeeByUser(employee: Employee): Observable<any> {
    return this.http.put('http://localhost:8082/employee-status/reject/'+employee.id, employee).pipe(
      tap(response => {
        console.log('Employee rejected by user successfully:', response);
      }),
      catchError(error => {
        console.error('Error rejecting employee by user:', error);
        throw error;
      })
    );
  }


  // Update an existing employee
  updateEmployee(empId: string, updatedEmployee: Employee): void {
    const employees = this.loadEmployees();
    const index = employees.findIndex(emp => emp.empId === empId);
    if (index !== -1) {
      employees[index] = updatedEmployee;
      this.saveEmployees(employees);
    }
  }
  // Send employee data to user
  sendToUser(employee: Employee): Observable<any> {
    return this.http.post('http://localhost:8082/employee-status/send-user', employee).pipe(
      tap(response => {
        console.log('Employee sent to user successfully:', response);
      }),
      catchError(error => {
        console.error('Error sending employee to user:', error);
        throw error;
      })
    );
  }
    // Send employee data to user
    sendToOperator(employee: Employee): Observable<any> {
      return this.http.put('http://localhost:8082/employee-status/pending/'+employee.id, employee).pipe(
        tap(response => {
          console.log('Employee sent to user successfully:', response);
        }),
        catchError(error => {
          console.error('Error sending employee to user:', error);
          throw error;
        })
      );
    }

    getEmployeeDetails(employeeId: number): Observable<any> {
      return this.http.get(`http://localhost:8082/employee/getEmp/${employeeId}`).pipe(
        tap(response => {
          console.log('Employee details fetched successfully:', response);
        }),
        catchError(error => {
          console.error('Error fetching employee details:', error);
          throw error;
        })
      );
    }

  // Get employee by ID
  getEmployeeById(empId: string): Employee | undefined {
    const employees = this.loadEmployees();
    return employees.find(emp => emp.empId === empId);
  }

  // Delete employee
  deleteEmployee(empId: string): void {
    const employees = this.loadEmployees();
    const filteredEmployees = employees.filter(emp => emp.empId !== empId);
    this.saveEmployees(filteredEmployees);
  }
  loadPendingEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>('http://localhost:8082/employee-status/status/pending').pipe(
      tap(data => {
        // Save to localStorage and update BehaviorSubject
        this.saveEmployees(data);
      }),
      catchError(error => {
        console.error('Error fetching approved employees from server:', error);
        // Fallback to localStorage data
        const localData = this.loadEmployees();
        return of(localData.filter(emp => emp.status === 'approved'));
      })
    );
  }

  loadApprovedEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>('http://localhost:8082/employee-status/status/approved').pipe(
      tap(data => {
        // Save to localStorage and update BehaviorSubject
        this.saveEmployees(data);
      }),
      catchError(error => {
        console.error('Error fetching approved employees from server:', error);
        // Fallback to localStorage data
        const localData = this.loadEmployees();
        return of(localData.filter(emp => emp.status === 'approved'));
      })
    );
  }

  // Add approveEmployee method to match backend endpoint
  approveEmployee(employee: Employee): Observable<any> {
    const payload = {
      approvedBy: localStorage.getItem('username') || 'System',
      remarks: employee.remarks || ''
    };

    return this.http.put(`http://localhost:8082/employee-status/approve/${employee.id}`, payload).pipe(
      tap(response => {
        console.log('Employee approved successfully:', response);
      }),
      catchError(error => {
        console.error('Error approving employee:', error);
        throw error;
      })
    );
  }
  loadRejectedEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>('http://localhost:8082/employee-status/status/rejected').pipe(
      tap(data => {
        // Save to localStorage and update BehaviorSubject
        this.saveEmployees(data);
      }),
      catchError(error => {
        console.error('Error fetching rejected employees from server:', error);
        // Fallback to localStorage data
        const localData = this.loadEmployees();
        return of(localData.filter(emp => emp.status === 'rejected'));
      })
    );
  }

  // Generic method to load employees by status
  loadEmployeesByStatus(status: 'pending' | 'approved' | 'rejected'): Observable<Employee[]> {
    return this.http.get<Employee[]>(`http://localhost:8082/employee-status/status/${status}`).pipe(
      tap(data => {
        // Save to localStorage and update BehaviorSubject
        this.saveEmployees(data);
      }),
      catchError(error => {
        console.error(`Error fetching ${status} employees from server:`, error);
        // Fallback to localStorage data
        const localData = this.loadEmployees();
        return of(localData.filter(emp => emp.status === status || (status === 'pending' && !emp.status)));
      })
    );
  }

  // Update rejectEmployee method to match backend endpoint
  rejectEmployee(employee: Employee, remarks: string): Observable<any> {
    const payload = {
      rejectedBy: localStorage.getItem('username') || sessionStorage.getItem('username') || 'System',
      remarks: remarks
    };

    return this.http.put(`http://localhost:8082/employee-status/reject/${employee.id}`, payload).pipe(
      tap(response => {
        console.log('Employee rejected successfully:', response);
      }),
      catchError(error => {
        console.error('Error rejecting employee:', error);
        throw error;
      })
    );
  }

}








