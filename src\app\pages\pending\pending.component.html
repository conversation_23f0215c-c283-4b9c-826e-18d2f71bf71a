<div class="mt-4">
    <h2>Employee Details</h2>
  
  
  <div class="row mb-3">
    <div class="col-md-6 d-flex align-items-center">
      <input type="text" class="form-control me-2" placeholder="Search employees..." [(ngModel)]="searchTerm" (input)="searchEmployees()">
      <button class="btn btn-outline-secondary flex-shrink-0" type="button" (click)="searchEmployees()" title="Search">
        <i class="bi bi-search"></i> Search
      </button>
    </div>
  </div>
    <div class="table-responsive">
      <table class="table table-striped table-bordered">
        <thead>
          <tr >
            <!-- <th class="th-color">Emp ID</th> -->
            <th class="th-color">Name</th>
            <th class="th-color">Designation</th>
            <th class="th-color">District</th>
            <th class="th-color">Date of Entry</th>
            <th class="th-color">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let employee of filteredEmployees; let i = index">
            <!-- <td>{{ employee.empId || employee.ecpfNumber }}</td> -->
            <td>{{ employee.employeeName }}</td>
            <td>{{ employee.designation }}</td>
            <td>{{ employee.district }}</td>
            <td>{{ employee.dateOfEntry | date:'dd/MM/yyyy' }}</td>
            <td>
              <button class="btn btn-sm btn-primary me-1" data-toggle="modal" data-target="#employeeModal" (click)="viewEmployee(employee)" title="View">
                <i class="bi bi-eye"></i>
              </button>
              <button *ngIf="employee.remarks" class="btn btn-sm btn-warning me-1" data-toggle="modal" data-target="#remarksModal" (click)="viewRemarks(employee)" title="View Remarks">
                <i class="bi bi-chat-text"></i>
              </button>
  
              <button class="btn btn-sm btn-success me-1" (click)="sendToUser(employee)" title="Send to User">
                <i class="bi bi-send"></i>
              </button>
              <button class="btn btn-sm btn-success me-1" (click)="approveEmployee(employee)" title="Approve">
                <i class="bi bi-check-circle"></i>
              </button>
              <button class="btn btn-sm btn-danger me-1" data-toggle="modal" data-target="#rejectionModal" (click)="rejectEmployee(employee)" title="Reject">
                <i class="bi bi-x-circle"></i>
              </button>

            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <label>Items per page:</label>
        <select class="form-control form-control-sm d-inline-block w-auto">
          <option>10</option>
          <option>20</option>
          <option>50</option>
        </select>
      </div>
      <nav>
        <ul class="pagination">
          <li class="page-item"><a class="page-link" href="#">1</a></li>
          <li class="page-item"><a class="page-link" href="#">2</a></li>
          <li class="page-item"><a class="page-link" href="#">3</a></li>
          <li class="page-item"><a class="page-link" href="#">Next</a></li>
        </ul>
      </nav>
    </div>
    <!-- Employee Details Modal -->
  <div class="modal fade" id="employeeModal" tabindex="-1" role="dialog" aria-labelledby="employeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="employeeModalLabel">
            <i class="bi bi-person-badge me-2"></i>
            Complete Employee Details
            <span *ngIf="selectedEmployee" class="badge bg-light text-dark ms-2">{{ selectedEmployee.empId }}</span>
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" *ngIf="selectedEmployee">
          <!-- Loading State -->
          <div *ngIf="isLoadingDetails" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading detailed employee information...</p>
          </div>
  
          <!-- Content when not loading -->
          <div *ngIf="!isLoadingDetails">
            <!-- Quick Summary Section -->
            <div class="row mb-4">
              <div class="col-12">
                <div class="card bg-light">
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-3 text-center">
                        <i class="bi bi-person-circle text-primary" style="font-size: 48px;"></i>
                        <h6 class="mt-2 mb-0">{{ selectedEmployeeDetails?.employeeName || selectedEmployee.employeeName }}</h6>
                        <small class="text-muted">{{ selectedEmployee.empId }}</small>
                      </div>
                      <div class="col-md-9">
                        <div class="row">
                          <div class="col-md-6">
                            <p class="mb-1"><strong>Designation:</strong> {{ selectedEmployeeDetails?.designation || selectedEmployee.designation }}</p>
                            <p class="mb-1"><strong>Authority:</strong> {{ selectedEmployeeDetails?.authority || selectedEmployee.Authority }}</p>
                            <p class="mb-1"><strong>Location:</strong> {{ selectedEmployeeDetails?.location || selectedEmployee.location }}, {{ selectedEmployeeDetails?.district || selectedEmployee.district }}</p>
                          </div>
                          <div class="col-md-6">
                            <p class="mb-1"><strong>Date of Entry:</strong> {{ selectedEmployeeDetails?.dateOfEntry || selectedEmployee.dateOfEntryIntoService | date:'dd/MM/yyyy' }}</p>
                            <p class="mb-1"><strong>Education:</strong> {{ selectedEmployeeDetails?.educationalQualification || selectedEmployee.educationalQualification }}</p>
                            <p class="mb-1">
                              <strong>Status:</strong>
                              <span class="badge"
                                    [ngClass]="{
                                      'bg-warning text-dark': (selectedEmployeeDetails?.status || selectedEmployee.status) === 'pending',
                                      'bg-success': (selectedEmployeeDetails?.status || selectedEmployee.status) === 'approved',
                                      'bg-danger': (selectedEmployeeDetails?.status || selectedEmployee.status) === 'rejected'
                                    }">
                                {{ (selectedEmployeeDetails?.status || selectedEmployee.status || 'pending') | titlecase }}
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
  
            <!-- Basic Information Section -->
            <div class="row">
              <div class="col-md-6">
                <h6 class="text-primary">
                  <i class="bi bi-person-fill me-2"></i>Personal Information
                </h6>
                <p><strong>Employee ID:</strong> {{ selectedEmployee.empId }}</p>
                <p><strong>Full Name:</strong> {{ selectedEmployeeDetails?.employeeName || selectedEmployee.employeeName }}</p>
                <p><strong>Father's Name:</strong> {{ selectedEmployeeDetails?.fatherName || selectedEmployee.fatherName }}</p>
                <p><strong>Date of Birth:</strong> {{ selectedEmployeeDetails?.dateOfBirth || selectedEmployee.dateOfBirth | date:'dd/MM/yyyy' }}</p>
                <p><strong>Religion:</strong> {{ selectedEmployeeDetails?.religion || selectedEmployee.religion }}</p>
                <p><strong>Community:</strong> {{ selectedEmployeeDetails?.community || selectedEmployee.community }}</p>
                <p *ngIf="selectedEmployeeDetails?.identificationMark1"><strong>Identification Mark 1:</strong> {{ selectedEmployeeDetails.identificationMark1 }}</p>
                <p *ngIf="selectedEmployeeDetails?.identificationMark2"><strong>Identification Mark 2:</strong> {{ selectedEmployeeDetails.identificationMark2 }}</p>
                <p *ngIf="!selectedEmployeeDetails && selectedEmployee.personalIdentificationMarks"><strong>Personal Identification Marks:</strong> {{ selectedEmployee.personalIdentificationMarks }}</p>
              </div>
              <div class="col-md-6">
                <h6 class="text-primary">
                  <i class="bi bi-building me-2"></i>Professional Information
                </h6>
                <p><strong>Authority:</strong> {{ selectedEmployeeDetails?.authority || selectedEmployee.Authority }}</p>
                <p><strong>Designation:</strong> {{ selectedEmployeeDetails?.designation || selectedEmployee.designation }}</p>
                <p><strong>Date of Entry into Service:</strong> {{ selectedEmployeeDetails?.dateOfEntry || selectedEmployee.dateOfEntryIntoService | date:'dd/MM/yyyy' }}</p>
                <p><strong>Educational Qualification:</strong> {{ selectedEmployeeDetails?.educationalQualification || selectedEmployee.educationalQualification }}</p>
                <p><strong>District:</strong> {{ selectedEmployeeDetails?.district || selectedEmployee.district }}</p>
                <p><strong>Location:</strong> {{ selectedEmployeeDetails?.location || selectedEmployee.location }}</p>
              </div>
            </div>
  
            <!-- Status and Tracking Information -->
            <div class="row mt-3" *ngIf="selectedEmployeeDetails || selectedEmployee.status || selectedEmployee.updatedAt || selectedEmployee.approvedBy || selectedEmployee.rejectedBy || selectedEmployee.remarks">
              <div class="col-12">
                <h6 class="text-primary">
                  <i class="bi bi-info-circle me-2"></i>Status & Tracking Information
                </h6>
                <div class="row">
                  <div class="col-md-6">
                    <p>
                      <strong>Current Status:</strong>
                      <span class="badge"
                            [ngClass]="{
                              'bg-warning text-dark': (selectedEmployeeDetails?.status || selectedEmployee.status) === 'pending' || (selectedEmployeeDetails?.status || selectedEmployee.status) === 'user-approval',
                              'bg-success': (selectedEmployeeDetails?.status || selectedEmployee.status) === 'approved',
                              'bg-danger': (selectedEmployeeDetails?.status || selectedEmployee.status) === 'rejected'
                            }">
                        {{ (selectedEmployeeDetails?.status || selectedEmployee.status || 'pending') | titlecase }}
                      </span>
                    </p>
                    <p *ngIf="selectedEmployeeDetails?.approvedBy || selectedEmployee.approvedBy"><strong>Approved By:</strong> {{ selectedEmployeeDetails?.approvedBy || selectedEmployee.approvedBy }}</p>
                    <p *ngIf="selectedEmployeeDetails?.rejectedBy || selectedEmployee.rejectedBy"><strong>Rejected By:</strong> {{ selectedEmployeeDetails?.rejectedBy || selectedEmployee.rejectedBy }}</p>
                  </div>
                  <div class="col-md-6">
                    <p *ngIf="selectedEmployeeDetails?.createdAt"><strong>Created Date:</strong> {{ selectedEmployeeDetails.createdAt | date:'dd/MM/yyyy HH:mm' }}</p>
                    <p *ngIf="selectedEmployeeDetails?.updatedAt || selectedEmployee.updatedAt"><strong>Last Updated:</strong> {{ selectedEmployeeDetails?.updatedAt || selectedEmployee.updatedAt | date:'dd/MM/yyyy HH:mm' }}</p>
                    <p *ngIf="selectedEmployee.approvedDate"><strong>Approved Date:</strong> {{ selectedEmployee.approvedDate | date:'dd/MM/yyyy HH:mm' }}</p>
                    <p *ngIf="selectedEmployee.rejectedDate"><strong>Rejected Date:</strong> {{ selectedEmployee.rejectedDate | date:'dd/MM/yyyy HH:mm' }}</p>
                  </div>
                </div>
  
  
              </div>
            </div>
  
            <!-- Service History Section -->
            <div class="row mt-4" *ngIf="(selectedEmployeeDetails?.serviceHistory && selectedEmployeeDetails.serviceHistory.length > 0) || (selectedEmployee.serviceEntries && selectedEmployee.serviceEntries.length > 0)">
              <div class="col-12">
                <h6 class="text-primary">
                  <i class="bi bi-clock-history me-2"></i>Service History
                  <span class="badge bg-secondary ms-2">{{ (selectedEmployeeDetails?.serviceHistory || selectedEmployee.serviceEntries)?.length }} entries</span>
                </h6>
                <div class="table-responsive">
                  <table class="table table-sm table-bordered table-hover">
                    <thead class="table-light">
                      <tr>
                        <th><i class="bi bi-calendar3 me-1"></i>Date</th>
                        <th><i class="bi bi-tag me-1"></i>Type</th>
                        <th><i class="bi bi-check-circle me-1"></i>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Show API data if available, otherwise show local data -->
                      <tr *ngFor="let service of (selectedEmployeeDetails?.serviceHistory || selectedEmployee.serviceEntries); let i = index">
                        <td>{{ service.date | date:'dd/MM/yyyy' }}</td>
                        <td>
                          <span class="badge bg-info">{{ service.type }}</span>
                        </td>
                        <td>
                          <span class="badge"
                                [ngClass]="{
                                  'bg-success': service.status === 'Completed' || service.status === 'Promotion',
                                  'bg-warning text-dark': service.status === 'In Progress',
                                  'bg-secondary': service.status === 'Pending'
                                }">
                            {{ service.status }}
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
  
            <!-- Leave Balance Section -->
            <div class="row mt-4" *ngIf="(selectedEmployeeDetails?.leaveBalances && selectedEmployeeDetails.leaveBalances.length > 0) || (selectedEmployee.leaveEntries && selectedEmployee.leaveEntries.length > 0)">
              <div class="col-12">
                <h6 class="text-primary">
                  <i class="bi bi-calendar-check me-2"></i>Leave Balance
                  <span class="badge bg-secondary ms-2">{{ (selectedEmployeeDetails?.leaveBalances || selectedEmployee.leaveEntries)?.length }} types</span>
                </h6>
                <div class="table-responsive">
                  <table class="table table-sm table-bordered table-hover">
                    <thead class="table-light">
                      <tr>
                        <th><i class="bi bi-calendar-event me-1"></i>Leave Type</th>
                        <th><i class="bi bi-hash me-1"></i>Balance Count</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Show API data if available, otherwise show local data -->
                      <tr *ngFor="let leave of (selectedEmployeeDetails?.leaveBalances || selectedEmployee.leaveEntries); let i = index">
                        <td>
                          <span class="badge bg-primary">{{ leave.leaveType }}</span>
                        </td>
                        <td>
                          <strong class="text-success">{{ leave.balanceCount || leave.leaveBalanceCount }}</strong> days
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
  
            <!-- Empty State Messages -->
            <div class="row mt-4" *ngIf="(!selectedEmployeeDetails?.serviceHistory || selectedEmployeeDetails.serviceHistory.length === 0) && (!selectedEmployee.serviceEntries || selectedEmployee.serviceEntries.length === 0)">
              <div class="col-12">
                <div class="alert alert-info" role="alert">
                  <i class="bi bi-info-circle me-2"></i>
                  <strong>No Service History:</strong> No service entries have been recorded for this employee yet.
                </div>
              </div>
            </div>
  
            <div class="row mt-3" *ngIf="(!selectedEmployeeDetails?.leaveBalances || selectedEmployeeDetails.leaveBalances.length === 0) && (!selectedEmployee.leaveEntries || selectedEmployee.leaveEntries.length === 0)">
              <div class="col-12">
                <div class="alert alert-info" role="alert">
                  <i class="bi bi-info-circle me-2"></i>
                  <strong>No Leave Records:</strong> No leave balance information has been recorded for this employee yet.
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
  
          <button type="button" class="btn btn-success" (click)="sendToUser(selectedEmployee!)" data-dismiss="modal">Send to User</button>
          <button type="button" class="btn btn-warning" (click)="editEmployee(selectedEmployee!)" data-dismiss="modal">Edit</button>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- PDF Files Modal -->
  <div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="pdfModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="pdfModalLabel">Uploaded PDF Files</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div *ngIf="selectedEmployeePDFs && selectedEmployeePDFs.length > 0; else noPDFs">
            <div class="row">
              <div class="col-md-4 mb-3" *ngFor="let pdf of selectedEmployeePDFs; let i = index">
                <div class="pdf-card border rounded p-3 text-center h-100">
                  <div class="pdf-icon mb-2" (click)="openPDF(pdf)" style="cursor: pointer;">
                    <i class="bi bi-file-earmark-pdf text-danger" style="font-size: 48px;"></i>
                  </div>
                  <h6 class="pdf-title text-truncate" [title]="pdf.name">{{ pdf.name }}</h6>
                  <small class="text-muted">{{ formatFileSize(pdf.size) }}</small>
                  <div class="mt-2">
                    <button class="btn btn-sm btn-primary" (click)="openPDF(pdf)" title="Open PDF">
                      <i class="bi bi-eye"></i> View
                    </button>
                    <button class="btn btn-sm btn-secondary ms-1" (click)="downloadPDF(pdf)" title="Download PDF">
                      <i class="bi bi-download"></i> Download
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <ng-template #noPDFs>
            <div class="text-center py-4">
              <i class="bi bi-file-earmark-pdf text-muted" style="font-size: 64px;"></i>
              <h5 class="mt-3 text-muted">No PDF Files Found</h5>
              <p class="text-muted">This employee hasn't uploaded any PDF files yet.</p>
            </div>
          </ng-template>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  
  
  
  </div>
    <!-- Remarks Modal -->
    <div class="modal fade" id="remarksModal" tabindex="-1" role="dialog" aria-labelledby="remarksModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="remarksModalLabel">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              Rejection Remarks
            </h5>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" *ngIf="selectedEmployee">
            <div class="mb-3">
              <h6 class="text-primary">Employee Information</h6>
              <p><strong>Employee ID:</strong> {{ selectedEmployee.empId }}</p>
              <p><strong>Name:</strong> {{ selectedEmployee.employeeName }}</p>
              <p><strong>Designation:</strong> {{ selectedEmployee.designation }}</p>
            </div>
  
            <div class="mb-3">
              <h6 class="text-danger">Rejection Details</h6>
              <p><strong>Rejected By:</strong> {{ selectedEmployee.rejectedBy || 'N/A' }}</p>
              <p *ngIf="selectedEmployee.rejectedDate"><strong>Rejected Date:</strong> {{ selectedEmployee.rejectedDate | date:'dd/MM/yyyy HH:mm' }}</p>
              <p *ngIf="selectedEmployee.updatedAt"><strong>Last Updated:</strong> {{ selectedEmployee.updatedAt | date:'dd/MM/yyyy HH:mm' }}</p>
            </div>
  
            <div class="alert alert-danger" role="alert">
              <h6 class="alert-heading">
                <i class="bi bi-chat-text me-2"></i>
                Reason for Rejection:
              </h6>
              <hr>
              <p class="mb-0">{{ selectedEmployee.remarks || 'No remarks provided' }}</p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  