import { Component, OnInit } from '@angular/core';
import { Employee } from '../form-fill/employee.model';
import { StatusServiceService } from '../../services/status-service.service';
import { Router } from '@angular/router';
import { EmployeeService } from '../../services/employee.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormGroup, FormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import * as bootstrap from 'bootstrap';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-pending-list',
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './pending-list.component.html',
  styleUrl: './pending-list.component.css'
})
export class PendingListComponent implements OnInit {

 employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
statusFormGroup: FormGroup;
  pendiList:any[]=[];
  searchTerm: string = '';
  selectedEmployee: any;

  selectedEmployeePDFs: File[] = [];
  selectedEmployeeDetails: any = null;
  isLoadingDetails: boolean = false;
  rejectionRemarks: string;
  isRejectionModalOpen: boolean;
  isSubmitting: boolean;
  submitSuccess: boolean;
  submitMessage: string;
  registrationForm: any;
  userRole: string | null = null;

  constructor(
    private statusService: StatusServiceService, private employeeService: EmployeeService,
    private router: Router, private http:HttpClient, private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.userRole = localStorage.getItem('role');

    this.statusFormGroup= this.formBuilder.group({
      status:[''],
      remarks:['']
    })
    this.pendList();

  }

  openApprovalModal(employee: any): void {
    this.selectedEmployee = employee;
    this.statusFormGroup.reset(); // clear previous data
  }


  pendList(){
    this.statusService.getPendingList().subscribe({
      next: (employees) => {
        console.log('Employees loaded:', employees);
        this.employees = employees;
        this.pendiList = [...this.employees];
      },
      error: (error) => {
        console.error('Error loading employees:', error);
        alert('Failed to load employees. Please try again later.');
      }
    });
  }

  statusApproval(){

  }
  async exportToPDF(): Promise<void> {
    if (!this.selectedEmployee) {
      alert('No employee data to export');
      return;
    }

    try {
      // Get logo base64 first
      const logoBase64 = await this.getLogoBase64();

      // Create PDF content as HTML string with logo
      const pdfContent = this.generatePDFContent(logoBase64);

      // Directly download as HTML file
      this.downloadAsHTML(pdfContent);

    } catch (error) {
      console.error('Error exporting to PDF:', error);
      alert('Error exporting to PDF. Please try again.');
    }
  }
  statusSubmit(id: any) {
    if (this.statusFormGroup.valid) {
      this.isSubmitting = true;
      const { status, remarks } = this.statusFormGroup.value;
      const requestData = { status, remarks };

      console.log('Submitting status update:', { id, requestData });

      this.statusService.updateStatus(id, requestData).subscribe({
        next: (response) => {
          console.log('Full API Response:', response);
          console.log('Response status:', response?.status);
          console.log('Response type:', typeof response);

          this.isSubmitting = false;
          this.submitSuccess = true;
          this.submitMessage = 'Status updated successfully!';

          // Close the modal
          this.closeModal('approvalModal');

          // Reset the form
          this.statusFormGroup.reset();

          // Show success message using SweetAlert2
          Swal.fire({
            title: 'Success!',
            text: 'Status updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK'
          }).then(() => {
            // Reload the pending list data instead of the entire page
            this.pendList();
          });
        },
        error: (error) => {
          console.error('Status update error details:', error);
          console.error('Error status code:', error.status);
          console.error('Error response:', error.error);

          this.isSubmitting = false;
          this.submitSuccess = false;
          this.submitMessage = 'Failed to update status. Please try again.';

          // Check if it's actually a successful response with error structure
          if (error.status === 200 || (error.error && error.error.success)) {
            // Handle case where backend returns 200 but with error structure
            Swal.fire({
              title: 'Success!',
              text: 'Status updated successfully!',
              icon: 'success',
              confirmButtonText: 'OK'
            }).then(() => {
              this.closeModal('approvalModal');
              this.statusFormGroup.reset();
              this.pendList();
            });
          } else {
            const errorMessage = error.error?.message || error.message || 'Failed to update status. Please try again.';
            Swal.fire({
              title: 'Error!',
              text: errorMessage,
              icon: 'error',
              confirmButtonText: 'OK'
            });
          }
        },
        complete: () => {
          console.log('Status update request completed');
          this.isSubmitting = false;
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.statusFormGroup.controls).forEach(key => {
        this.statusFormGroup.get(key)?.markAsTouched();
      });
    }
  }
  canApproveReject(): boolean {
    return this.userRole === null || this.userRole === '' || this.userRole === 'null';
  }

  rejectEmployee(employee: Employee): void {
    Swal.fire({
      title: 'Change Request',
      text: `Are you sure you want to change values for this ${employee.employeeName}?`,
      input: 'textarea',
      inputLabel: 'Change Request',
      inputPlaceholder: 'Please provide detailed remarks for Changes...',
      inputAttributes: {
        'aria-label': 'Change remarks',
        'rows': '4'
      },
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Change!',
      cancelButtonText: 'Cancel',
      inputValidator: (value) => {
        if (!value || !value.trim()) {
          return 'Please provide remarks for Change!';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const payload = {
          rejectedBy: localStorage.getItem('employeeName') || localStorage.getItem('username') || 'System',
          status: 'pending',
          remarks: result.value.trim()
        };

        // Show loading
        Swal.fire({
          title: 'Processing...',
          text: 'Request sent to employee record',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        this.http.put(`http://localhost:8082/employee-status/reject/${employee.id}`, payload)
          .subscribe({
            next: (response) => {
              console.log('Employee rejected successfully:', response);

              Swal.fire({
                title: 'Change Request Sent!',
                text: `change request has been sent successfully`,
                icon: 'success',
                confirmButtonColor: '#dc3545'
              });

              // Clear stored employee data after successful rejection
              if (this.canApproveReject()) {
                this.clearStoredEmployeeData();
                // Clear the current employee list since the record is now rejected
                this.employees = [];
                this.filteredEmployees = [];
              }
            },
            error: (error) => {
              console.error('Error rejecting employee:', error);

              Swal.fire({
                title: 'Error!',
                text: `Failed to reject ${employee.employeeName}. Please try again.`,
                icon: 'error',
                confirmButtonColor: '#dc3545'
              });
            }
          });
      }
    });
  }

  // Method to close modal programmatically
  private closeModal(modalId: string) {
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
      // For Bootstrap 5
      const modal = (window as any).bootstrap?.Modal?.getInstance(modalElement);
      if (modal) {
        modal.hide();
      } else {
        // Fallback for Bootstrap 4 or if Bootstrap 5 instance not found
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
          backdrop.remove();
        }
      }
    }
  }

  confirmRejectEmployee(): void {
    if (!this.selectedEmployee) return;

    if (!this.rejectionRemarks.trim()) {
      Swal.fire({
        title: 'Missing Information',
        text: 'Please provide remarks for rejection.',
        icon: 'warning',
        confirmButtonColor: '#ffc107'
      });
      return;
    }

    const payload = {
      rejectedBy: localStorage.getItem('employeeName') || localStorage.getItem('username') || 'System',
      remarks: this.rejectionRemarks
    };

    // Show loading
    Swal.fire({
      title: 'Processing...',
      text: 'Rejecting employee record',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.http.put(`http://localhost:8082/employee-status/reject/${this.selectedEmployee.id}`, payload)
      .subscribe({
        next: (response) => {
          console.log('Employee rejected successfully:', response);

          Swal.fire({
            title: 'Rejected!',
            text: `${this.selectedEmployee!.employeeName} has been rejected successfully!`,
            icon: 'success',
            confirmButtonColor: '#dc3545'
          });

          // Clear stored employee data after successful rejection
          if (this.canApproveReject()) {
            this.clearStoredEmployeeData();
            // Clear the current employee list since the record is now rejected
            this.employees = [];
            this.filteredEmployees = [];
          }

          this.closeRejectionModal();
        },
        error: (error) => {
          console.error('Error rejecting employee:', error);

          Swal.fire({
            title: 'Error!',
            text: `Failed to reject ${this.selectedEmployee!.employeeName}. Please try again.`,
            icon: 'error',
            confirmButtonColor: '#dc3545'
          });
        }
      });
  }

  closeRejectionModal(): void {
    this.isRejectionModalOpen = false;
    this.selectedEmployee = null;
    this.rejectionRemarks = '';
  }

  clearStoredEmployeeData(): void {
    localStorage.removeItem('employeeResponseData');
    localStorage.removeItem('role');
    localStorage.removeItem('username');
    localStorage.removeItem('employeeId');
    localStorage.removeItem('employeeName');
    localStorage.removeItem('token');

    // Set a flag to prevent login success message from showing again
    localStorage.setItem('skipLoginMessage', 'true');
    console.log('Cleared stored employee data from localStorage');

    // Navigate to login page after clearing data
    setTimeout(() => {
      this.router.navigate(['/login']);
    }, 2000);
  }



  // searchEmployees(): void {
  //   if (!this.searchTerm.trim()) {
  //     this.filteredEmployees = [...this.employees];
  //     return;
  //   }

  //   const searchTermLower = this.searchTerm.toLowerCase();
  //   this.filteredEmployees = this.employees.filter(employee =>
  //     employee.employeeName.toLowerCase().includes(searchTermLower) ||
  //     employee.designation.toLowerCase().includes(searchTermLower) ||
  //     employee.Authority.toLowerCase().includes(searchTermLower) ||
  //     employee.district.toLowerCase().includes(searchTermLower) ||employee.location.toLowerCase().includes(searchTermLower) ||
  //     employee.empId.toLowerCase().includes(searchTermLower)
  //   );
  // }

  editEmployee(employee: Employee): void {
    this.router.navigate(['/form-edit'], {
      queryParams: { edit: true, empId: employee.id || employee.empId }
    });
  }

  viewEmployee(employee: Employee): void {
    console.log('Viewing employee details for employee:', employee);
    console.log('Employee ID to be used for API call:', employee.id);

    // Use employee.id specifically for the API call
    if (!employee.id) {
      console.error('No employee.id found');
      this.selectedEmployee = employee;
      alert('Could not find employee ID. Showing basic information.');
      return;
    }

    this.http.get<any>(`http://localhost:8082/employee/getEmp/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Complete employee data received:', response);
          console.log('Nominees in API response:', response.nominees);

          this.selectedEmployee = this.mapCompleteEmployeeData(response);

          console.log('Mapped employee nomination entries:', this.selectedEmployee.nominationEntries);
          console.log('Nominee photos after mapping:', this.selectedEmployee.nominationEntries?.map((nominee, index) => ({
            index: index + 1,
            name: nominee.nomineeName,
            rawPhoto: nominee.nomineePhoto,
            processedPhotoUrl: (nominee as any).nomineePhotoUrl
          })));

          // Load profile photo after setting employee data
          this.loadEmployeeProfilePhoto(employee.id.toString());
          // Initialize nominee photo loading
          this.initializeNomineePhotos();
        },
        error: (error) => {
          console.error('Error fetching employee details:', error);
          console.error('API URL called:', `http://localhost:8082/employee/getEmp/${employee.id}`);
          // Fallback to existing data if API fails
          this.selectedEmployee = employee;
          alert('Could not load complete employee details. Showing basic information.');
        }
      });
  }

  refreshData(): void {
    this.searchTerm = '';
  }

  firstLevelAprroval(employee: Employee): void {
    this.selectedEmployee = employee;
    this.selectedEmployeeDetails = null;
    this.isLoadingDetails = true;

    // Call API to get detailed employee information
    this.employeeService.getEmployeeDetails(employee.id).subscribe({
      next: (details) => {
        console.log('Employee details loaded:', details);
        this.selectedEmployeeDetails = details;
        this.isLoadingDetails = false;
      },
      error: (error) => {
        console.error('Error loading employee details:', error);
        this.isLoadingDetails = false;
        // Keep the basic employee data if API fails
        alert('Failed to load detailed employee information. Showing basic details only.');
      }
    });
  }

  private mapCompleteEmployeeData(apiData: any): Employee {
    return {
      // Basic Information
      id: apiData.id || apiData.employeeId || apiData.ecpfNumber || '',
      empId: apiData.ecpfNumber || apiData.empId || '',
      ecpfNumber: apiData.ecpfNumber || '',
      employeeName: apiData.employeeName || '',
      designation: apiData.currentDesignation || apiData.designation || '',
      Authority: apiData.section || '',
      district: apiData.district || '',
      location: apiData.nativePlaceAndTaluk || '',
      fatherName: apiData.fatherName || apiData.profile?.fatherName || '',
      motherName: apiData.motherName || apiData.profile?.motherName || '',
      dateOfBirth: apiData.dateOfBirth || apiData.profile?.dateOfBirth || '',
      religion: apiData.religion || '',
      community: apiData.community || apiData.profile?.community || '',
      createdAt: apiData.createdAt || '',
      caste: apiData.caste || apiData.profile?.caste || '',
      personalIdentificationMarks: (apiData.personalIdentificationmark1 || '') +
        (apiData.personalIdentificationmark2 ? ', ' + apiData.personalIdentificationmark2 : ''),
      dateOfEntryIntoService: apiData.dateOfEntry || '',
      educationalQualification: this.formatEducationQualifications(apiData.educationQualifications),

      // Missing required fields
      status: apiData.status || '',
      dateOfEntry: apiData.dateOfEntry || '',
      educationEnties: this.mapEducationEntries(apiData.educationQualifications || []),

      // Contact Information
      email: apiData.email || apiData.profile?.email || '',
      presentAddress: apiData.presentAddress || apiData.profile?.presentaddress || '',
      permanentAddress: apiData.permanentAddress || apiData.profile?.permanentaddress || '',

      // Account Details
      bankAccountNo: apiData.accountDetails?.bankaccountnumber || '',
      ifscCode: apiData.accountDetails?.ifsccode || '',
      bankName: apiData.accountDetails?.bankname || '',
      panNumber: apiData.panNumber || '',
      uanNumber: apiData.accountDetails?.uannumber || '',
      aadharNumber: apiData.accountDetails?.aadharnumber || '',

      // Service Records
      serviceRecords: apiData.serviceHistory || [],
      leaveEntries: this.mapLeaveBalances(apiData.leaveBalances || []),

      // Additional Information
      registerNumber: apiData.registerNumber || '',
      personalIdMark1: apiData.personalIdentificationmark1 || '',
      personalIdMark2: apiData.personalIdentificationmark2 || '',
      nativeAndTaluk: apiData.nativePlaceAndTaluk || '',

      // Profile information
      profile: apiData.profile || {},
      profilePhotoUrl: this.generateProfilePhotoUrl(apiData),
      gender: apiData.gender || apiData.profile?.gender || '',
      mobileNumber: apiData.mobileNumber || apiData.profile?.mobileNumber || '',

      // Arrays
      serviceEntries: this.mapServiceHistory(apiData.serviceHistory || []),
      trainingEntries: this.mapTrainingDetails(apiData.trainingDetails || []),
      punishmentEntries: this.mapPunishmentDetails(apiData.punishmentDetails || []),
      nominationEntries: this.mapNominees(apiData.nominees || []),

      // Salary Details
      salaryDetails: {
        lastSalaryRevisedDate: apiData.salaryDetails?.lastSalaryRevisedDate || '',
        group: apiData.salaryDetails?.group || apiData.group || '',
        payband: apiData.salaryDetails?.payband || apiData.payband || '',
        gradepay: apiData.salaryDetails?.gradepay || apiData.gradepay || ''
      },

      remarks: apiData.remarks || '',
      rejectedBy: apiData.rejectedBy || ''
    };
  }

  private mapLeaveBalances(leaveBalances: any[]): any[] {
    return leaveBalances.map(leave => ({
      leaveType: leave.leaveType || '',
      leaveBalanceCount: leave.openingBalance || leave.closingBalance || 0,
      openingBalance: leave.openingBalance || 0,
      closingBalance: leave.closingBalance || 0,
      entryDate: leave.entryDate || ''
    }));
  }

  private mapServiceHistory(serviceHistory: any[]): any[] {
    return serviceHistory.map(service => ({
      date: service.date || service.dateofappointment || service.joiningdate || service.fromdate || service.promoteddate || service.punishmentdate || '',
      type: service.type || '',
      status: service.status || 'Active',

      // Appointment fields
      appointmentType: service.appointmenttype || '',
      modeOfAppointment: service.modeofappointment || '',
      dateOfAppointment: service.dateofappointment || '',
      proceedingOrderNo: service.proceedingorderno || '',
      proceedingOrderDate: service.proceedingorderdate || '',

      // Promotion fields
      joiningDate: service.joiningdate || '',
      fromDesignation: service.fromdesignation || '',
      toPromoted: service.topromoted || '',
      promotedDate: service.promoteddate || '',

      // Transfer fields
      fromDate: service.fromdate || '',
      toDate: service.todate || '',
      fromPlace: service.fromplace || '',
      toPlace: service.toplace || '',

      // Increment fields
      typeOfIncrement: service.typeofincrement || '',
      incrementType: service.incrementtype || '',

      // Deputation fields
      designation: service.designation || '',
      originalDesignation: service.originaldesignation || '',
      parentDepartment: service.parentdepartment || '',

      // Punishment fields
      punishmentType: service.punishmenttype || '',
      punishmentDate: service.punishmentdate || '',
      caseDetails: service.casedetails || '',
      caseNumber: service.casenumber || '',
      caseDate: service.casedate || '',
      presentStatus: service.presentstatus || '',
      description: service.description || '',
      personInvolved: service.personinvolved || '',
      involvedPersonsWithNames: service.involvedPersonsWithNames || []
    }));
  }

  private mapTrainingDetails(trainingDetails: any[]): any[] {
    return trainingDetails.map(training => ({
      trainingType: training.trainingtype || '',
      trainingDate: training.date || ''
    }));
  }

  private mapPunishmentDetails(punishmentDetails: any[]): any[] {
    return punishmentDetails.map(punishment => ({
      punishmentType: punishment.punishmenttype || '',
      punishmentDate: punishment.date || '',
      caseDetails: punishment.casedetails || '',
      caseNumber: punishment.casenumber || '',
      caseDate: punishment.casedate || '',
      presentStatus: punishment.presentstatus || '',
      description: punishment.description || '',
      personInvolved: punishment.personinvolved || '',
      involvedPersonsWithNames: punishment.involvedPersonsWithNames || []
    }));
  }

  private mapNominees(nominees: any[]): any[] {
    return nominees.map(nominee => {
      const mappedNominee = {
        nomineeName: nominee.nomineename || '',
        address: nominee.address || '',
        relationship: nominee.relationship || '',
        age: nominee.age || 0,
        percentageOfShare: nominee.percentageofshare || 0,
        gender: nominee.gender || '',
        nomineePhoto: nominee.nomineephoto || nominee.nomineePhoto || '',
        photoLoadingComplete: false
      };

      // If nominee has a photo, construct the full URL immediately
      if (mappedNominee.nomineePhoto && mappedNominee.nomineePhoto !== 'pending_upload' && mappedNominee.nomineePhoto.trim() !== '') {
        const baseUrl = 'http://localhost:8082';
        const photoPath = mappedNominee.nomineePhoto;

        let fullPhotoUrl: string;
        if (photoPath.startsWith('http')) {
          fullPhotoUrl = photoPath;
        } else if (photoPath.startsWith('/api/')) {
          fullPhotoUrl = `${baseUrl}${photoPath}`;
        } else if (photoPath.startsWith('/')) {
          fullPhotoUrl = `${baseUrl}${photoPath}`;
        } else {
          // Ensure path starts with /
          fullPhotoUrl = `${baseUrl}/${photoPath}`;
        }

        // Store the processed URL for immediate use
        (mappedNominee as any).nomineePhotoUrl = fullPhotoUrl;
        console.log('Nominee photo URL set during mapping:', fullPhotoUrl);
      }

      return mappedNominee;
    });
  }

  private mapEducationEntries(educationQualifications: any[]): any[] {
    return educationQualifications.map(education => ({
      qualification: education.qualification || '',
      courseName: education.coursename || '',
      instituteName: education.schoolname || education.collegename || education.universityname || ''
    }));
  }

  private formatEducationQualifications(educationQualifications: any[]): string {
    if (!educationQualifications || educationQualifications.length === 0) {
      return 'Not specified';
    }
    return educationQualifications.map(edu => edu.qualification).join(', ');
  }

  // PDF Export Methods (copied from view component)
  private async getLogoBase64(): Promise<string> {
    return new Promise<string>((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        const base64 = canvas.toDataURL('image/png');
        resolve(base64);
      };
      img.onerror = () => {
        resolve(''); // Return empty string if logo fails to load
      };
      img.src = 'logo_tncsc.png';
    });
  }

  private generatePDFContent(logoBase64?: string): string {
    const employee = this.selectedEmployee!;

    // Helper function to safely get array length
    const getArrayLength = (arr: any[]): number => {
      return Array.isArray(arr) ? arr.length : 0;
    };

    // Helper function to format date
    const formatDate = (dateStr: string): string => {
      if (!dateStr) return 'Not specified';
      try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-GB');
      } catch {
        return dateStr;
      }
    };

    // Helper function to get photo HTML
    const getPhotoHTML = (): string => {
      if (employee.profilePhotoUrl) {
        return `<img src="${employee.profilePhotoUrl}" alt="Employee Photo"
                     style="width: 120px; height: 120px; object-fit: cover; border: 1px solid #ddd;">`;
      } else {
        return `<div style="width: 120px; height: 120px; border: 1px solid #ddd;
                           background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;
                           text-align: center; line-height: 120px; color: #666; font-size: 9px;">
                  No Photo Available
                </div>`;
      }
    };

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Employee Details - ${employee.employeeName}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; line-height: 1.4; }
            .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; }
            .logo { max-width: 80px; height: auto; margin-bottom: 10px; }
            .title { font-size: 18px; font-weight: bold; margin: 5px 0; }
            .subtitle { font-size: 14px; color: #666; }
            .employee-info { display: flex; margin-bottom: 20px; }
            .photo-section { margin-right: 20px; }
            .basic-info { flex: 1; }
            .section { margin-bottom: 15px; }
            .section-title { font-size: 14px; font-weight: bold; background-color: #f0f0f0; padding: 5px; margin-bottom: 10px; border-left: 4px solid #007bff; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
            .info-item { margin-bottom: 5px; }
            .label { font-weight: bold; color: #333; }
            .value { color: #666; }
            .table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 11px; }
            .table th { background-color: #f8f9fa; font-weight: bold; }
            .table tr:nth-child(even) { background-color: #f9f9f9; }
            .no-data { color: #999; font-style: italic; }
            @media print { body { margin: 0; } .header { page-break-after: avoid; } }
        </style>
    </head>
    <body>
        <div class="header">
            ${logoBase64 ? `<img src="${logoBase64}" alt="Logo" class="logo">` : ''}
            <div class="title">TAMIL NADU CIVIL SUPPLIES CORPORATION LIMITED</div>
            <div class="subtitle">Employee Service Record</div>
        </div>

        <div class="employee-info">
            <div class="photo-section">
                ${getPhotoHTML()}
            </div>
            <div class="basic-info">
                <div class="info-grid">
                    <div class="info-item"><span class="label">Employee Name:</span> <span class="value">${employee.employeeName || 'Not specified'}</span></div>
                    <div class="info-item"><span class="label">Employee ID:</span> <span class="value">${employee.empId || 'Not specified'}</span></div>
                    <div class="info-item"><span class="label">ECPF Number:</span> <span class="value">${employee.ecpfNumber || 'Not specified'}</span></div>
                    <div class="info-item"><span class="label">Designation:</span> <span class="value">${employee.designation || 'Not specified'}</span></div>
                    <div class="info-item"><span class="label">District:</span> <span class="value">${employee.district || 'Not specified'}</span></div>
                    <div class="info-item"><span class="label">Authority:</span> <span class="value">${employee.Authority || 'Not specified'}</span></div>
                    <div class="info-item"><span class="label">Date of Birth:</span> <span class="value">${formatDate(employee.dateOfBirth)}</span></div>
                    <div class="info-item"><span class="label">Date of Entry:</span> <span class="value">${formatDate(employee.dateOfEntryIntoService || employee.dateOfEntry)}</span></div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Personal Information</div>
            <div class="info-grid">
                <div class="info-item"><span class="label">Father's Name:</span> <span class="value">${employee.fatherName || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Mother's Name:</span> <span class="value">${employee.motherName || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Religion:</span> <span class="value">${employee.religion || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Community:</span> <span class="value">${employee.community || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Caste:</span> <span class="value">${employee.caste || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Personal ID Marks:</span> <span class="value">${employee.personalIdentificationMarks || 'Not specified'}</span></div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Contact Information</div>
            <div class="info-grid">
                <div class="info-item"><span class="label">Email:</span> <span class="value">${employee.email || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Present Address:</span> <span class="value">${this.formatAddressForPDF(employee, 'present')}</span></div>
                <div class="info-item"><span class="label">Permanent Address:</span> <span class="value">${this.formatAddressForPDF(employee, 'permanent')}</span></div>
                <div class="info-item"><span class="label">Native Place & Taluk:</span> <span class="value">${employee.nativeAndTaluk || employee.location || 'Not specified'}</span></div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Account Details</div>
            <div class="info-grid">
                <div class="info-item"><span class="label">Bank Account No:</span> <span class="value">${employee.bankAccountNo || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Bank Name:</span> <span class="value">${employee.bankName || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">IFSC Code:</span> <span class="value">${employee.ifscCode || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">PAN Number:</span> <span class="value">${employee.panNumber || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">UAN Number:</span> <span class="value">${employee.uanNumber || 'Not specified'}</span></div>
                <div class="info-item"><span class="label">Aadhar Number:</span> <span class="value">${employee.aadharNumber || 'Not specified'}</span></div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Educational Qualifications (${getArrayLength(employee.educationEnties)})</div>
            ${getArrayLength(employee.educationEnties) > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Qualification</th>
                            <th>Course Name</th>
                            <th>Institute Name</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employee.educationEnties.map((edu: any) => `
                            <tr>
                                <td>${edu.qualification || 'Not specified'}</td>
                                <td>${edu.courseName || 'Not specified'}</td>
                                <td>${edu.instituteName || 'Not specified'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<div class="no-data">No educational qualifications recorded</div>'}
        </div>

        <div class="section">
            <div class="section-title">Service History (${getArrayLength(employee.serviceEntries)})</div>
            ${getArrayLength(employee.serviceEntries) > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Details</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employee.serviceEntries.map((service: any) => `
                            <tr>
                                <td>${formatDate(service.date)}</td>
                                <td>${service.type || 'Not specified'}</td>
                                <td>
                                    ${service.appointmentType ? `Appointment: ${service.appointmentType}<br>` : ''}
                                    ${service.fromDesignation ? `From: ${service.fromDesignation}<br>` : ''}
                                    ${service.toPromoted ? `To: ${service.toPromoted}<br>` : ''}
                                    ${service.fromPlace ? `From: ${service.fromPlace}<br>` : ''}
                                    ${service.toPlace ? `To: ${service.toPlace}<br>` : ''}
                                    ${service.punishmentType ? `Punishment: ${service.punishmentType}<br>` : ''}
                                    ${service.caseDetails ? `Case: ${service.caseDetails}` : ''}
                                </td>
                                <td>${service.status || 'Active'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<div class="no-data">No service history recorded</div>'}
        </div>

        <div class="section">
            <div class="section-title">Training Details (${getArrayLength(employee.trainingEntries)})</div>
            ${getArrayLength(employee.trainingEntries) > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Training Type</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employee.trainingEntries.map((training: any) => `
                            <tr>
                                <td>${training.trainingType || 'Not specified'}</td>
                                <td>${formatDate(training.trainingDate)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<div class="no-data">No training details recorded</div>'}
        </div>

        <div class="section">
            <div class="section-title">Punishment Details (${getArrayLength(employee.punishmentEntries)})</div>
            ${getArrayLength(employee.punishmentEntries) > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Punishment Type</th>
                            <th>Date</th>
                            <th>Case Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employee.punishmentEntries.map((punishment: any) => `
                            <tr>
                                <td>${punishment.punishmentType || 'Not specified'}</td>
                                <td>${formatDate(punishment.punishmentDate)}</td>
                                <td>${punishment.caseDetails || 'Not specified'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<div class="no-data">No punishment details recorded</div>'}
        </div>

        <div class="section">
            <div class="section-title">Nomination Details (${getArrayLength(employee.nominationEntries)})</div>
            ${getArrayLength(employee.nominationEntries) > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Nominee Name</th>
                            <th>Relationship</th>
                            <th>Age</th>
                            <th>Share %</th>
                            <th>Address</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employee.nominationEntries.map((nominee: any) => `
                            <tr>
                                <td>${nominee.nomineeName || 'Not specified'}</td>
                                <td>${nominee.relationship || 'Not specified'}</td>
                                <td>${nominee.age || 'Not specified'}</td>
                                <td>${nominee.percentageOfShare || 'Not specified'}%</td>
                                <td>${nominee.address || 'Not specified'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<div class="no-data">No nomination details recorded</div>'}
        </div>

        <div class="section">
            <div class="section-title">Leave Balance (${getArrayLength(employee.leaveEntries)})</div>
            ${getArrayLength(employee.leaveEntries) > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th>Opening Balance</th>
                            <th>Closing Balance</th>
                            <th>Entry Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employee.leaveEntries.map((leave: any) => `
                            <tr>
                                <td>${leave.leaveType || 'Not specified'}</td>
                                <td>${leave.openingBalance || 0}</td>
                                <td>${leave.closingBalance || 0}</td>
                                <td>${formatDate(leave.entryDate)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<div class="no-data">No leave balance recorded</div>'}
        </div>

        ${employee.remarks ? `
        <div class="section">
            <div class="section-title">Remarks</div>
            <div class="value">${employee.remarks}</div>
        </div>
        ` : ''}

        <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
            Generated on: ${new Date().toLocaleString()}<br>
            This is a system-generated document from TNCSC Employee Management System
        </div>
    </body>
    </html>
    `;
  }

  private downloadAsHTML(content: string): void {
    const blob = new Blob([content], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Employee_${this.selectedEmployee?.employeeName || 'Details'}_${new Date().toISOString().split('T')[0]}.html`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // Helper method to generate profile photo URL
  private generateProfilePhotoUrl(apiData: any): string {
    // Check multiple possible locations for profile photo
    const profilePhoto = apiData.profilePhoto || apiData.profile?.profilephoto || apiData.profile?.profilePhoto;

    console.log('Profile photo path from API:', profilePhoto);

    if (profilePhoto && profilePhoto.trim() !== '') {
      const baseUrl = 'http://localhost:8082';

      if (profilePhoto.startsWith('http')) {
        console.log('Profile photo is already a full URL:', profilePhoto);
        return profilePhoto;
      } else if (profilePhoto.startsWith('/api/')) {
        const fullUrl = `${baseUrl}${profilePhoto}`;
        console.log('Profile photo URL constructed:', fullUrl);
        return fullUrl;
      } else if (profilePhoto.startsWith('/')) {
        const fullUrl = `${baseUrl}${profilePhoto}`;
        console.log('Profile photo URL constructed:', fullUrl);
        return fullUrl;
      } else {
        // Ensure path starts with /
        const fullUrl = `${baseUrl}/${profilePhoto}`;
        console.log('Profile photo URL constructed:', fullUrl);
        return fullUrl;
      }
    }

    console.log('No profile photo found in API data');
    return '';
  }

  // Load employee profile photo (similar to view component)
  loadEmployeeProfilePhoto(employeeId: string): void {
    console.log('Loading profile photo for employee ID:', employeeId);

    // Check if we already have a valid profile photo URL from mapping
    if (this.selectedEmployee?.profilePhotoUrl && this.selectedEmployee.profilePhotoUrl.trim() !== '') {
      console.log('Profile photo URL already set from mapping:', this.selectedEmployee.profilePhotoUrl);
      return;
    }

    // Check multiple possible locations for profile photo
    const profilePhotoPath = this.selectedEmployee?.profilePhoto ||
                            this.selectedEmployee?.profile?.profilephoto ||
                            this.selectedEmployee?.profile?.profilePhoto;

    if (profilePhotoPath) {
      console.log('Profile photo found in employee data:', profilePhotoPath);

      // Construct the full URL for the profile photo
      const baseUrl = 'http://localhost:8082';

      // Handle both absolute and relative paths
      let fullPhotoUrl: string;
      if (profilePhotoPath.startsWith('http')) {
        fullPhotoUrl = profilePhotoPath;
      } else if (profilePhotoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${profilePhotoPath}`;
      } else if (profilePhotoPath.startsWith('/')) {
        fullPhotoUrl = `${baseUrl}${profilePhotoPath}`;
      } else {
        // Add /api prefix if path doesn't start with /
        fullPhotoUrl = `${baseUrl}/api/${profilePhotoPath}`;
      }

      console.log('Full profile photo URL constructed:', fullPhotoUrl);
      (this.selectedEmployee as any).profilePhotoUrl = fullPhotoUrl;
      return;
    }

    // Fallback: Call API to get employee profile photo from document service
    console.log('No profile photo in profile object, trying document service...');
    this.http.get(`http://localhost:8082/document/photo/${employeeId}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log('Profile photo loaded successfully from document service');
          // Create blob URL for the image
          const photoUrl = window.URL.createObjectURL(blob);
          if (this.selectedEmployee) {
            (this.selectedEmployee as any).profilePhotoUrl = photoUrl;
          }
        },
        error: (error) => {
          console.log('No profile photo found in document service or error loading photo:', error);
          // Set profilePhotoUrl to null if no photo is found
          if (this.selectedEmployee) {
            (this.selectedEmployee as any).profilePhotoUrl = null;
          }
        }
      });
  }

  // Helper methods for the modal template
  getFormattedPresentAddress(): string {
    if (!this.selectedEmployee) return 'Not provided';

    const profile = this.selectedEmployee.profile;
    if (profile && (profile.presentDoorNo || profile.presentBuildingName || profile.presentStreetAddress || profile.presentCity)) {
      const parts = [
        profile.presentDoorNo,
        profile.presentBuildingName,
        profile.presentStreetAddress,
        profile.presentCity,
        profile.presentPincode
      ].filter(part => part && part.trim() !== '');

      return parts.length > 0 ? parts.join(', ') : (this.selectedEmployee.presentAddress || 'Not provided');
    }

    // Check for structured fields at root level (for newer data format)
    if (this.selectedEmployee.presentDoorNo || this.selectedEmployee.presentBuildingName ||
        this.selectedEmployee.presentStreetAddress || this.selectedEmployee.presentCity) {
      const parts = [
        this.selectedEmployee.presentDoorNo,
        this.selectedEmployee.presentBuildingName,
        this.selectedEmployee.presentStreetAddress,
        this.selectedEmployee.presentCity,
        this.selectedEmployee.presentPincode
      ].filter(part => part && part.trim() !== '');

      return parts.length > 0 ? parts.join(', ') : (this.selectedEmployee.presentAddress || 'Not provided');
    }

    return this.selectedEmployee.presentAddress || 'Not provided';
  }

  getFormattedPermanentAddress(): string {
    if (!this.selectedEmployee) return 'Not provided';

    const profile = this.selectedEmployee.profile;
    if (profile && (profile.permanentDoorNo || profile.permanentBuildingName || profile.permanentStreetAddress || profile.permanentCity)) {
      const parts = [
        profile.permanentDoorNo,
        profile.permanentBuildingName,
        profile.permanentStreetAddress,
        profile.permanentCity,
        profile.permanentPincode
      ].filter(part => part && part.trim() !== '');

      return parts.length > 0 ? parts.join(', ') : (this.selectedEmployee.permanentAddress || 'Not provided');
    }

    // Check for structured fields at root level (for newer data format)
    if (this.selectedEmployee.permanentDoorNo || this.selectedEmployee.permanentBuildingName ||
        this.selectedEmployee.permanentStreetAddress || this.selectedEmployee.permanentCity) {
      const parts = [
        this.selectedEmployee.permanentDoorNo,
        this.selectedEmployee.permanentBuildingName,
        this.selectedEmployee.permanentStreetAddress,
        this.selectedEmployee.permanentCity,
        this.selectedEmployee.permanentPincode
      ].filter(part => part && part.trim() !== '');

      return parts.length > 0 ? parts.join(', ') : (this.selectedEmployee.permanentAddress || 'Not provided');
    }

    return this.selectedEmployee.permanentAddress || 'Not provided';
  }

  private formatAddressForPDF(employee: any, type: 'present' | 'permanent'): string {
    if (!employee) return 'Not provided';

    const profile = employee.profile;

    // Get address parts based on type
    let addressParts: string[] = [];

    if (profile) {
      addressParts = type === 'present'
        ? [
            profile.presentDoorNo,
            profile.presentBuildingName,
            profile.presentStreetAddress,
            profile.presentCity,
            profile.presentPincode
          ]
        : [
            profile.permanentDoorNo,
            profile.permanentBuildingName,
            profile.permanentStreetAddress,
            profile.permanentCity,
            profile.permanentPincode
          ];
    } else {
      // Check for structured fields at root level
      addressParts = type === 'present'
        ? [
            employee.presentDoorNo,
            employee.presentBuildingName,
            employee.presentStreetAddress,
            employee.presentCity,
            employee.presentPincode
          ]
        : [
            employee.permanentDoorNo,
            employee.permanentBuildingName,
            employee.permanentStreetAddress,
            employee.permanentCity,
            employee.permanentPincode
          ];
    }

    // Filter out empty/null values
    const filteredParts = addressParts.filter(part => part && part.trim() !== '' && part.trim() !== 'null');

    if (filteredParts.length > 0) {
      return filteredParts.join(', ');
    }

    // Fallback to legacy address fields
    return employee[`${type}Address`] || 'Not provided';
  }

  // Image handling methods
  onImageError(event: any): void {
    console.log('Profile image failed to load:', event);
    // You can set a default image here if needed
  }

  onImageLoad(event: any): void {
    console.log('Profile image loaded successfully:', event);
  }

  // Nominee photo handling methods (matching view component)
  getNomineePhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      console.log(`No nominee found at index ${nomineeIndex}`);
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // Check if nominee already has a processed photo URL
    if ((nominee as any).nomineePhotoUrl) {
      console.log(`Nominee ${nomineeIndex + 1} already has processed URL:`, (nominee as any).nomineePhotoUrl);
      return (nominee as any).nomineePhotoUrl;
    }

    // Check if nominee has a photo field
    if ((nominee as any).nomineePhoto &&
        (nominee as any).nomineePhoto !== 'pending_upload' &&
        (nominee as any).nomineePhoto.trim() !== '') {

      const baseUrl = 'http://localhost:8082';
      const photoPath = (nominee as any).nomineePhoto;

      console.log(`Nominee ${nomineeIndex + 1} raw photo path:`, photoPath);

      // Use the same logic as profile photo (exactly matching view component logic)
      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      console.log(`Nominee ${nomineeIndex + 1} constructed full photo URL:`, fullPhotoUrl);

      // Store the processed URL for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      (nominee as any).photoLoadingComplete = true;
      return fullPhotoUrl;
    }

    console.log(`Nominee ${nomineeIndex + 1} has no valid photo path, trying document service...`);
    // If no photo path in nominee data, try to load from document service
    this.loadNomineePhotoFromDocuments(nomineeIndex);
    return null;
  }

  // Simple method to get nominee photo URL for template use (matching view component)
  getNomineeDisplayPhotoUrl(nomineeIndex: number): string | null {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return null;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // If already processed, return it
    if ((nominee as any).nomineePhotoUrl) {
      return (nominee as any).nomineePhotoUrl;
    }

    // If has raw photo path, construct URL immediately
    if (nominee.nomineePhoto && nominee.nomineePhoto !== 'pending_upload' && nominee.nomineePhoto.trim() !== '') {
      const baseUrl = 'http://localhost:8082';
      const photoPath = nominee.nomineePhoto;

      let fullPhotoUrl: string;
      if (photoPath.startsWith('http')) {
        fullPhotoUrl = photoPath;
      } else if (photoPath.startsWith('/api/')) {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      } else {
        fullPhotoUrl = `${baseUrl}${photoPath}`;
      }

      // Store for future use
      (nominee as any).nomineePhotoUrl = fullPhotoUrl;
      return fullPhotoUrl;
    }

    return null;
  }

  onNomineeImageError(event: any, nomineeIndex: number): void {
    console.log(`Error loading nominee ${nomineeIndex + 1} photo:`, event);

    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];

    // Try to reload from document service if the current URL failed
    if (!(nominee as any).documentServiceTried) {
      (nominee as any).documentServiceTried = true;
      this.loadNomineePhotoFromDocuments(nomineeIndex);
    } else {
      // Hide the image and mark as no photo available
      event.target.style.display = 'none';
      (nominee as any).nomineePhotoUrl = null;
      (nominee as any).photoLoadingComplete = true;
    }
  }

  onNomineeImageLoad(nomineeIndex: number): void {
    console.log(`Nominee ${nomineeIndex + 1} image loaded successfully`);
    if (this.selectedEmployee?.nominationEntries && this.selectedEmployee.nominationEntries[nomineeIndex]) {
      const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
      (nominee as any).photoLoadingComplete = true;
    }
  }

  // View nominee photo in larger size
  viewNomineePhoto(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const photoUrl = (nominee as any).nomineePhotoUrl || this.getNomineeDisplayPhotoUrl(nomineeIndex);

    if (photoUrl) {
      // Open photo in new window/tab
      window.open(photoUrl, '_blank');
    } else {
      console.log('No photo available for nominee', nomineeIndex + 1);
    }
  }

  // Initialize nominee photo loading for all nominees (similar to view component)
  initializeNomineePhotos(): void {
    if (!this.selectedEmployee?.nominationEntries) {
      console.log('No nomination entries found for photo initialization');
      return;
    }

    console.log('Initializing nominee photos for', this.selectedEmployee.nominationEntries.length, 'nominees');
    console.log('Nominee entries:', this.selectedEmployee.nominationEntries);

    this.selectedEmployee.nominationEntries.forEach((nominee, index) => {
      console.log(`Initializing photo for nominee ${index + 1}:`, nominee);

      // Mark as loading initially only if photo URL is not already processed
      if (!(nominee as any).nomineePhotoUrl) {
        (nominee as any).photoLoadingComplete = false;

        // Try to get photo URL - this will trigger loading if needed
        setTimeout(() => {
          const photoUrl = this.getNomineePhotoUrl(index);
          console.log(`Photo URL result for nominee ${index + 1}:`, photoUrl);
        }, 50 * index); // Stagger the requests slightly
      } else {
        console.log(`Nominee ${index + 1} already has photo URL:`, (nominee as any).nomineePhotoUrl);
        (nominee as any).photoLoadingComplete = true;
      }
    });
  }

  // Load nominee photo from document service
  loadNomineePhotoFromDocuments(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const employeeId = this.selectedEmployee.id || this.selectedEmployee.empId;

    console.log(`Loading nominee ${nomineeIndex + 1} photo from document service for employee ID:`, employeeId);

    // Try to get nominee photo from document service
    this.http.get(`http://localhost:8082/document/nominee-photo/${employeeId}/${nomineeIndex}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`Nominee ${nomineeIndex + 1} photo loaded successfully from document service`);
          // Create blob URL for the image
          const photoUrl = window.URL.createObjectURL(blob);
          (nominee as any).nomineePhotoUrl = photoUrl;
          (nominee as any).photoLoadingComplete = true;
        },
        error: (error) => {
          console.log(`No nominee ${nomineeIndex + 1} photo found in document service or error loading photo:`, error);
          // Try alternative endpoint or set to null
          this.tryAlternativeNomineePhotoEndpoint(nomineeIndex);
        }
      });
  }

  // Try alternative nominee photo endpoint
  tryAlternativeNomineePhotoEndpoint(nomineeIndex: number): void {
    if (!this.selectedEmployee?.nominationEntries || !this.selectedEmployee.nominationEntries[nomineeIndex]) {
      return;
    }

    const nominee = this.selectedEmployee.nominationEntries[nomineeIndex];
    const employeeId = this.selectedEmployee.id || this.selectedEmployee.empId;

    // Try alternative endpoint pattern
    this.http.get(`http://localhost:8082/document/photo/nominee/${employeeId}/${nomineeIndex}`, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`Nominee ${nomineeIndex + 1} photo loaded from alternative endpoint`);
          const photoUrl = window.URL.createObjectURL(blob);
          (nominee as any).nomineePhotoUrl = photoUrl;
          (nominee as any).photoLoadingComplete = true;
        },
        error: (error) => {
          console.log(`No nominee ${nomineeIndex + 1} photo found in alternative endpoint:`, error);
          // Set to null if no photo is found anywhere
          (nominee as any).nomineePhotoUrl = null;
          (nominee as any).photoLoadingComplete = true;
        }
      });
  }

  // PDF and file handling methods
  viewPDFs(employee: any): void {
    this.selectedEmployee = employee;
    console.log('Fetching documents for employee ID:', employee.id);

    if (!employee.id) {
      console.error('No employee ID found for document fetch');
      this.selectedEmployeePDFs = [];
      return;
    }

    // Call API to get employee documents
    this.http.get<any[]>(`http://localhost:8082/documents/employee/${employee.id}`)
      .subscribe({
        next: (response) => {
          console.log('Documents received:', response);
          this.selectedEmployeePDFs = this.mapDocumentsToFiles(response);
        },
        error: (error) => {
          console.error('Error fetching documents:', error);
          // Fallback to empty array if API fails
          this.selectedEmployeePDFs = [];
          alert('Could not load documents for this employee.');
        }
      });
  }

  private mapDocumentsToFiles(documents: any[]): any[] {
    // Filter out photos and signatures, keep only PDF documents
    const pdfDocuments = documents.filter(doc => {
      const fileName = doc.fileName?.toLowerCase() || '';
      const fileType = doc.fileType?.toLowerCase() || '';

      // Exclude photos and signatures
      const isPhotoOrSignature = fileName.includes('photo') || fileName.includes('signature') ||
                                fileName.includes('sign') || fileName.includes('pic');

      // Only include PDF files
      const isPdf = fileType.includes('pdf') || fileName.endsWith('.pdf');

      // Also check if the file type is an image type
      const isImageType = fileType.startsWith('image/');

      return isPdf && !isPhotoOrSignature && !isImageType;
    });

    return pdfDocuments.map(doc => ({
      id: doc.id,
      employeeId: doc.employeeId,
      employeeName: doc.employeeName,
      name: doc.fileName,
      type: doc.fileType,
      size: doc.fileSize,
      filePath: doc.filePath,
      fileUrl: doc.fileUrl,
      uploadDate: doc.uploadDate,
      downloadUrl: `http://localhost:8082${doc.fileUrl}`
    }));
  }

  openPDF(file: any): void {
    console.log('Opening PDF:', file);

    if (file.downloadUrl) {
      // Open the actual PDF file from the server
      window.open(file.downloadUrl, '_blank');
    } else {
      // Fallback for old File objects
      alert(`Opening PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would open the PDF file.`);
    }
  }

  downloadPDF(file: any): void {
    console.log('Downloading PDF:', file);

    if (file.downloadUrl) {
      // Use HttpClient to download the file as blob
      this.http.get(file.downloadUrl, { responseType: 'blob' })
        .subscribe({
          next: (blob) => {
            // Create blob URL and force download
            const blobUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = file.name;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL
            setTimeout(() => {
              window.URL.revokeObjectURL(blobUrl);
            }, 100);

            console.log('File downloaded successfully:', file.name);
          },
          error: (error) => {
            console.error('Error downloading file:', error);
            alert('Error downloading file. Please try again.');
          }
        });
    } else {
      // Fallback for old File objects
      alert(`Downloading PDF: ${file.name}\nSize: ${this.formatFileSize(file.size)}\n\nIn a real application, this would download the PDF file.`);
    }
  }

  isDownloading(_fileId: number): boolean {
    return false; // Simplified for now, you can implement download tracking if needed
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
