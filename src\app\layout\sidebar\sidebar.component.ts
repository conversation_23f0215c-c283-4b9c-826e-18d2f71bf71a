import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-sidebar',
  imports: [RouterModule, CommonModule, ReactiveFormsModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent implements OnInit{

  role: string | null = null;

  ngOnInit(): void {
    this.role = sessionStorage.getItem('role');
    console.log('Role:', this.role);
  }


  get isAdmin(){
    console.log(this.role);
    return this.role === 'ADMIN';
    
  }

get isOperator(){
    console.log('isOperator check:', this.role === 'OPERATOR');
    return this.role === 'OPERATOR'; 
  }

  get isSUPERINTENDENTorARM(){
    console.log('isSUPERINTENDENT check:', this.role === 'SUPERINTENDENT');
    return this.role === 'SUPERINTENDENT' || this.role === 'AM'; 
  }

  get isDRMandMA(){
    console.log('isDRMandMA check:', this.role === 'DRM' || this.role === 'MA');
    return this.role === 'DRM' || this.role === 'MA'; 
  }

  get isRM(){
    console.log('isRM check:', this.role === 'RM');
    return this.role === 'RM'; 
  }

}
