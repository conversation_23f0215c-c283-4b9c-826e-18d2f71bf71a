export interface LeaveEntry {
  leaveType: string;
  leaveBalanceCount: number;
}

export interface ServiceEntry {
  date: string;
  type: string;
  status: string;
}

export interface SalaryDetails {
  lastSalaryRevisedDate: string;
  group: string;
  payband: string;
  gradepay: string;
}

export interface Profile {
  id?: number;
  fatherName?: string;
  motherName?: string;
  dateOfBirth?: string;
  community?: string;
  caste?: string;
  district?: string;
  nativeplaceandtaluk?: string;
  presentDoorNo?: string;
  presentBuildingName?: string;
  presentStreetAddress?: string;
  presentCity?: string;
  presentPincode?: string;
  permanentDoorNo?: string;
  permanentBuildingName?: string;
  permanentStreetAddress?: string;
  permanentCity?: string;
  permanentPincode?: string;
  profilephoto?: string;
  profilePhoto?: string;
  email?: string;
  gender?: string;
  mobileNumber?: string;
  // Legacy address fields for backward compatibility
  presentaddress?: string;
  permanentaddress?: string;
}

export interface Employee {
  remarks: any;
  rejectedBy: any;
  id: number;
  status: string;
  district: String;
  ecpfNumber: string;
  motherName: string;
  email: string;
  gender?: string;
  mobileNumber?: string;
  presentAddress: string;
  permanentAddress: string;
  bankAccountNo: string;
  ifscCode: string;
  bankName: string;
  panNumber: string;
  uanNumber: string;
  aadharNumber: string;
  serviceRecords: any[];
  registerNumber: string;
  personalIdMark1: string;
  personalIdMark2: string;
  nativeAndTaluk: string;
  createdAt: string;
  dateOfEntry: string;
  trainingEntries: any[];
  punishmentEntries: any[];
  nominationEntries: any[];
  educationEnties: any[];
  location: string;
  empId: string;
  employeeName: string;
  fatherName: string;
  dateOfBirth: string;
  religion: string;
  community: string;
  personalIdentificationMarks: string;
  Authority: string;
  caste: string;
  designation: string;
  dateOfEntryIntoService: string;
  educationalQualification: string;
  leaveEntries: LeaveEntry[]; // Array of leave entries
  serviceEntries: ServiceEntry[]; // Array of service entries
  salaryDetails?: SalaryDetails; // Optional salary details
  profile?: Profile; // Profile object containing additional details including profile photo
  profilePhotoUrl?: string; // Optional profile photo URL
  profilePhoto?: string; // Root level profile photo path from API
  employeePhoto?: string; // Employee photo URL
  educationEntries?: any[]; // Array of education entries
  technicalQualificationEntries?: any[]; // Array of technical qualification entries
}
