import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { UserserviceService } from '../../services/userservice.service';
import { User } from '../../bean/User';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-user-list',
  imports: [CommonModule,ReactiveFormsModule],
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.css'
})
export class UserListComponent implements OnInit {
  userList: User[] = [];
  users: any[] = [];

  // Pagination properties
  pageSize: number = 10;
  start: number = 0; // This will store the 0-based page number for tracking
  currentPage: number = 1; // This is the 1-based page number for UI display
  totalRecords: number = 0;
  totalPages: number = 0;

  // Loading state
  isLoading: boolean = false;

  // Make Math available in template
  Math = Math;

  constructor(private userService: UserserviceService, private cdr: ChangeDetectorRef) {
    // Fetch user data from the service
  }

  ngOnInit(): void {
    this.getList();
  }

  getList() {
    this.isLoading = true;

    // Backend expects start as PAGE NUMBER (0-based), not record offset
    // Page 1 -> start = 0, Page 2 -> start = 1, Page 3 -> start = 2, etc.
    const pageNumber = this.currentPage - 1; // Convert to 0-based page number

    console.log('Fetching users:', {
      currentPage: this.currentPage,
      pageSize: this.pageSize,
      pageNumber: pageNumber,
      apiCall: `allList?pagesize=${this.pageSize}&start=${pageNumber}`
    });

    this.userService.listByPage(pageNumber, this.pageSize).subscribe({
      next: (res: { status: boolean; totalRecords: number; data: User[] }) => {
        console.log('API Response:', res);

        this.userList = Array.isArray(res.data) ? res.data : [];
        this.totalRecords = res.totalRecords || 0;
        this.totalPages = this.totalRecords > 0 ? Math.ceil(this.totalRecords / this.pageSize) : 0;

        // Update the start value for tracking
        this.start = pageNumber;

        console.log('Pagination Info:', {
          userListLength: this.userList.length,
          totalRecords: this.totalRecords,
          totalPages: this.totalPages,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
          pageNumber: this.start
        });

        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Error fetching user list:', err);
        this.userList = [];
        this.totalRecords = 0;
        this.totalPages = 0;
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }
  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage && !this.isLoading) {
      this.currentPage = page;
      // The getList() method will calculate the correct startOffset
      this.getList();
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  // Generate array of page numbers for pagination display
  getPageNumbers(): number[] {
    if (this.totalPages <= 0) {
      return [];
    }

    const pages: number[] = [];
    const maxPagesToShow = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // Method to change page size
  onPageSizeChange(newPageSize: number): void {
    if (newPageSize !== this.pageSize && !this.isLoading) {
      this.pageSize = newPageSize;
      this.currentPage = 1; // Reset to first page
      // The getList() method will calculate the correct startOffset (which will be 0 for page 1)
      this.getList();
    }
  }

  // Get current page info for display
  getCurrentPageInfo(): string {
    if (this.totalRecords === 0) {
      return 'No users found';
    }

    const startRecord = (this.currentPage - 1) * this.pageSize + 1;
    const endRecord = Math.min(this.currentPage * this.pageSize, this.totalRecords);
    return `Showing ${startRecord} to ${endRecord} of ${this.totalRecords} users`;
  }

  onDelete(id: any) {
    if (this.isLoading) {
      return; // Prevent deletion while loading
    }

    if (confirm('Are you sure you want to delete this user?')) {
      this.userService.deleteUser(id).subscribe({
        next: (res) => {
          console.log('User deleted successfully:', res);
          // Refresh the current page data
          this.getList();
        },
        error: (err) => {
          console.error('Error deleting user:', err);
          alert('Failed to delete user. Please try again.');
        }
      });
    }
  }
}
