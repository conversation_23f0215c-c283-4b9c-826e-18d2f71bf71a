import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { environment } from '../../../environments/environment';
import { DashboardService } from '../../services/dashboard.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

export interface StatusCount {
  status: string;
  count: number;
}
@Component({
  selector: 'app-dashboard-total',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './dashboard-total.component.html',
  styleUrl: './dashboard-total.component.css'
})
export class DashboardTotalComponent implements OnInit{
// totalDocuments = 150;
// approvedDocuments = 80;
// rejectedDocuments = 30;
// pendingDocuments = 40;
totalAdmins = 5;
totalOperators = 10;
employee:any=0;
document: any = 0;
totalDocuments:number=0;
approvedDocuments: number = 0;
  rejectedDocuments: number = 0;
  pendingDocuments: number = 0;
  admin:any=0;
  operator:any=0;

  constructor(private dashboardService: DashboardService) {

   }
  ngOnInit(): void {
    this.getDocumentCount();
    this.totalAdmin();
    this.totalOperatorCount();
    this.getStatus();
  }

   getDocumentCount(){
    this.dashboardService.getDocumentCount().subscribe((data: any) => {
        this.document = data;

    });
   }

   getEmployee(){
     this.dashboardService.getDocumentCount().subscribe((data: any) => {
        this.employee = data;

    });

   }

   getStatus(){
      this.dashboardService.getStatusCount().subscribe((data: StatusCount[]) => {
      data.forEach((item) => {
        const status = item.status.toLowerCase();
        if (status === 'approved') this.approvedDocuments = item.count;
        else if (status === 'rejected') this.rejectedDocuments = item.count;
        else if (status === 'pending') this.pendingDocuments = item.count;
      });
    });
  }


   totalAdmin(){
     this.dashboardService.countAdmin().subscribe((data: any) => {
        this.admin = data;

    });


   }

    totalOperatorCount(){
      this.dashboardService.countOperator().subscribe((data: any) => {
        this.operator = data;

    });
    }

}
