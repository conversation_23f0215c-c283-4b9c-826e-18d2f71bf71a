.th-color {
background-color: #203664;
color: white;
}

/* PDF Modal Styles */
.pdf-card {
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  border: 1px solid #dee2e6;
}

.pdf-card:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-color: #007bff;
}

.pdf-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.pdf-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

#pdfModal .modal-header {
  background-color: #203664;
  color: white;
}

#pdfModal .modal-header .close {
  color: white;
  opacity: 0.8;
}

#pdfModal .modal-header .close:hover {
  opacity: 1;
}

/* Nominee Photo Styles */
.nominee-photo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.nominee-photo-thumbnail {
  border-radius: 4px;
  border: 2px solid #dee2e6;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nominee-photo-thumbnail:hover {
  border-color: #007bff;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.no-nominee-photo {
  color: #6c757d;
  text-align: center;
  font-size: 0.8rem;
}

.no-nominee-photo i {
  color: #adb5bd;
}

.btn-sm {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Service History Accordion Styles */
.accordion-item {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

.accordion-button {
  background-color: #f8f9fa;
  border: none;
  padding: 1rem;
  font-weight: 500;
}

.accordion-button:not(.collapsed) {
  background-color: #203664;
  color: white;
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: 0 0 0 0.25rem rgba(32, 54, 100, 0.25);
  border-color: #203664;
}

.accordion-body {
  padding: 1.5rem;
  background-color: #ffffff;
}

.accordion-body p {
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.accordion-body strong {
  color: #495057;
  font-weight: 600;
}

/* Service Type Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

.badge.bg-success {
  background-color: #28a745 !important;
}

.badge.bg-primary {
  background-color: #007bff !important;
}

.badge.bg-info {
  background-color: #17a2b8 !important;
}

.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
}

.badge.bg-danger {
  background-color: #dc3545 !important;
}

.badge.bg-dark {
  background-color: #343a40 !important;
}

.badge.bg-light {
  background-color: #f8f9fa !important;
  border: 1px solid #dee2e6;
}

/* Service History Card Header */
.card-header h6 {
  font-weight: 600;
  font-size: 1.1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .accordion-body .row .col-md-6 {
    margin-bottom: 1rem;
  }

  .accordion-button {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .accordion-body {
    padding: 1rem;
  }
}
