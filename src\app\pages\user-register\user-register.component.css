/* .form-container {
  max-width: 90%;
  margin: 40px auto;
  padding: 30px;
  border-radius: 16px;
  background-color: #fff;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
} */
 .form-container {
  width: 90%;
  margin-left: 40px;
  margin-top: 40px;
  padding: 24px;
  border: 1px solid #ccc;
  border-radius: 16px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
  background-color: #fefefe;
}

.form-label {
  font-weight: 500;
}

.btn-outline-secondary i {
  font-size: 1rem;
}

input.ng-invalid.ng-touched,
select.ng-invalid.ng-touched {
  border-color: #dc3545;
}

button.btn-sm {
  font-size: .975rem;
  padding: 8px 16px;
}

