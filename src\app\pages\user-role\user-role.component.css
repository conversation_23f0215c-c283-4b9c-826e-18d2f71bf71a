.form-container {
  width: 90%;
  margin-left: 40px;
  margin-top: 40px;
  padding: 24px;
  border: 1px solid #ccc;
  border-radius: 16px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
  background-color: #fefefe;
}

h2 {
  margin-bottom: 16px;
  color: #333;
}

.form-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;   /* everything lines up from the left edge   */
}

label {
  font-weight: bold;
  margin-bottom: 6px;
}

input {
  width: 250px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
}

.error {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

.button-container {
  margin-top: 8px;   /* gap below the textbox            */
  margin-left: 170px;/* 100 px (textbox width) + 10 px   */
  display: flex;
  justify-content: flex-start;
}

button {
  padding: 10px 16px;
  background-color: #1976d2;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color .3s;
}

button:hover {
  background-color: #145ea8;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
