<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document with Footer</title>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* Prevent scroll bars */
        }

        body {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .content {
            flex: 1;
            overflow-y: auto; /* Allow internal scrolling if content exceeds viewport */
            padding: 20px;
            box-sizing: border-box;
            margin-bottom: 60px; /* Adjust based on footer height */
        }

        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #f8f9fa;
            padding: 10px;
            height: 50px; /* Set a fixed height for the footer */
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>

    <footer class="footer">
        <p class="footer-text mb-0">
            Developed By <a href="#" target="_blank">Eagle Software India</a>
        </p>
        <p class="footer-text mb-0 text-muted">
            Version: 1.0
        </p>
        <p class="footer-text mb-0">
            Copyright © <span id="currentYear"></span>
            <a href="#" target="_blank">TNCSC</a>. All rights reserved
        </p>
    </footer>

    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</html> -->
