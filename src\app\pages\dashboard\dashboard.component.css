.wide-card {
  width: 100%;
  margin: 0 auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: none;
}

/* Service Entry Container Styles */
.service-entry-container {
  transition: all 0.3s ease;
}

.service-entry-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.border-left-primary {
  border-left: 4px solid #203664 !important;
}

.card-header.bg-light {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #dee2e6;
}

/* Location & Service Information Styles */
.location-service-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-header {
  border-bottom: 2px solid #203664;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.section-header i {
  color: #203664;
  font-size: 1.2em;
}

/* Form Enhancement Styles */
.form-control:focus {
  border-color: #203664;
  box-shadow: 0 0 0 0.2rem rgba(32, 54, 100, 0.25);
}

.required-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 5px;
  display: block;
}

.required-label i {
  color: #203664;
  margin-right: 4px;
}

/* Alert Styles */
.alert {
  border-radius: 6px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-primary {
  background-color: rgba(32, 54, 100, 0.1);
  color: #203664;
  border-left: 4px solid #203664;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: #155724;
  border-left: 4px solid #28a745;
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.1);
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #856404;
  border-left: 4px solid #ffc107;
}

.alert-secondary {
  background-color: rgba(108, 117, 125, 0.1);
  color: #495057;
  border-left: 4px solid #6c757d;
}

/* Button Enhancements */
.btn-group .btn {
  border-radius: 4px;
  margin-left: 2px;
}

.btn-outline-success:hover {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Card Enhancements */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* Select Dropdown Enhancements */
select.form-control option {
  padding: 8px 12px;
}

/* File Input Enhancements */
.form-control[type="file"].file-selected::-webkit-file-upload-button {
  display: none;
}

.form-control[type="file"].file-selected::before {
  content: '';
  display: inline-block;
  background: transparent;
  border: none;
  outline: none;
  white-space: nowrap;
  user-select: none;
  cursor: pointer;
}

.form-control[type="file"].file-selected {
  color: transparent;
}

.form-control[type="file"].file-selected::-webkit-file-upload-button {
  visibility: hidden;
}

.form-control[type="file"].file-selected::after {
  content: '';
  display: inline-block;
  background: transparent;
}

/* Firefox */
.form-control[type="file"].file-selected::-moz-file-upload-button {
  display: none;
}

/* Multi-select dropdown fixes */
.multi-select-parent-container {
  position: relative;
}

/* Ensure cards don't interfere with dropdown z-index */
.card {
  position: relative;
  z-index: 1;
}

/* Ensure the dropdown appears above all cards */
.multi-select-dropdown.open {
  z-index: 999999 !important;
}

/* Override Bootstrap card z-index */
.card {
  z-index: auto !important;
}

/* Ensure form sections don't create stacking contexts */
.card-body {
  position: relative;
  z-index: auto;
}

/* Force dropdown content to appear on top */
app-multi-select-dropdown .multi-select-dropdown-content {
  z-index: 999999 !important;
  position: absolute !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-entry-container .card-header .btn-group {
    margin-top: 10px;
  }

  .service-entry-container .card-header .row {
    flex-direction: column;
  }

  .service-entry-container .card-header .text-end {
    text-align: left !important;
  }
}