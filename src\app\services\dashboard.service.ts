import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment.development';
import { Observable } from 'rxjs';
export interface StatusCount {
  status: string;
  count: number;
}
@Injectable({
  providedIn: 'root'
})
export class DashboardService {
baseURL:string
  constructor(private http: HttpClient) {

    this.baseURL= environment.DASHBOARD;
   }

   countAdmin(): Observable<any> {
     return this.http.get<any>(`${this.baseURL}/admin-counts`);
   }

   countOperator(): Observable<any> {
     return this.http.get<any>(`${this.baseURL}/operator-counts`);
   }

   getDocumentCount():Observable<any>{
    return this.http.get<any>(`${this.baseURL}/document-counts`)
   }
   getApprovedCount():Observable<any>{
    return this.http.get<any>(`${this.baseURL}/operator-counts`)
   }
   getPendingCount():Observable<any>{
    return this.http.get<any>(`${this.baseURL}/operator-counts`)
   }
   getRejectCount():Observable<any>{
    return this.http.get<any>(`${this.baseURL}/operator-counts`)
   }

   getStatusCount():Observable<StatusCount[]>{
        return this.http.get<StatusCount[]>(`${this.baseURL}/status-count`)

   }

   getApprovedEmployeeNames():Observable<any>{
    return this.http.get<any>(`http://localhost:8082/employee-status/approved/names`)
   }

}
