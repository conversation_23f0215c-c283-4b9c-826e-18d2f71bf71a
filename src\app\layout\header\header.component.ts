import { ChangeDetectorRef, Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { LoginService } from '../../services/auth/login.service';

@Component({
  selector: 'app-header',
  imports: [CommonModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {

    // imagePath = 'assets/logo_tncsc.png';

     userName: string = '';
  entityType: string | null = null
  isLiked: boolean;
  isEmployeeLogin: boolean = false;

  constructor(private authService:LoginService, private cdr: ChangeDetectorRef,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.getUserName();
    // this.getType();

    // Listen for storage changes to update header when login state changes
    window.addEventListener('storage', (e) => {
      if (e.key === 'employeeName' || e.key === 'token') {
        this.getUserName();
      }
    });
  }

  getUserName(){
    // Check if this is an employee login
    const employeeName = localStorage.getItem('employeeName');
    const token = localStorage.getItem('token');

    if (employeeName && token === 'employee_session') {
      // Employee login - display employee name
      this.userName = employeeName;
      this.isEmployeeLogin = true;
      console.log('Employee login detected, displaying employee name:', this.userName);
    } else {
      // Admin/Operator login - display username from auth service
      this.userName = this.authService.getUserName;
      this.isEmployeeLogin = false;
      console.log('Admin/Operator login detected, displaying username:', this.userName);
    }

    this.cdr.detectChanges();  // Manually trigger change detection
  }

  // Method to refresh header information (can be called from other components)
  refreshHeader(): void {
    this.getUserName();
  }

  logout(){
    console.log('Logout initiated');
    this.authService.logout();
    sessionStorage.clear();
    localStorage.clear();
    // Small delay to ensure session is cleared before navigation
    setTimeout(() => {
      console.log('Session cleared, navigating to login');
      this.router.navigate(['/login']).then(
        (success) => {
          console.log('Logout navigation success:', success);
          if (!success) {
            console.error('Logout navigation failed');
            // Force navigation using location
            window.location.href = '/login';
          }
        },
        (error) => {
          console.error('Logout navigation error:', error);
          // Force navigation using location
          window.location.href = '/login';
        }
      );
    }, 100);
  }
}
