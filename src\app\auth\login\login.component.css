    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: Arial, sans-serif;
    }

    .wrapper {
      display: flex;
      height: 100vh;
      width: 100%;
    }

    .left-panel {
      flex: 2;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: white;
    }

    .left-panel img {
      max-width: 90%;
      width: 800px;
      height: auto;
      object-fit: contain;
    }

    .right-panel {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f8f9fa;
    }

    .right-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .heading {
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }

    .user-type-toggle {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .btn-toggle {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      background: #e0e0e0;
      cursor: pointer;
      transition: background 0.3s, color 0.3s;
    }

    .btn-toggle.active {
      background: #007bff;
      color: white;
    }

    .login-form {
      width: 100%;
      max-width: 350px;
      padding: 30px;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    }

    .field {
      margin-bottom: 20px;
    }

    .password-toggle {
      position: absolute;
      right: 10px;
      top: 38px;
      cursor: pointer;
      font-size: 1.2em;
      color: #6c757d;
    }

    .position-relative {
      position: relative;
    }

    /* Flip Transition Styles */
    .flip-container {
      perspective: 1000px;
    }

    .flipper {
      transition: 0.6s;
      transform-style: preserve-3d;
      position: relative;
      width: 100%;
      height: 300px;
    }

    .form-front, .form-back {
      backface-visibility: hidden;
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .form-back {
      transform: rotateY(180deg);
    }

    .flip-container.flipped .flipper {
      transform: rotateY(180deg);
    }
