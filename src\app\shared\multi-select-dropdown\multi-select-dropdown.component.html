<div class="multi-select-container">
  <label class="form-label" *ngIf="label">{{ label }}</label>

  <div class="multi-select-dropdown" (click)="toggleDropdown()" [class.open]="isDropdownOpen">
    <div class="multi-select-trigger" #triggerElement>
      <span class="multi-select-text">{{ getDisplayText() }}</span>
      <i class="bi bi-chevron-down multi-select-arrow" [class.rotated]="isDropdownOpen"></i>
    </div>
  </div>

  <!-- Backdrop to close dropdown when clicking outside -->
  <div class="multi-select-backdrop" *ngIf="isDropdownOpen" (click)="closeDropdown()"></div>
</div>
